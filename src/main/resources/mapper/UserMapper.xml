<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cet.electric.quickstart.dao.UserDao">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cet.electric.quickstart.entity.User">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        user_id, username, email, phone, status, created_at, updated_at, is_deleted
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where_Clause">
        <where>
            is_deleted = 0
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (username LIKE '%' || #{keyword} || '%'
                     OR email LIKE '%' || #{keyword} || '%')
            </if>
            <if test="startTime != null">
                AND created_at >= #{startTime}
            </if>
            <if test="endTime != null">
                AND created_at &lt;= #{endTime}
            </if>
        </where>
    </sql>

    <!-- 创建用户 -->
    <insert id="createUser" parameterType="com.scaffold.entity.User" useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO users (username, email, phone, status, created_at, updated_at, is_deleted)
        VALUES (#{username}, #{email}, #{phone}, #{status}, #{createdAt}, #{updatedAt}, #{isDeleted})
    </insert>

    <!-- 分页查询用户 -->
    <select id="getUsers" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        <include refid="Query_Where_Clause"/>
        <choose>
            <when test="sortBy != null and sortBy != ''">
                ORDER BY ${sortBy}
                <if test="sortOrder != null and sortOrder != ''">
                    ${sortOrder}
                </if>
            </when>
            <otherwise>
                ORDER BY created_at DESC
            </otherwise>
        </choose>
        LIMIT #{size} OFFSET #{offset}
    </select>

    <!-- 查询用户总数 -->
    <select id="countUsers" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM users
        <include refid="Query_Where_Clause"/>
    </select>

    <!-- 根据用户ID查询 -->
    <select id="findByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE user_id = #{userId} AND is_deleted = 0
    </select>

    <!-- 根据用户名查询 -->
    <select id="findByUsername" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE username = #{username} AND is_deleted = 0
    </select>

    <!-- 更新用户 -->
    <update id="updateUser" parameterType="com.scaffold.entity.User">
        UPDATE users
        SET username = #{username},
            email = #{email},
            phone = #{phone},
            status = #{status},
            updated_at = #{updatedAt}
        WHERE user_id = #{userId} AND is_deleted = 0
    </update>

    <!-- 软删除用户 -->
    <update id="deleteUser" parameterType="java.lang.Long">
        UPDATE users
        SET is_deleted = 1,
            updated_at = EXTRACT(EPOCH FROM NOW()) * 1000
        WHERE user_id = #{userId} AND is_deleted = 0
    </update>
</mapper>
