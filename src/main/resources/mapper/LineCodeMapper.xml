<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cet.electric.ngapserver.dao.LineCodeDao">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cet.electric.ngapserver.entity.LineCode">
        <id column="line_code_id" property="lineCodeId" jdbcType="BIGINT"/>
        <result column="line_code" property="lineCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        line_code_id, line_code
    </sql>

    <!-- 插入新线路代码 -->
    <insert id="createLineCode" parameterType="com.cet.electric.ngapserver.entity.LineCode" useGeneratedKeys="true" keyProperty="lineCodeId">
        INSERT INTO line_code (
            line_code
        ) VALUES (
                     #{lineCode}
                 )
    </insert>

    <sql id="LineCode_Where_Clause">
        <where>
            <if test="keyword != null and keyword != ''">
                AND line_code LIKE '%' || #{keyword} || '%'
            </if>
        </where>
    </sql>

    <select id="getLineCodes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM line_code
        <include refid="LineCode_Where_Clause"/>
        ORDER BY
        <choose>
            <when test="sortBy == 'lineCode'">line_code</when>
            <otherwise>line_code_id</otherwise>
        </choose>
        <choose>
            <when test="sortOrder.equalsIgnoreCase('asc')">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
        LIMIT #{offset}, #{size}
    </select>

    <select id="countLineCodes" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM line_code
        <include refid="LineCode_Where_Clause"/>
    </select>

    <select id="findByLineCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List" />
        FROM line_code
        WHERE line_code = #{lineCode}
        LIMIT 1
    </select>

    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM line_code
        WHERE line_code_id = #{lineCodeId}
        LIMIT 1
    </select>

    <update id="updateLineCode" parameterType="com.cet.electric.ngapserver.entity.LineCode">
        UPDATE line_code
        SET
            line_code = #{lineCode}
        WHERE
            line_code_id = #{lineCodeId}
    </update>

    <delete id="deleteLineCode" parameterType="java.lang.Long">
        DELETE FROM line_code
        WHERE line_code_id = #{lineCodeId}
    </delete>
</mapper>
