server:
  port: 28080

mybatis:
  mapper-locations: classpath:mapper/*Mapper.xml

spring:
  profiles:
    active: sqlite
  datasource:
    url: ${DATABASE_URL:**********************************}
    driver-class-name: org.sqlite.JDBC
    username: ${DATABASE_USERNAME:}
    password: ${DATABASE_PASSWORD:}
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# 应用配置
app:
  name: Java Scaffold
  version: 1.0.0

---
# SQLite配置
spring:
  profiles: sqlite
  datasource:
    url: ${DATABASE_URL:**********************************}
    driver-class-name: org.sqlite.JDBC
    username:
    password:

---
# PostgreSQL配置
spring:
  profiles: postgresql
  datasource:
    url: ${DATABASE_URL:*****************************************}
    driver-class-name: org.postgresql.Driver
    username: ${DATABASE_USERNAME:scaffold}
    password: ${DATABASE_PASSWORD:scaffold123}
