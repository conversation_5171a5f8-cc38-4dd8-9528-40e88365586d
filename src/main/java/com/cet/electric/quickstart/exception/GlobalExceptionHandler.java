package com.cet.electric.quickstart.exception;

import javax.servlet.http.HttpServletRequest;

import com.cet.electric.quickstart.dto.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 全局异常处理器
 * 基于ngap-server项目的GlobalExceptionHandler
 * 
 * <AUTHOR> Generator
 */
@ControllerAdvice
@ResponseBody
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(value = Exception.class)
    public ResponseDTO<?> exceptionHandler(HttpServletRequest request, Exception exception) {
        log.error(ErrorMsg.getStackTraceStr(exception));
        if (exception instanceof ErrorMsg) {
            ErrorMsg error = (ErrorMsg) exception;
            return new ResponseDTO<Void>(error.getCode(), error.getMessage(), null);
        }
        return new ResponseDTO<Void>(-1, "未知异常", null);
    }
}
