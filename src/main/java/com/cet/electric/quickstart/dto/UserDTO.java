package com.cet.electric.quickstart.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户数据传输对象
 * 基于ngap-server项目的DTO设计模式
 * 
 * <AUTHOR> Generator
 */
@Data
@ApiModel(description = "用户信息")
public class UserDTO {
    
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "用户名", example = "admin", required = true)
    private String username;

    @ApiModelProperty(value = "邮箱地址", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "手机号码", example = "13800138000")
    private String phone;

    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "1735660800000")
    private Long createdAt;

    @ApiModelProperty(value = "更新时间", example = "1735660800000")
    private Long updatedAt;
}
