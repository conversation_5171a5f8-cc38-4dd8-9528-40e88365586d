package com.cet.electric.quickstart.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用响应数据传输对象
 * 基于ngap-server项目的ResponseDTO
 * 
 * <AUTHOR> Generator
 */
@Data
@NoArgsConstructor
public class ResponseDTO<T> {
    private int code;
    private String msg;
    private T data;
    private Long total;
    
    @Override
    public String toString() {
        return "ResponseDTO [code=" + code + ", msg=" + msg + ", data=" + data + "]";
    }
    
    public ResponseDTO(int code, String msg, T data) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
    
    public ResponseDTO(int code, String msg, T data, Long total) {
        super();
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.total = total;
    }
}
