package com.cet.electric.quickstart.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户查询参数DTO
 * 基于ngap-server项目的查询DTO设计模式
 * 
 * <AUTHOR> Generator
 */
@Data
@ApiModel(description = "用户查询参数")
public class UserQueryDTO {
    
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size = 10;

    @ApiModelProperty(value = "排序字段", example = "created_at")
    private String sortBy = "created_at";

    @ApiModelProperty(value = "排序方式", example = "desc")
    private String sortOrder = "desc";

    @ApiModelProperty(value = "用户状态", required = false)
    private Integer status;

    @ApiModelProperty(value = "关键字搜索（用户名或邮箱）", required = false)
    private String keyword;

    @ApiModelProperty(value = "起始时间(时间戳-毫秒)", example = "1735660800000")
    private Long startTime;

    @ApiModelProperty(value = "结束时间(时间戳-毫秒)", example = "1735747200000")
    private Long endTime;

    @Override
    public String toString() {
        return "UserQueryDTO [page=" + page + ", size=" + size + ", sortBy=" + sortBy +
                ", sortOrder=" + sortOrder + ", status=" + status + ", keyword=" + keyword +
                ", startTime=" + startTime + ", endTime=" + endTime + "]";
    }
}
