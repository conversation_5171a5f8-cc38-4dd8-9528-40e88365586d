package com.cet.electric.quickstart.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户实体类
 * 基于ngap-server项目的实体设计模式
 * 
 * <AUTHOR> Generator
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {
    /**
     * 用户ID：主键，自增
     */
    private Long userId;

    /**
     * 用户名：非空，唯一
     */
    private String username;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间：时间戳（毫秒）
     */
    private Long createdAt;

    /**
     * 修改时间：时间戳（毫秒）
     */
    private Long updatedAt;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
}
