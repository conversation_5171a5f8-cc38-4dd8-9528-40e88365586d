package com.cet.electric.quickstart.dao;

import com.cet.electric.quickstart.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户数据访问层
 * 基于ngap-server项目的DAO设计模式
 * 
 * <AUTHOR> Generator
 */
@Mapper
@Component
public interface UserDao {

    /**
     * 创建新用户
     *
     * @param user 用户实体
     * @return 受影响的行数
     */
    int createUser(User user);

    /**
     * 分页查询用户
     *
     * @param offset    偏移量
     * @param size      每页大小
     * @param sortBy    排序字段
     * @param sortOrder 排序方向
     * @param status    用户状态(可选)
     * @param keyword   搜索关键词(可选)
     * @param startTime 起始时间(时间戳-毫秒，可选)
     * @param endTime   结束时间(时间戳-毫秒，可选)
     * @return 用户列表
     */
    List<User> getUsers(@Param("offset") int offset,
                        @Param("size") int size,
                        @Param("sortBy") String sortBy,
                        @Param("sortOrder") String sortOrder,
                        @Param("status") Integer status,
                        @Param("keyword") String keyword,
                        @Param("startTime") Long startTime,
                        @Param("endTime") Long endTime);

    /**
     * 查询用户总数
     *
     * @param status    用户状态(可选)
     * @param keyword   搜索关键词(可选)
     * @param startTime 起始时间(时间戳-毫秒，可选)
     * @param endTime   结束时间(时间戳-毫秒，可选)
     * @return 总数
     */
    Long countUsers(@Param("status") Integer status,
                    @Param("keyword") String keyword,
                    @Param("startTime") Long startTime,
                    @Param("endTime") Long endTime);

    /**
     * 根据用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户实体，如果不存在返回null
     */
    User findByUserId(Long userId);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户实体，如果不存在返回null
     */
    User findByUsername(String username);

    /**
     * 更新用户信息
     *
     * @param user 用户实体
     * @return 受影响的行数
     */
    int updateUser(User user);

    /**
     * 软删除用户
     *
     * @param userId 用户ID
     * @return 受影响的行数
     */
    int deleteUser(Long userId);
}
