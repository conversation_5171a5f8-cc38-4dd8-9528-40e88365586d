package com.cet.electric.ngapserver.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.ResponseErrorHandler;

import java.io.IOException;

/**
 * DSS服务响应错误处理器
 * 
 * 用于处理DSS服务返回的4xx和5xx错误，不抛出异常，而是让RestTemplate正常返回响应
 * 这样可以在业务层统一处理成功和失败的情况
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Slf4j
public class DssResponseErrorHandler implements ResponseErrorHandler {

    /**
     * 判断响应是否有错误
     * 
     * @param response HTTP响应
     * @return 是否有错误
     * @throws IOException IO异常
     */
    @Override
    public boolean hasError(ClientHttpResponse response) throws IOException {
        HttpStatus statusCode = response.getStatusCode();
        
        // 记录响应状态码
        log.debug("DSS服务响应状态码: {}", statusCode);
        
        // 对于4xx和5xx错误，我们认为有错误，但不会抛出异常
        // 而是让RestTemplate正常返回ResponseEntity，由业务层处理
        return statusCode.is4xxClientError() || statusCode.is5xxServerError();
    }

    /**
     * 处理错误响应
     * 
     * 对于DSS服务的错误响应，我们不抛出异常，而是让RestTemplate正常返回
     * 这样业务层可以通过检查ResponseEntity的状态码来判断是否成功
     * 
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    @Override
    public void handleError(ClientHttpResponse response) throws IOException {
        HttpStatus statusCode = response.getStatusCode();
        
        // 记录错误信息，但不抛出异常
        log.warn("DSS服务返回错误响应，状态码: {}, 状态文本: {}", 
                statusCode, response.getStatusText());
        
        // 不抛出异常，让RestTemplate正常返回ResponseEntity
        // 业务层可以通过response.getStatusCode()来判断是否成功
    }
}
