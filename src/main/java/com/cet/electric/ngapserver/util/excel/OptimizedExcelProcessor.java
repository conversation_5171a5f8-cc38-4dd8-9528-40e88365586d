package com.cet.electric.ngapserver.util.excel;

import com.cet.electric.ngapserver.entity.DataPoint;
import com.cet.electric.ngapserver.entity.Metric;
import com.cet.electric.ngapserver.entity.MetricData;
import com.cet.electric.ngapserver.entity.MetricStatistics;
import com.cet.electric.ngapserver.util.voltage.VoltageObjectPool;
import com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 优化的Excel处理器
 * 专门用于高性能的指标提取和数据获取
 * 
 * <AUTHOR>
 */
public class OptimizedExcelProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(OptimizedExcelProcessor.class);
    
    // 缓存管理器
    private final ExcelCacheManager cacheManager;
    
    // 对象池
    private final VoltageObjectPool objectPool;
    
    // 性能监控器
    private final VoltagePerformanceMonitor performanceMonitor;
    
    // 日期格式化器缓存
    private static final List<DateTimeFormatter> DATE_FORMATTERS = Arrays.asList(
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd"),
        DateTimeFormatter.ofPattern("MM/dd/yyyy"),
        DateTimeFormatter.ofPattern("dd/MM/yyyy")
    );
    
    public OptimizedExcelProcessor() {
        this.cacheManager = ExcelCacheManager.getInstance();
        this.objectPool = VoltageObjectPool.getInstance();
        this.performanceMonitor = VoltagePerformanceMonitor.getInstance();
    }
    
    /**
     * 优化的指标提取方法
     */
    public List<Metric> extractMetricsOptimized(String filePath) {
        try (VoltagePerformanceMonitor.PerformanceTimer timer = 
             performanceMonitor.startOperation("extractMetricsOptimized")) {
            
            // 检查缓存
            List<Metric> cachedMetrics = ExcelCacheManager.CacheOperations.getMetrics(filePath);
            if (cachedMetrics != null) {
                log.debug("从缓存获取指标数据: {}", filePath);
                return cachedMetrics;
            }
            
            List<Metric> rootMetrics = new ArrayList<>();
            Metric rootMetric = createRootMetric();
            rootMetrics.add(rootMetric);
            
            try {
                // 增加内存限制
                IOUtils.setByteArrayMaxOverride(500_000_000);
                
                try (FileInputStream fis = new FileInputStream(filePath);
                     Workbook workbook = WorkbookFactory.create(fis)) {
                    
                    // 并行处理工作表（如果工作表数量多）
                    if (workbook.getNumberOfSheets() > 3) {
                        processSheetsConcurrently(workbook, rootMetric);
                    } else {
                        for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                            processSheetOptimized(workbook.getSheetAt(sheetIndex), rootMetric);
                        }
                    }
                    
                    // 缓存结果
                    ExcelCacheManager.CacheOperations.cacheMetrics(filePath, rootMetrics);
                    
                    log.info("指标提取完成: 文件={}, 耗时={}ms, 指标数={}", 
                            filePath, timer.getDuration(), countTotalMetrics(rootMetrics));
                    
                    return rootMetrics;
                }
            } catch (Exception e) {
                log.error("提取指标失败: {}", filePath, e);
                return new ArrayList<>();
            }
        }
    }
    
    /**
     * 优化的指标数据获取方法
     */
    public MetricData getMetricDataOptimized(String filePath, Metric metric) {
        try (VoltagePerformanceMonitor.PerformanceTimer timer = 
             performanceMonitor.startOperation("getMetricDataOptimized")) {
            
            log.info("开始获取指标数据, 指标ID: {}", metric.getMetricId());
            
            // 检查缓存
            MetricData cachedData = ExcelCacheManager.CacheOperations.getMetricData(filePath, metric.getMetricId());
            if (cachedData != null) {
                log.debug("从缓存获取指标数据: {}", metric.getMetricId());
                return cachedData;
            }
            
            try {
                // 增加内存限制
                IOUtils.setByteArrayMaxOverride(500_000_000);
                
                try (FileInputStream fis = new FileInputStream(filePath);
                     Workbook workbook = WorkbookFactory.create(fis)) {
                    
                    String metricId = metric.getMetricId();
                    String[] parts = metricId.split("\\.");
                    if (parts.length < 1) {
                        log.error("指标ID格式不正确: {}", metricId);
                        return null;
                    }
                    
                    Sheet sheet = workbook.getSheet(parts[0]);
                    if (sheet == null) {
                        log.error("找不到指定的sheet: {}", parts[0]);
                        return null;
                    }
                    
                    // 使用缓存的列索引
                    ColumnAnalysisResult columnAnalysis = analyzeColumnsWithCache(sheet, filePath);
                    
                    if (columnAnalysis.dateColumnIndex == -1) {
                        log.error("找不到日期列");
                        return null;
                    }
                    
                    // 优化的数据点提取
                    List<DataPoint> dataPoints = extractDataPointsOptimized(
                        sheet, columnAnalysis, parts);
                    
                    // 优化的统计计算
                    MetricStatistics statistics = calculateStatisticsOptimized(dataPoints, metric);
                    
                    MetricData result = MetricData.builder()
                            .metric(metric)
                            .xUnit(determineUnit(metric, columnAnalysis.uColumns))
                            .dataPoints(dataPoints)
                            .statistics(statistics)
                            .build();
                    
                    // 缓存结果
                    ExcelCacheManager.CacheOperations.cacheMetricData(filePath, metric.getMetricId(), result);
                    
                    log.info("指标数据获取完成: 指标ID={}, 耗时={}ms, 数据点数={}", 
                            metric.getMetricId(), timer.getDuration(), dataPoints.size());
                    
                    return result;
                }
            } catch (Exception e) {
                log.error("获取指标数据失败: {}", e.getMessage(), e);
                return null;
            }
        }
    }
    
    /**
     * 并发处理工作表
     */
    private void processSheetsConcurrently(Workbook workbook, Metric rootMetric) {
        List<Sheet> sheets = new ArrayList<>();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            sheets.add(workbook.getSheetAt(i));
        }
        
        // 使用并行流处理工作表
        List<Metric> sheetMetrics = sheets.parallelStream()
            .map(sheet -> {
                Metric sheetMetric = createSheetMetric(sheet.getSheetName());
                processSheetOptimized(sheet, sheetMetric);
                return sheetMetric;
            })
            .collect(Collectors.toList());
        
        // 合并结果
        rootMetric.getChildren().addAll(sheetMetrics);
    }
    
    /**
     * 优化的工作表处理
     */
    private void processSheetOptimized(Sheet sheet, Metric parentMetric) {
        if (sheet == null) return;
        
        String sheetName = sheet.getSheetName();
        Metric sheetMetric = parentMetric;
        
        // 如果父指标不是工作表指标，创建新的工作表指标
        if (!parentMetric.getMetricName().equals(sheetName)) {
            sheetMetric = createSheetMetric(sheetName);
            parentMetric.getChildren().add(sheetMetric);
        }
        
        // 使用对象池获取临时对象
        Map<String, Integer> columnIndexMap = objectPool.borrowStringBuilder() != null ? 
            new HashMap<>() : new HashMap<>();
        
        try {
            List<String> metricColumns = analyzeHeaderOptimized(sheet.getRow(0), columnIndexMap);
            
            // 批量处理数据行
            int batchSize = 1000;
            int lastRowNum = sheet.getLastRowNum();
            
            for (int startRow = 1; startRow <= lastRowNum; startRow += batchSize) {
                int endRow = Math.min(startRow + batchSize, lastRowNum + 1);
                processRowBatch(sheet, startRow, endRow, metricColumns, columnIndexMap, sheetMetric);
            }
            
            cleanupEmptyChildren(sheetMetric);
            
        } finally {
            // 这里可以归还对象到池中（如果使用了对象池的对象）
        }
    }
    
    /**
     * 批量处理数据行
     */
    private void processRowBatch(Sheet sheet, int startRow, int endRow, 
                                List<String> metricColumns, Map<String, Integer> columnIndexMap, 
                                Metric parentMetric) {
        for (int rowNum = startRow; rowNum < endRow; rowNum++) {
            Row dataRow = sheet.getRow(rowNum);
            if (dataRow != null) {
                processDataRowOptimized(dataRow, metricColumns, columnIndexMap, parentMetric);
            }
        }
    }
    
    /**
     * 优化的数据行处理
     */
    private void processDataRowOptimized(Row dataRow, List<String> metricColumns, 
                                       Map<String, Integer> columnIndexMap, Metric currentParent) {
        StringBuilder idBuilder = objectPool.borrowStringBuilder();
        if (idBuilder == null) {
            idBuilder = new StringBuilder();
        }
        
        try {
            idBuilder.setLength(0);
            idBuilder.append(currentParent.getMetricId());
            
            for (String columnName : metricColumns) {
                Integer columnIndex = columnIndexMap.get(columnName);
                if (columnIndex == null) continue;
                
                String cellValue = getCellValueAsStringOptimized(dataRow.getCell(columnIndex));
                if (cellValue.isEmpty()) continue;
                
                String metricName = columnName + "-" + cellValue;
                idBuilder.append(".").append(metricName);
                String metricId = idBuilder.toString();
                
                Metric existingMetric = findChildByMetricName(currentParent, metricName);
                currentParent = (existingMetric != null) ? existingMetric : 
                    createAndAddNewMetric(currentParent, metricId, metricName);
            }
        } finally {
            objectPool.returnStringBuilder(idBuilder);
        }
    }
    
    // 辅助方法（简化版本，完整实现需要更多代码）
    private Metric createRootMetric() {
        return Metric.builder()
                .metricId("measuredDataMetric")
                .metricName("实测数据指标")
                .children(new ArrayList<>())
                .build();
    }
    
    private Metric createSheetMetric(String sheetName) {
        return Metric.builder()
                .metricId(sheetName)
                .metricName(sheetName)
                .children(new ArrayList<>())
                .build();
    }
    
    private List<String> analyzeHeaderOptimized(Row headerRow, Map<String, Integer> columnIndexMap) {
        List<String> metricColumns = new ArrayList<>();
        if (headerRow == null) return metricColumns;
        
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell == null) continue;
            
            String columnName = cell.getStringCellValue().trim();
            columnIndexMap.put(columnName, i);
            
            if (isMetricColumn(columnName)) {
                metricColumns.add(columnName);
            }
        }
        return metricColumns;
    }
    
    private boolean isMetricColumn(String columnName) {
        return !"日期".equals(columnName) &&
                !(columnName.startsWith("p") && isNumeric(columnName.substring(1))) &&
                !(columnName.startsWith("u") && isNumeric(columnName.substring(1)));
    }
    
    private boolean isNumeric(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    // 其他辅助方法的简化实现...
    private String getCellValueAsStringOptimized(Cell cell) {
        if (cell == null) return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return getNumericCellValueOptimized(cell);
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 获取单元格的数值并转换为字符串（与传统方式保持一致）
     *
     * @param cell 单元格对象
     * @return 数值的字符串表示
     */
    private String getNumericCellValueOptimized(Cell cell) {
        try {
            if (cell.getCellType() == CellType.NUMERIC) {
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                }
                double value = cell.getNumericCellValue();
                long longValue = (long) value;
                return value == longValue ? String.valueOf(longValue) : String.valueOf(value);
            } else if (cell.getCellType() == CellType.STRING) {
                // 如果是字符串类型，直接返回字符串值
                return cell.getStringCellValue().trim();
            } else {
                log.debug("不支持的单元格类型: {}", cell.getCellType());
                return "";
            }
        } catch (Exception e) {
            log.debug("无法获取单元格数值，返回空字符串: {}", e.getMessage());
            return "";
        }
    }
    
    private Metric findChildByMetricName(Metric parent, String metricName) {
        if (parent.getChildren() == null) return null;
        
        return parent.getChildren().stream()
                .filter(child -> metricName.equals(child.getMetricName()))
                .findFirst()
                .orElse(null);
    }
    
    private Metric createAndAddNewMetric(Metric parent, String metricId, String metricName) {
        Metric newMetric = Metric.builder()
                .metricId(metricId)
                .metricName(metricName)
                .children(new ArrayList<>())
                .build();
        
        if (parent.getChildren() == null) {
            parent.setChildren(new ArrayList<>());
        }
        parent.getChildren().add(newMetric);
        
        return newMetric;
    }
    
    private void cleanupEmptyChildren(Metric metric) {
        if (metric.getChildren() != null) {
            if (metric.getChildren().isEmpty()) {
                metric.setChildren(null);
            } else {
                for (Metric child : metric.getChildren()) {
                    cleanupEmptyChildren(child);
                }
            }
        }
    }
    
    private int countTotalMetrics(List<Metric> metrics) {
        return metrics.stream()
                .mapToInt(this::countMetricsRecursive)
                .sum();
    }
    
    private int countMetricsRecursive(Metric metric) {
        int count = 1;
        if (metric.getChildren() != null) {
            count += metric.getChildren().stream()
                    .mapToInt(this::countMetricsRecursive)
                    .sum();
        }
        return count;
    }
    
    /**
     * 使用缓存的列分析（增强异常处理）
     */
    private ColumnAnalysisResult analyzeColumnsWithCache(Sheet sheet, String filePath) {
        String cacheKey = ExcelCacheManager.getColumnIndexKey(filePath, sheet.getSheetName());

        try {
            ColumnAnalysisResult cached = cacheManager.get(cacheKey, ColumnAnalysisResult.class);
            if (cached != null) {
                log.debug("从缓存获取列分析结果: {}", sheet.getSheetName());
                return cached;
            }
        } catch (Exception e) {
            log.warn("获取缓存列分析结果失败，将重新计算: {}", e.getMessage());
            // 清理可能损坏的缓存
            cacheManager.remove(cacheKey);
            // 同时清理旧版本的缓存键
            String oldCacheKey = ExcelCacheManager.getOldColumnIndexKey(filePath, sheet.getSheetName());
            cacheManager.remove(oldCacheKey);
        }

        // 重新计算列分析结果
        ColumnAnalysisResult result = new ColumnAnalysisResult();
        Row headerRow = sheet.getRow(0);

        if (headerRow != null) {
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell == null) continue;

                String columnName = cell.getStringCellValue().trim();
                result.columnIndexMap.put(columnName, i);

                if ("日期".equals(columnName)) {
                    result.dateColumnIndex = i;
                } else if (columnName.startsWith("p") && isNumeric(columnName.substring(1))) {
                    result.pColumns.add(columnName);
                } else if (columnName.startsWith("u") && isNumeric(columnName.substring(1))) {
                    result.uColumns.add(columnName);
                }
            }
        }

        // 缓存结果（使用新的缓存键）
        try {
            cacheManager.put(cacheKey, result);
            log.debug("列分析结果已缓存: {}", sheet.getSheetName());
        } catch (Exception e) {
            log.warn("缓存列分析结果失败: {}", e.getMessage());
        }

        return result;
    }

    /**
     * 优化的数据点提取
     */
    private List<DataPoint> extractDataPointsOptimized(Sheet sheet, ColumnAnalysisResult columnAnalysis, String[] parts) {
        List<DataPoint> dataPoints = new ArrayList<>();
        Set<LocalDate> processedDates = new HashSet<>();

        // 构建路径匹配条件
        Map<String, String> pathCriteria = buildPathCriteria(parts);

        // 批量处理数据行
        int batchSize = 500;
        int lastRowNum = sheet.getLastRowNum();

        for (int startRow = 1; startRow <= lastRowNum; startRow += batchSize) {
            int endRow = Math.min(startRow + batchSize, lastRowNum + 1);

            for (int rowNum = startRow; rowNum < endRow; rowNum++) {
                Row dataRow = sheet.getRow(rowNum);
                if (dataRow == null) continue;

                if (matchesPathCriteria(dataRow, columnAnalysis.columnIndexMap, pathCriteria)) {
                    String dateStr = getCellValueAsStringOptimized(dataRow.getCell(columnAnalysis.dateColumnIndex));
                    if (dateStr.isEmpty()) continue;

                    LocalDate date = parseDate(dateStr);
                    if (date != null && processedDates.add(date)) {
                        // 处理p列和u列数据
                        processDataPointsBatch(dataRow, columnAnalysis.pColumns, date,
                                             columnAnalysis.columnIndexMap, dataPoints);
                        processDataPointsBatch(dataRow, columnAnalysis.uColumns, date,
                                             columnAnalysis.columnIndexMap, dataPoints);
                    }
                }
            }
        }

        // 按时间戳排序数据点
        dataPoints.sort((dp1, dp2) -> {
            try {
                // 解析时间戳进行比较
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                LocalDateTime time1 = LocalDateTime.parse(dp1.getTimestamp(), formatter);
                LocalDateTime time2 = LocalDateTime.parse(dp2.getTimestamp(), formatter);
                return time1.compareTo(time2);
            } catch (Exception e) {
                // 如果时间解析失败，按字符串排序
                log.debug("时间戳解析失败，使用字符串排序: {}, {}", dp1.getTimestamp(), dp2.getTimestamp());
                return dp1.getTimestamp().compareTo(dp2.getTimestamp());
            }
        });

        log.debug("数据点按时间排序完成，共 {} 个数据点", dataPoints.size());
        return dataPoints;
    }

    /**
     * 批量处理数据点（与传统方式保持一致的时间戳计算）
     */
    private void processDataPointsBatch(Row dataRow, List<String> columns, LocalDate date,
                                       Map<String, Integer> columnIndexMap, List<DataPoint> dataPoints) {
        for (String columnName : columns) {
            Integer columnIndex = columnIndexMap.get(columnName);
            if (columnIndex == null) continue;

            Cell cell = dataRow.getCell(columnIndex);
            if (cell == null) continue;

            try {
                double value;

                // 根据单元格类型获取数值
                if (cell.getCellType() == CellType.NUMERIC) {
                    value = cell.getNumericCellValue();
                } else if (cell.getCellType() == CellType.STRING) {
                    // 尝试将字符串转换为数值
                    String stringValue = cell.getStringCellValue().trim();
                    if (stringValue.isEmpty()) {
                        continue; // 跳过空字符串
                    }
                    value = Double.parseDouble(stringValue);
                } else {
                    // 其他类型（如公式、布尔值等）跳过
                    log.debug("跳过不支持的单元格类型: 列名={}, 类型={}", columnName, cell.getCellType());
                    continue;
                }

                // 解析列索引（如 p1, p2, u1, u2 等）
                int index = parseColumnIndex(columnName);
                if (index == -1) continue;

                // 计算具体的时间点（与传统方式保持一致）
                LocalDateTime dateTime = date.atStartOfDay().plusMinutes((index - 1) * 15L);
                String formattedDateTime = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

                DataPoint dataPoint = DataPoint.builder()
                        .timestamp(formattedDateTime)
                        .value(value)
                        .build();
                dataPoints.add(dataPoint);
            } catch (NumberFormatException e) {
                // 字符串无法转换为数值
                log.debug("字符串无法转换为数值: 列名={}, 值='{}', 异常信息={}",
                    columnName,
                    cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : "N/A",
                    e.getMessage());
            } catch (Exception e) {
                // 记录其他异常的详细信息
                log.debug("处理数据点时发生异常: 列名={}, 单元格类型={}, 异常信息={}",
                    columnName,
                    cell != null ? cell.getCellType() : "null",
                    e.getMessage());
            }
        }
    }

    /**
     * 解析列名中的索引（与传统方式保持一致）
     *
     * @param columnName 列名（如 p1, p2, u1, u2）
     * @return 列索引，如果解析失败返回-1
     */
    private int parseColumnIndex(String columnName) {
        try {
            return Integer.parseInt(columnName.substring(1));
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * 优化的统计计算
     */
    private MetricStatistics calculateStatisticsOptimized(List<DataPoint> dataPoints, Metric metric) {
        if (dataPoints.isEmpty()) return null;

        // 使用并行流进行统计计算（如果数据量大）
        if (dataPoints.size() > 1000) {
            return calculateStatisticsParallel(dataPoints, metric);
        } else {
            return calculateStatisticsSequential(dataPoints, metric);
        }
    }

    /**
     * 并行统计计算
     */
    private MetricStatistics calculateStatisticsParallel(List<DataPoint> dataPoints, Metric metric) {
        if (metric.getMetricId().contains("电压") || metric.getMetricId().contains("V")) {
            long qualifiedCount = dataPoints.parallelStream()
                    .filter(point -> point.getValue() != null)
                    .mapToDouble(DataPoint::getValue)
                    .filter(value -> value >= 198.0 && value <= 242.0)
                    .count();

            long totalCount = dataPoints.parallelStream()
                    .filter(point -> point.getValue() != null)
                    .count();

            return MetricStatistics.builder()
                    .min(198.0)
                    .max(242.0)
                    .rate(totalCount > 0 ? (double) qualifiedCount / totalCount * 100 : 0.0)
                    .build();
        }

        return null;
    }

    /**
     * 顺序统计计算
     */
    private MetricStatistics calculateStatisticsSequential(List<DataPoint> dataPoints, Metric metric) {
        if (metric.getMetricId().contains("电压") || metric.getMetricId().contains("V")) {
            int qualifiedCount = 0;
            int totalCount = 0;

            for (DataPoint point : dataPoints) {
                if (point.getValue() != null) {
                    totalCount++;
                    if (point.getValue() >= 198.0 && point.getValue() <= 242.0) {
                        qualifiedCount++;
                    }
                }
            }

            return MetricStatistics.builder()
                    .min(198.0)
                    .max(242.0)
                    .rate(totalCount > 0 ? (double) qualifiedCount / totalCount * 100 : 0.0)
                    .build();
        }

        return null;
    }

    /**
     * 确定指标单位
     */
    private String determineUnit(Metric metric, List<String> uColumns) {
        if (metric.getMetricId().contains("电压") || !uColumns.isEmpty()) {
            return "V";
        } else if (metric.getMetricId().contains("功率")) {
            return "W";
        }
        return "";
    }

    /**
     * 构建路径匹配条件
     */
    private Map<String, String> buildPathCriteria(String[] parts) {
        Map<String, String> criteria = new HashMap<>();

        for (int i = 1; i < parts.length; i++) {
            String part = parts[i];
            if (part.contains("-")) {
                String[] keyValue = part.split("-", 2);
                if (keyValue.length == 2) {
                    criteria.put(keyValue[0], keyValue[1]);
                }
            }
        }

        return criteria;
    }

    /**
     * 检查行是否匹配路径条件
     */
    private boolean matchesPathCriteria(Row dataRow, Map<String, Integer> columnIndexMap,
                                       Map<String, String> pathCriteria) {
        for (Map.Entry<String, String> criterion : pathCriteria.entrySet()) {
            String columnName = criterion.getKey();
            String expectedValue = criterion.getValue();

            Integer columnIndex = columnIndexMap.get(columnName);
            if (columnIndex == null) return false;

            String actualValue = getCellValueAsStringOptimized(dataRow.getCell(columnIndex));
            if (!expectedValue.equals(actualValue)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 解析日期字符串（与传统方式保持一致）
     */
    private LocalDate parseDate(String dateStr) {
        try {
            // 首先尝试传统方式的 yyyyMMdd 格式
            return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (DateTimeParseException e) {
            // 如果失败，尝试其他格式
            for (DateTimeFormatter formatter : DATE_FORMATTERS) {
                try {
                    return LocalDate.parse(dateStr, formatter);
                } catch (DateTimeParseException ex) {
                    // 尝试下一个格式
                }
            }

            log.debug("无法解析日期: {}", dateStr);
            return null;
        }
    }
    
    // 内部类
    private static class ColumnAnalysisResult {
        int dateColumnIndex = -1;
        List<String> pColumns = new ArrayList<>();
        List<String> uColumns = new ArrayList<>();
        Map<String, Integer> columnIndexMap = new HashMap<>();
    }
}
