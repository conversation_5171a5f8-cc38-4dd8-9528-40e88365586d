package com.scaffold;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 应用启动测试
 * 基于ngap-server项目的测试设计
 * 
 * <AUTHOR> Generator
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ScaffoldApplicationTests {

    @Test
    public void contextLoads() {
        // 测试Spring上下文是否能正常加载
    }
}
