package com.cet.electric.ngapserver.entity;

import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

public class MetricTest {

    @Test
    public void testMetricBuilder() {
        // 准备测试数据
        String metricId = "voltage_metric";
        String metricName = "电压指标";
        List<Metric> children = Arrays.asList(
                Metric.builder().metricId("child1").metricName("子指标1").build(),
                Metric.builder().metricId("child2").metricName("子指标2").build()
        );

        // 使用Builder创建Metric对象
        Metric metric = Metric.builder()
                .metricId(metricId)
                .metricName(metricName)
                .children(children)
                .build();

        // 验证结果
        assertNotNull(metric);
        assertEquals(metricId, metric.getMetricId());
        assertEquals(metricName, metric.getMetricName());
        assertEquals(children, metric.getChildren());
        assertEquals(2, metric.getChildren().size());
    }

    @Test
    public void testMetricNoArgsConstructor() {
        // 使用无参构造函数创建Metric对象
        Metric metric = new Metric();

        // 验证结果
        assertNotNull(metric);
        assertNull(metric.getMetricId());
        assertNull(metric.getMetricName());
        assertNull(metric.getChildren());
    }

    @Test
    public void testMetricAllArgsConstructor() {
        // 准备测试数据
        String metricId = "voltage_metric";
        String metricName = "电压指标";
        List<Metric> children = Arrays.asList(
                Metric.builder().metricId("child1").metricName("子指标1").build()
        );

        // 使用全参构造函数创建Metric对象
        Metric metric = new Metric(metricId, metricName, children);

        // 验证结果
        assertNotNull(metric);
        assertEquals(metricId, metric.getMetricId());
        assertEquals(metricName, metric.getMetricName());
        assertEquals(children, metric.getChildren());
    }

    @Test
    public void testMetricSettersAndGetters() {
        // 创建Metric对象
        Metric metric = new Metric();

        // 准备测试数据
        String metricId = "voltage_metric";
        String metricName = "电压指标";
        List<Metric> children = Arrays.asList(
                Metric.builder().metricId("child1").metricName("子指标1").build()
        );

        // 使用Setter设置值
        metric.setMetricId(metricId);
        metric.setMetricName(metricName);
        metric.setChildren(children);

        // 使用Getter验证值
        assertEquals(metricId, metric.getMetricId());
        assertEquals(metricName, metric.getMetricName());
        assertEquals(children, metric.getChildren());
    }

    @Test
    public void testGetIsMetric_WithChildren() {
        // 创建有子指标的Metric对象
        List<Metric> children = Arrays.asList(
                Metric.builder().metricId("child1").metricName("子指标1").build(),
                Metric.builder().metricId("child2").metricName("子指标2").build()
        );

        Metric metric = Metric.builder()
                .metricId("parent_metric")
                .metricName("父指标")
                .children(children)
                .build();

        // 验证不是叶节点
        assertFalse(metric.getIsMetric());
    }

    @Test
    public void testGetIsMetric_WithoutChildren() {
        // 创建没有子指标的Metric对象
        Metric metric = Metric.builder()
                .metricId("leaf_metric")
                .metricName("叶节点指标")
                .children(null)
                .build();

        // 验证是叶节点
        assertTrue(metric.getIsMetric());
    }

    @Test
    public void testGetIsMetric_WithEmptyChildren() {
        // 创建有空子指标列表的Metric对象
        Metric metric = Metric.builder()
                .metricId("leaf_metric")
                .metricName("叶节点指标")
                .children(new ArrayList<>())
                .build();

        // 验证是叶节点
        assertTrue(metric.getIsMetric());
    }

    @Test
    public void testMetricEqualsAndHashCode() {
        // 创建两个相同的Metric对象
        List<Metric> children = Arrays.asList(
                Metric.builder().metricId("child1").metricName("子指标1").build()
        );

        Metric metric1 = Metric.builder()
                .metricId("voltage_metric")
                .metricName("电压指标")
                .children(children)
                .build();

        Metric metric2 = Metric.builder()
                .metricId("voltage_metric")
                .metricName("电压指标")
                .children(children)
                .build();

        // 验证equals方法
        assertEquals(metric1, metric2);
        assertEquals(metric2, metric1);
        assertEquals(metric1, metric1);

        // 验证hashCode方法
        assertEquals(metric1.hashCode(), metric2.hashCode());
    }

    @Test
    public void testMetricNotEquals() {
        // 创建两个不同的Metric对象
        Metric metric1 = Metric.builder()
                .metricId("voltage_metric")
                .metricName("电压指标")
                .build();

        Metric metric2 = Metric.builder()
                .metricId("current_metric")
                .metricName("电流指标")
                .build();

        // 验证不相等
        assertNotEquals(metric1, metric2);
        assertNotEquals(metric2, metric1);
        assertNotEquals(metric1, null);
        assertNotEquals(metric1, "not a metric");
    }

    @Test
    public void testMetricToString() {
        // 创建Metric对象
        Metric metric = Metric.builder()
                .metricId("voltage_metric")
                .metricName("电压指标")
                .build();

        // 验证toString方法
        String toString = metric.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("Metric"));
        assertTrue(toString.contains("metricId=voltage_metric"));
        assertTrue(toString.contains("metricName=电压指标"));
    }

    @Test
    public void testMetricWithNullValues() {
        // 创建包含null值的Metric对象
        Metric metric = Metric.builder()
                .metricId(null)
                .metricName(null)
                .children(null)
                .build();

        // 验证null值处理
        assertNotNull(metric);
        assertNull(metric.getMetricId());
        assertNull(metric.getMetricName());
        assertNull(metric.getChildren());
        assertTrue(metric.getIsMetric()); // null children应该被视为叶节点
    }

    @Test
    public void testMetricHierarchy() {
        // 创建多层级的指标结构
        Metric grandChild = Metric.builder()
                .metricId("grandchild")
                .metricName("孙指标")
                .children(null)
                .build();

        Metric child = Metric.builder()
                .metricId("child")
                .metricName("子指标")
                .children(Arrays.asList(grandChild))
                .build();

        Metric parent = Metric.builder()
                .metricId("parent")
                .metricName("父指标")
                .children(Arrays.asList(child))
                .build();

        // 验证层级结构
        assertFalse(parent.getIsMetric()); // 父节点不是叶节点
        assertFalse(child.getIsMetric());  // 子节点不是叶节点
        assertTrue(grandChild.getIsMetric()); // 孙节点是叶节点

        assertEquals(1, parent.getChildren().size());
        assertEquals(child, parent.getChildren().get(0));
        assertEquals(1, child.getChildren().size());
        assertEquals(grandChild, child.getChildren().get(0));
    }

    @Test
    public void testMetricFieldModification() {
        // 创建Metric对象
        Metric metric = Metric.builder()
                .metricId("original_id")
                .metricName("原始名称")
                .build();

        // 修改字段值
        metric.setMetricId("modified_id");
        metric.setMetricName("修改后的名称");

        List<Metric> newChildren = Arrays.asList(
                Metric.builder().metricId("new_child").metricName("新子指标").build()
        );
        metric.setChildren(newChildren);

        // 验证修改结果
        assertEquals("modified_id", metric.getMetricId());
        assertEquals("修改后的名称", metric.getMetricName());
        assertEquals(newChildren, metric.getChildren());
        assertFalse(metric.getIsMetric()); // 现在有子节点了
    }

    @Test
    public void testMetricCopy() {
        // 创建原始Metric对象
        List<Metric> children = Arrays.asList(
                Metric.builder().metricId("child1").metricName("子指标1").build()
        );

        Metric original = Metric.builder()
                .metricId("voltage_metric")
                .metricName("电压指标")
                .children(children)
                .build();

        // 创建副本
        Metric copy = Metric.builder()
                .metricId(original.getMetricId())
                .metricName(original.getMetricName())
                .children(original.getChildren())
                .build();

        // 验证副本与原始对象相等
        assertEquals(original, copy);
        assertEquals(original.hashCode(), copy.hashCode());
        assertEquals(original.getIsMetric(), copy.getIsMetric());
    }

    @Test
    public void testMetricChildrenManipulation() {
        // 创建Metric对象
        Metric metric = Metric.builder()
                .metricId("parent_metric")
                .metricName("父指标")
                .children(new ArrayList<>())
                .build();

        // 初始状态是叶节点
        assertTrue(metric.getIsMetric());

        // 添加子指标
        Metric child1 = Metric.builder().metricId("child1").metricName("子指标1").build();
        metric.getChildren().add(child1);

        // 现在不是叶节点了
        assertFalse(metric.getIsMetric());

        // 添加更多子指标
        Metric child2 = Metric.builder().metricId("child2").metricName("子指标2").build();
        metric.getChildren().add(child2);

        assertEquals(2, metric.getChildren().size());
        assertFalse(metric.getIsMetric());

        // 清空子指标
        metric.getChildren().clear();

        // 又变成叶节点了
        assertTrue(metric.getIsMetric());
    }
}
