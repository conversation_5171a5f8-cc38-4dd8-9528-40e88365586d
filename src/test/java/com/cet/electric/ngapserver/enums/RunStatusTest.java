package com.cet.electric.ngapserver.enums;

import org.junit.Test;

import static org.junit.Assert.*;

public class RunStatusTest {

    @Test
    public void testEnumValues() {
        // 验证枚举值的存在
        RunStatus[] expectedValues = {RunStatus.READY, RunStatus.RUNNING, RunStatus.SUCCESS, RunStatus.FAILED};
        RunStatus[] actualValues = RunStatus.values();
        
        assertEquals(4, actualValues.length);
        assertArrayEquals(expectedValues, actualValues);
    }

    @Test
    public void testEnumNames() {
        // 验证枚举名称
        assertEquals("READY", RunStatus.READY.name());
        assertEquals("RUNNING", RunStatus.RUNNING.name());
        assertEquals("SUCCESS", RunStatus.SUCCESS.name());
        assertEquals("FAILED", RunStatus.FAILED.name());
    }

    @Test
    public void testFromString_ValidValues() {
        // 测试有效的字符串转换
        assertEquals(RunStatus.READY, RunStatus.fromString("READY"));
        assertEquals(RunStatus.RUNNING, RunStatus.fromString("RUNNING"));
        assertEquals(RunStatus.SUCCESS, RunStatus.fromString("SUCCESS"));
        assertEquals(RunStatus.FAILED, RunStatus.fromString("FAILED"));
    }

    @Test
    public void testFromString_CaseInsensitive() {
        // 测试大小写不敏感
        assertEquals(RunStatus.READY, RunStatus.fromString("ready"));
        assertEquals(RunStatus.RUNNING, RunStatus.fromString("running"));
        assertEquals(RunStatus.SUCCESS, RunStatus.fromString("success"));
        assertEquals(RunStatus.FAILED, RunStatus.fromString("failed"));
        
        // 测试混合大小写
        assertEquals(RunStatus.READY, RunStatus.fromString("Ready"));
        assertEquals(RunStatus.RUNNING, RunStatus.fromString("Running"));
        assertEquals(RunStatus.SUCCESS, RunStatus.fromString("Success"));
        assertEquals(RunStatus.FAILED, RunStatus.fromString("Failed"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testFromString_InvalidValue() {
        // 测试无效值应该抛出异常
        RunStatus.fromString("INVALID");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testFromString_NullValue() {
        // 测试null值应该抛出异常
        RunStatus.fromString(null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testFromString_EmptyValue() {
        // 测试空字符串应该抛出异常
        RunStatus.fromString("");
    }

    @Test
    public void testFromString_ExceptionMessage() {
        // 测试异常消息格式
        try {
            RunStatus.fromString("INVALID");
            fail("应该抛出IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            String expectedMessage = "No enum constant " + RunStatus.class.getCanonicalName() + ".INVALID";
            assertEquals(expectedMessage, e.getMessage());
        }
    }

    @Test
    public void testValueOf() {
        // 测试标准的valueOf方法
        assertEquals(RunStatus.READY, RunStatus.valueOf("READY"));
        assertEquals(RunStatus.RUNNING, RunStatus.valueOf("RUNNING"));
        assertEquals(RunStatus.SUCCESS, RunStatus.valueOf("SUCCESS"));
        assertEquals(RunStatus.FAILED, RunStatus.valueOf("FAILED"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testValueOf_InvalidValue() {
        // 测试valueOf方法对无效值的处理
        RunStatus.valueOf("INVALID");
    }

    @Test
    public void testOrdinal() {
        // 测试枚举的序号
        assertEquals(0, RunStatus.READY.ordinal());
        assertEquals(1, RunStatus.RUNNING.ordinal());
        assertEquals(2, RunStatus.SUCCESS.ordinal());
        assertEquals(3, RunStatus.FAILED.ordinal());
    }

    @Test
    public void testToString() {
        // 测试toString方法
        assertEquals("READY", RunStatus.READY.toString());
        assertEquals("RUNNING", RunStatus.RUNNING.toString());
        assertEquals("SUCCESS", RunStatus.SUCCESS.toString());
        assertEquals("FAILED", RunStatus.FAILED.toString());
    }

    @Test
    public void testCompareTo() {
        // 测试枚举的比较
        assertTrue(RunStatus.READY.compareTo(RunStatus.RUNNING) < 0);
        assertTrue(RunStatus.RUNNING.compareTo(RunStatus.SUCCESS) < 0);
        assertTrue(RunStatus.SUCCESS.compareTo(RunStatus.FAILED) < 0);
        assertEquals(0, RunStatus.READY.compareTo(RunStatus.READY));
    }

    @Test
    public void testEquals() {
        // 测试equals方法
        assertEquals(RunStatus.READY, RunStatus.READY);
        assertNotEquals(RunStatus.READY, RunStatus.RUNNING);
        assertNotEquals(RunStatus.READY, null);
        assertNotEquals(RunStatus.READY, "READY");
    }

    @Test
    public void testHashCode() {
        // 测试hashCode方法
        assertEquals(RunStatus.READY.hashCode(), RunStatus.READY.hashCode());
        assertNotEquals(RunStatus.READY.hashCode(), RunStatus.RUNNING.hashCode());
    }
}
