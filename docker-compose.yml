version: '3'
services:
  java-scaffold:
    image: *************/base/java-scaffold:1.0.0-SNAPSHOT
    ports:
      - 28080:28080
    networks:
      scaffold-net:
        ipv4_address: ${master_network_segment}.${scaffold_ip}
        aliases:
          - java-scaffold
    hostname: java-scaffold
    restart: always
    privileged: true
    environment:
      TZ: Asia/Shanghai
      DATABASE_URL: **********************************
      # PostgreSQL配置示例（可选）
      # DATABASE_URL: *****************************************
      # DATABASE_USERNAME: scaffold
      # DATABASE_PASSWORD: scaffold123
    volumes:
      - /var/scaffold/database:/database
      - /var/scaffold/logs:/logs
      - /var/scaffold/data:/data

networks:
  scaffold-net:
    driver: bridge
    ipam:
      driver: default
      config:
      - subnet: ${master_network_segment}.0/24
