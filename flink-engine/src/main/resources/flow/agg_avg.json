[{"sink": [{"id": "T5", "from": ["T4"], "name": "DeviceAvg_Year", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vAvgT4", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DeviceAvg_Year"}, {"id": "T6", "from": ["T3"], "name": "DeviceAvg_", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vAvgT3", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DeviceAvg_"}], "trans": [{"id": "T2", "to": "T2", "from": ["T1"], "func": 11, "name": "天avg", "type": "StreamGroupByFunction", "groupBy": ["devID", "dataID", "tm"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vAvgT2": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 12}], "resultField": "vAvgT2", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vAvgT2": 3, "aggregationcycle": 4}}, {"id": "T3", "to": "T3", "from": ["T2"], "func": 11, "name": "月avg", "type": "StreamGroupByFunction", "groupBy": ["devID", "dataID", "tm"], "nodeType": "trans", "funcField": "vAvgT2", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vAvgT2", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vAvgT3": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 14}], "resultField": "vAvgT3", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vAvgT3": 3, "aggregationcycle": 4}}, {"id": "T4", "to": "T4", "from": ["T3"], "func": 11, "name": "年avg", "type": "StreamGroupByFunction", "groupBy": ["devID", "dataID", "tm"], "nodeType": "trans", "funcField": "vAvgT3", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vAvgT3", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vAvgT4": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 17}], "resultField": "vAvgT4", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vAvgT4": 3, "aggregationcycle": 4}}], "source": [{"id": "T1", "to": "T1", "name": "定时记录输入算子", "type": "DatalogDataStreamSource", "nodeType": "source", "RecalcTime": 1732982400000, "dataStream": "stream", "fieldsArray": {"tm": "long", "val": "double", "devID": "int", "dataID": "int", "status": "byte", "logicalID": "short", "dataTypeID": "short"}, "latenessDay": 2, "fieldsPosition": {"tm": 4, "val": 5, "devID": 0, "dataID": 1, "status": 6, "logicalID": 2, "dataTypeID": 3}}], "resource": {"jobName": "jc_aggregate_avg", "jobmanager": "1024M", "parallelism": 1, "taskmanager": "1024M"}}]