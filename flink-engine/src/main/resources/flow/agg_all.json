[{"sink": [{"id": "T15", "from": ["T2"], "name": "DeviceCount_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vCountT2", "fieldType": "double", "description": "计数结果", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DeviceCount_Hour"}, {"id": "T16", "from": ["T3"], "name": "DeviceSum_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vArithSumT3", "fieldType": "double", "description": "算术累加", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DeviceSum_Hour"}, {"id": "T17", "from": ["T4"], "name": "DeviceAvg_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vAvgT4", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DeviceAvg_Hour"}, {"id": "T18", "from": ["T5"], "name": "DeviceMax_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vMaxT5", "fieldType": "double", "description": "最大值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DeviceMax_Hour"}, {"id": "T19", "from": ["T6"], "name": "DeviceMin_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vMinT6", "fieldType": "double", "description": "最小值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DeviceMin_Hour"}, {"id": "T20", "from": ["T7"], "name": "DeviceAvg_Day", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vAvgT7", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DeviceAvg_Day"}, {"id": "T21", "from": ["T8"], "name": "DataIDCount_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vCountT8", "fieldType": "double", "description": "计数结果", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DataIDCount_Hour"}, {"id": "T22", "from": ["T9"], "name": "DataIDSum_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vArithSumT9", "fieldType": "double", "description": "算术累加", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DataIDSum_Hour"}, {"id": "T23", "from": ["T10"], "name": "DataIDAvg_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vAvgT10", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DataIDAvg_Hour"}, {"id": "T24", "from": ["T11"], "name": "DataIDMax_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vMaxT11", "fieldType": "double", "description": "最大值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DataIDMax_Hour"}, {"id": "T25", "from": ["T12"], "name": "DataIDMin_Hour", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vMinT12", "fieldType": "double", "description": "最小值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DataIDMin_Hour"}, {"id": "T26", "from": ["T13"], "name": "DataIDAvg_Day", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["dataID", "tm", "aggregationcycle"], "dataStream": "stream", "filterTable": [{"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "vAvgT13", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "redisTableName": "DataIDAvg_Day"}], "trans": [{"id": "T2", "to": "T2", "from": ["T1"], "func": 17, "name": "小时Count-按设备dataid", "type": "StreamGroupByFunction", "groupBy": ["devID", "tm", "dataID"], "nodeType": "trans", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vCountT2": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vCountT2", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vCountT2": 3, "aggregationcycle": 4}}, {"id": "T3", "to": "T3", "from": ["T1"], "func": 18, "name": "小时SUM-按设备dataid", "type": "StreamGroupByFunction", "groupBy": ["devID", "tm", "dataID"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vArithSumT3": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vArithSumT3", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vArithSumT3": 3, "aggregationcycle": 4}}, {"id": "T4", "to": "T4", "from": ["T1"], "func": 11, "name": "小时Avg-按设备dataid", "type": "StreamGroupByFunction", "groupBy": ["devID", "tm", "dataID"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vAvgT4": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vAvgT4", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vAvgT4": 3, "aggregationcycle": 4}}, {"id": "T5", "to": "T5", "from": ["T1"], "func": 3, "name": "小时Max-按设备dataid", "type": "StreamGroupByFunction", "groupBy": ["devID", "tm", "dataID"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vMaxT5": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vMaxT5", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vMaxT5": 3, "aggregationcycle": 4}}, {"id": "T6", "to": "T6", "from": ["T1"], "func": 4, "name": "小时Min-按设备dataid", "type": "StreamGroupByFunction", "groupBy": ["devID", "tm", "dataID"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vMinT6": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vMinT6", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vMinT6": 3, "aggregationcycle": 4}}, {"id": "T7", "to": "T7", "from": ["T1"], "func": 11, "name": "天Avg-按设备dataid", "type": "StreamGroupByFunction", "groupBy": ["devID", "dataID", "tm"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "devID": "int", "dataID": "int", "status": "byte", "vAvgT7": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 12}], "resultField": "vAvgT7", "fieldsPosition": {"tm": 2, "devID": 0, "dataID": 1, "status": 5, "vAvgT7": 3, "aggregationcycle": 4}}, {"id": "T8", "to": "T8", "from": ["T1"], "func": 17, "name": "小时Count-按dataid", "type": "StreamGroupByFunction", "groupBy": ["dataID", "tm"], "nodeType": "trans", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "dataID": "int", "status": "byte", "vCountT8": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vCountT8", "fieldsPosition": {"tm": 1, "dataID": 0, "status": 4, "vCountT8": 2, "aggregationcycle": 3}}, {"id": "T9", "to": "T9", "from": ["T1"], "func": 18, "name": "小时Sum-按dataid", "type": "StreamGroupByFunction", "groupBy": ["dataID", "tm"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "dataID": "int", "status": "byte", "vArithSumT9": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vArithSumT9", "fieldsPosition": {"tm": 1, "dataID": 0, "status": 4, "vArithSumT9": 2, "aggregationcycle": 3}}, {"id": "T10", "to": "T10", "from": ["T1"], "func": 11, "name": "小时Avg-按dataid", "type": "StreamGroupByFunction", "groupBy": ["dataID", "tm"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "dataID": "int", "status": "byte", "vAvgT10": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vAvgT10", "fieldsPosition": {"tm": 1, "dataID": 0, "status": 4, "vAvgT10": 2, "aggregationcycle": 3}}, {"id": "T11", "to": "T11", "from": ["T1"], "func": 3, "name": "小时Max-按dataid", "type": "StreamGroupByFunction", "groupBy": ["dataID", "tm"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "dataID": "int", "status": "byte", "vMaxT11": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vMaxT11", "fieldsPosition": {"tm": 1, "dataID": 0, "status": 4, "vMaxT11": 2, "aggregationcycle": 3}}, {"id": "T12", "to": "T12", "from": ["T1"], "func": 4, "name": "小时Min-按dataid", "type": "StreamGroupByFunction", "groupBy": ["dataID", "tm"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "dataID": "int", "status": "byte", "vMinT12": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vMinT12", "fieldsPosition": {"tm": 1, "dataID": 0, "status": 4, "vMinT12": 2, "aggregationcycle": 3}}, {"id": "T13", "to": "T13", "from": ["T1"], "func": 11, "name": "天Avg-按dataid", "type": "StreamGroupByFunction", "groupBy": ["dataID", "tm"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "dataStream": "stream", "fieldsArray": {"tm": "long", "dataID": "int", "status": "byte", "vAvgT13": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 12}], "resultField": "vAvgT13", "fieldsPosition": {"tm": 1, "dataID": 0, "status": 4, "vAvgT13": 2, "aggregationcycle": 3}}], "source": [{"id": "T1", "to": "T1", "name": "定时记录输入算子", "type": "DatalogDataStreamSource", "nodeType": "source", "RecalcTime": 1735574400000, "dataStream": "stream", "fieldsArray": {"tm": "long", "val": "double", "devID": "int", "dataID": "int", "status": "byte", "logicalID": "short", "dataTypeID": "short"}, "latenessDay": 2, "fieldsPosition": {"tm": 4, "val": 5, "devID": 0, "dataID": 1, "status": 6, "logicalID": 2, "dataTypeID": 3}}], "resource": {"jobName": "jc_aggregate_1", "jobmanager": "1024M", "parallelism": 1, "taskmanager": "1024M"}}]