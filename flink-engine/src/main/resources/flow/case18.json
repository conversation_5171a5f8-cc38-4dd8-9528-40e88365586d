[{"resource": {"jobName": "difference"}, "sink": [{"isMetaRedisFormat": false, "redisTableName": "pecdeviceenergy", "filterTable": [{"field": "devID", "frontedType": "number", "description": "设备id", "fieldType": "int"}, {"field": "dataID", "frontedType": "number", "description": "数据id", "fieldType": "int"}, {"field": "logicalID", "frontedType": "number", "description": "回路号id", "fieldType": "short"}, {"field": "dataTypeID", "frontedType": "number", "description": "数据类型id", "fieldType": "short"}, {"field": "tm", "frontedType": "date", "description": "时间", "fieldType": "long"}, {"field": "val", "frontedType": "number", "description": "表码值", "fieldType": "double"}, {"field": "status", "frontedType": "number", "description": "状态", "fieldType": "byte"}], "dataStream": "stream", "syncMethod": "1", "name": "redis输出算子", "from": ["T2"], "id": "T3", "nodeType": "sink", "type": "CommonRedisSink", "modelAssociation": [{"selectedAField": "devID", "selectedBLabel": "aggregationcycle", "selectedBtype": "", "selectedAtype": "int", "selectedBField": "aggregationcycle"}, {"selectedAField": "dataID", "selectedBLabel": "dataid", "selectedBtype": "", "selectedAtype": "int", "selectedBField": "dataid"}, {"selectedAField": "logicalID", "selectedBLabel": "logicalid", "selectedBtype": "", "selectedAtype": "short", "selectedBField": "logicalid"}, {"selectedAField": "dataTypeID", "selectedBLabel": "deviceid", "selectedBtype": "", "selectedAtype": "short", "selectedBField": "deviceid"}, {"selectedAField": "tm", "selectedBLabel": "logtime", "selectedBtype": "", "selectedAtype": "long", "selectedBField": "logtime"}, {"selectedAField": "val", "selectedBLabel": "energydata", "selectedBtype": "", "selectedAtype": "double", "selectedBField": "energydata"}, {"selectedAField": "status", "selectedBLabel": "", "selectedBtype": "", "selectedAtype": "byte", "selectedBField": ""}]}, {"isMetaRedisFormat": false, "redisTableName": "ro_aggregationdata", "filterTable": [{"field": "devID", "frontedType": "number", "description": "设备id", "fieldType": "int"}, {"field": "dataID", "frontedType": "number", "description": "数据id", "fieldType": "int"}, {"field": "logicalID", "frontedType": "number", "description": "回路号id", "fieldType": "short"}, {"field": "dataTypeID", "frontedType": "number", "description": "数据类型id", "fieldType": "short"}, {"field": "tm", "frontedType": "date", "description": "时间", "fieldType": "long"}, {"field": "val", "frontedType": "number", "description": "表码值", "fieldType": "double"}, {"field": "status", "frontedType": "number", "description": "状态", "fieldType": "byte"}], "dataStream": "stream", "syncMethod": "1", "name": "redis输出算子", "from": ["T1"], "id": "T5", "nodeType": "sink", "type": "CommonRedisSink", "modelAssociation": [{"selectedAField": "devID", "selectedBLabel": "aggregationcycle", "selectedBtype": "", "selectedAtype": "int", "selectedBField": "aggregationcycle"}, {"selectedAField": "dataID", "selectedBLabel": "paraid", "selectedBtype": "", "selectedAtype": "int", "selectedBField": "paraid"}, {"selectedAField": "logicalID", "selectedBLabel": "objectlabel", "selectedBtype": "", "selectedAtype": "short", "selectedBField": "objectlabel"}, {"selectedAField": "dataTypeID", "selectedBLabel": "objectid", "selectedBtype": "", "selectedAtype": "short", "selectedBField": "objectid"}, {"selectedAField": "tm", "selectedBLabel": "logtime", "selectedBtype": "", "selectedAtype": "long", "selectedBField": "logtime"}, {"selectedAField": "val", "selectedBLabel": "value", "selectedBtype": "", "selectedAtype": "double", "selectedBField": "value"}, {"selectedAField": "status", "selectedBLabel": "", "selectedBtype": "", "selectedAtype": "byte", "selectedBField": ""}]}], "source": [{"fieldsArray": {"devID": "int", "dataID": "int", "logicalID": "short", "dataTypeID": "short", "tm": "long", "val": "double", "status": "byte"}, "latenessDay": 4, "dataStream": "stream", "name": "定时记录输入算子", "RecalcTime": 1741017600000, "fieldsPosition": {"devID": 0, "dataID": 1, "logicalID": 2, "dataTypeID": 3, "tm": 4, "val": 5, "status": 6}, "id": "T1", "to": "T1", "nodeType": "source", "type": "DatalogDataStreamSource"}], "trans": [{"groupBy": ["devID", "dataID", "logicalID", "dataTypeID"], "modelAssociation": [{"selectedAField": "devID", "selectedBLabel": "devID", "selectedBtype": "", "selectedAtype": "int", "selectedBField": "devID"}, {"selectedAField": "dataID", "selectedBLabel": "dataID", "selectedBtype": "", "selectedAtype": "int", "selectedBField": "dataID"}, {"selectedAField": "logicalID", "selectedBLabel": "logicalID", "selectedBtype": "", "selectedAtype": "short", "selectedBField": "logicalID"}, {"selectedAField": "dataTypeID", "selectedBLabel": "dataTypeID", "selectedBtype": "", "selectedAtype": "short", "selectedBField": "dataTypeID"}, {"selectedAField": "tm", "selectedBLabel": "tm", "selectedBtype": "", "selectedAtype": "long", "selectedBField": "tm"}, {"selectedAField": "val", "selectedBLabel": "val", "selectedBtype": "", "selectedAtype": "double", "selectedBField": "val"}, {"selectedAField": "status", "selectedBLabel": "status", "selectedBtype": "", "selectedAtype": "byte", "selectedBField": "status"}], "filterTable": [{"field": "devID", "frontedType": "number", "description": "设备id", "fieldType": "int"}, {"field": "dataID", "frontedType": "number", "description": "数据id", "fieldType": "int"}, {"field": "logicalID", "frontedType": "number", "description": "回路号id", "fieldType": "short"}, {"field": "dataTypeID", "frontedType": "number", "description": "数据类型id", "fieldType": "short"}, {"field": "tm", "frontedType": "date", "description": "时间", "fieldType": "long"}, {"field": "val", "frontedType": "number", "description": "表码值", "fieldType": "double"}, {"field": "status", "frontedType": "number", "description": "状态", "fieldType": "byte"}], "fieldsPosition": {"devID": 0, "dataID": 1, "logicalID": 2, "dataTypeID": 3, "tm": 4, "val": 5, "status": 6}, "nodeType": "trans", "type": "ExceptionHandlingFunction", "fieldsArray": {"devID": "int", "dataID": "int", "logicalID": "short", "dataTypeID": "short", "tm": "long", "val": "double", "status": "byte", "priorTmT2": "long", "priorValT2": "double", "priorStatusT2": "byte", "latterTmT2": "long", "latterValT2": "double", "latterStatusT2": "byte"}, "dataStream": "stream", "name": "异常处理算子", "from": ["T1"], "id": "T2", "to": "T2"}]}]