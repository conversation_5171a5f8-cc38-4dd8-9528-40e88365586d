[{"sink": [{"id": "T7", "from": ["T6"], "name": "field_setting", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "logicalID", "dataTypeID", "tm"], "dataStream": "stream", "filterTable": [{"field": "formula_2", "formula": "$dataID#/1000.0 + $quantityobject_id#+ $stationid#* $rate#+ $val#*$status#", "fieldType": "double", "description": "合法公式", "frontedType": "number"}, {"field": "illegal_formula", "formula": "$val#/ 0.0 + 1000", "fieldType": "double", "description": "非法公式", "frontedType": "number"}, {"field": "formula", "formula": "($val#*100+$val#)/100.0 + $dataTypeID# + $energytype#+$quantityobject_id#", "fieldType": "double", "description": "合法公式1", "frontedType": "number"}, {"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "redisTableName": "field_setting"}, {"id": "T10", "from": ["T8"], "name": "nest_field_setting", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "logicalID", "dataTypeID", "tm"], "dataStream": "stream", "filterTable": [{"field": "_field_1736218734956", "formula": "$nest_formula#+($formula#+ $val#*100+$logicalID#)/100.0", "fieldType": "double", "description": "嵌套嵌套公式", "frontedType": "number"}, {"field": "nest_formula", "formula": "$formula#+$formula#*100 + $channelid#+$logicalID#", "fieldType": "double", "description": "嵌套字段", "frontedType": "number"}, {"field": "formula", "formula": "$devID#+($val#*100+$devID#) /100.0", "fieldType": "double", "description": "公式字段", "frontedType": "number"}, {"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "redisTableName": "nest_field_setting"}], "trans": [{"id": "T4", "to": "T4", "from": ["T1"], "name": "过滤：4000012和4000004", "type": "GeneralFilterFunction", "nodeType": "trans", "dataStream": "stream", "fieldsArray": {"tm": "long", "val": "double", "devID": "int", "dataID": "int", "status": "byte", "logicalID": "short", "dataTypeID": "short"}, "filterTable": [{"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "totalFilter": "&&", "fieldsPosition": {"tm": 4, "val": 5, "devID": 0, "dataID": 1, "status": 6, "logicalID": 2, "dataTypeID": 3}, "filterCondition": " include([4000012,4000004], dataID)", "filterConditionArray": [{"rangeType": "", "inputValue": ["4000012", "4000004"], "selectField": "dataID", "selectFunctionName": "in"}]}, {"id": "T3", "to": "T3", "from": ["T4", "T2"], "name": "设备测点内连接", "type": "StreamJoinFunction", "tableA": [{"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "tableB": [{"field": "deviceid", "label": "设备id（deviceid）", "fieldType": "long", "description": "设备id", "frontedType": "number"}, {"field": "dataid", "label": "数据id（dataid）", "fieldType": "long", "description": "数据id", "frontedType": "number"}, {"field": "logicalindex", "label": "回路号id（logicalindex）", "fieldType": "int", "description": "回路号id", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "streamA": "GeneralFilterFunction", "streamB": "data-center-agg", "joinType": "mapjoin", "nodeType": "trans", "streamAM": "T4", "streamBM": "T2", "dataStream": "stream", "fieldsArray": {"tm": "long", "val": "double", "path": "String", "rate": "double", "devID": "int", "level": "int", "orgid": "long", "dataID": "int", "status": "byte", "orglabel": "String", "channelid": "long", "logicalID": "short", "stationid": "long", "dataTypeID": "short", "energytype": "int", "monitoredid": "long", "monitoredlabel": "String", "quantityobject_id": "long"}, "joinCondition": "a.devID = b.deviceid  and a.dataID = b.dataid  and a.logicalID = b.logicalindex", "fieldsPosition": {"tm": 4, "val": 5, "path": 13, "rate": 15, "devID": 0, "level": 14, "orgid": 16, "dataID": 1, "status": 6, "orglabel": 17, "channelid": 7, "logicalID": 2, "stationid": 8, "dataTypeID": 3, "energytype": 10, "monitoredid": 11, "monitoredlabel": 12, "quantityobject_id": 9}, "associationTableData": [{"selectedAField": "devID", "selectedBField": "deviceid"}, {"selectedAField": "dataID", "selectedBField": "dataid"}, {"selectedAField": "logicalID", "selectedBField": "logicalindex"}]}, {"id": "T6", "to": "T6", "from": ["T3"], "name": "新增计算字段", "type": "FieldSettingFunction", "nodeType": "trans", "dataStream": "stream", "fieldTable": [{"field": "formula_2", "formula": "$dataID#/1000.0 + $quantityobject_id#+ $stationid#* $rate#+ $val#*$status#", "fieldType": "double", "description": "合法公式", "frontedType": "number"}, {"field": "illegal_formula", "formula": "$val#/ 0.0 + 1000", "fieldType": "double", "description": "非法公式", "frontedType": "number"}, {"field": "formula", "formula": "($val#*100+$val#)/100.0 + $dataTypeID# + $energytype#+$quantityobject_id#", "fieldType": "double", "description": "合法公式1", "frontedType": "number"}, {"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "fieldsArray": {"tm": "long", "bbb": "double", "val": "double", "path": "String", "rate": "double", "devID": "int", "level": "int", "orgid": "long", "dataID": "int", "status": "byte", "orglabel": "String", "channelid": "long", "logicalID": "short", "stationid": "long", "dataTypeID": "short", "energytype": "int", "monitoredid": "long", "monitoredlabel": "String", "quantityobject_id": "long"}, "filterTable": [{"field": "formula_2", "formula": "$dataID#/1000.0 + $quantityobject_id#+ $stationid#* $rate#+ $val#*$status#", "fieldType": "double", "description": "合法公式", "frontedType": "number"}, {"field": "illegal_formula", "formula": "$val#/ 0.0 + 1000", "fieldType": "double", "description": "非法公式", "frontedType": "number"}, {"field": "formula", "formula": "($val#*100+$val#)/100.0 + $dataTypeID# + $energytype#+$quantityobject_id#", "fieldType": "double", "description": "合法公式1", "frontedType": "number"}, {"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "allfieldTable": [{"field": "formula_2", "formula": "$dataID#/1000.0 + $quantityobject_id#+ $stationid#* $rate#+ $val#*$status#", "fieldType": "double", "description": "合法公式", "frontedType": "number"}, {"field": "illegal_formula", "formula": "$val#/ 0.0 + 1000", "fieldType": "double", "description": "非法公式", "frontedType": "number"}, {"field": "formula", "formula": "($val#*100+$val#)/100.0 + $dataTypeID# + $energytype#+$quantityobject_id#", "fieldType": "double", "description": "合法公式1", "frontedType": "number"}, {"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "fieldsPosition": {"tm": 5, "bbb": 0, "val": 6, "path": 14, "rate": 16, "devID": 1, "level": 15, "orgid": 17, "dataID": 2, "status": 7, "orglabel": 18, "channelid": 8, "logicalID": 3, "stationid": 9, "dataTypeID": 4, "energytype": 11, "monitoredid": 12, "monitoredlabel": 13, "quantityobject_id": 10}}, {"id": "T8", "to": "T8", "from": ["T3"], "name": "嵌套字段", "type": "FieldSettingFunction", "nodeType": "trans", "dataStream": "stream", "fieldTable": [{"field": "_field_1736218734956", "formula": "$nest_formula#+($formula#+ $val#*100+$logicalID#)/100.0", "fieldType": "double", "description": "嵌套嵌套公式", "frontedType": "number"}, {"field": "nest_formula", "formula": "$formula#+$formula#*100 + $channelid#+$logicalID#", "fieldType": "double", "description": "嵌套字段", "frontedType": "number"}, {"field": "formula", "formula": "$devID#+($val#*100+$devID#) /100.0", "fieldType": "double", "description": "公式字段", "frontedType": "number"}, {"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "filterTable": [{"field": "_field_1736218734956", "formula": "$nest_formula#+($formula#+ $val#*100+$logicalID#)/100.0", "fieldType": "double", "description": "嵌套嵌套公式", "frontedType": "number"}, {"field": "nest_formula", "formula": "$formula#+$formula#*100 + $channelid#+$logicalID#", "fieldType": "double", "description": "嵌套字段", "frontedType": "number"}, {"field": "formula", "formula": "$devID#+($val#*100+$devID#) /100.0", "fieldType": "double", "description": "公式字段", "frontedType": "number"}, {"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "fieldsArray": {"tm": "long", "bbb": "double", "val": "double", "path": "String", "rate": "double", "devID": "int", "level": "int", "orgid": "long", "dataID": "int", "status": "byte", "orglabel": "String", "channelid": "long", "logicalID": "short", "stationid": "long", "dataTypeID": "short", "energytype": "int", "monitoredid": "long", "monitoredlabel": "String", "quantityobject_id": "long", "_field_1736218734956": "double", "nest_formula": "double", "formula": "double"}, "allfieldTable": [{"field": "_field_1736218734956", "formula": "$nest_formula#+($formula#+ $val#*100+$logicalID#)/100.0", "fieldType": "double", "description": "嵌套嵌套公式", "frontedType": "number"}, {"field": "nest_formula", "formula": "$formula#+$formula#*100 + $channelid#+$logicalID#", "fieldType": "double", "description": "嵌套字段", "frontedType": "number"}, {"field": "formula", "formula": "$devID#+($val#*100+$devID#) /100.0", "fieldType": "double", "description": "公式字段", "frontedType": "number"}, {"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}]}], "source": [{"id": "T1", "to": "T1", "name": "定时记录输入算子", "type": "DatalogDataStreamSource", "nodeType": "source", "RecalcTime": 1734969600000, "dataStream": "stream", "fieldsArray": {"tm": "long", "val": "double", "devID": "int", "dataID": "int", "status": "byte", "logicalID": "short", "dataTypeID": "short"}, "latenessDay": 1, "fieldsPosition": {"tm": 4, "val": 5, "devID": 0, "dataID": 1, "status": 6, "logicalID": 2, "dataTypeID": 3}}, {"id": "T2", "to": "T2", "sql": "WITH RECURSIVE   energy_hierarchy AS (     SELECT       e.supplytolabel AS orglabel,       e.supplytoid AS orgid,       e.objectlabel AS monitoredlabel,       e.objectid AS monitoredid,       e.rate,       0 AS level,       '/' || e.supplytolabel || '[' || e.supplytoid || ']' AS path     FROM       energysupplyto e     WHERE       e.isdeleted IS NOT TRUE     UNION ALL     SELECT       mir.atype AS orglabel,       mir.aid AS orgid,       eh.monitoredlabel,       eh.monitoredid,       eh.rate,       eh.level + 1,       '/' || mir.atype || '[' || mir.aid || ']' || eh.path AS path     FROM       energy_hierarchy eh       JOIN model_instance_relationship mir ON eh.orglabel = mir.btype       AND eh.orgid = mir.bid     WHERE       NOT EXISTS (         SELECT           1         FROM           energysupplyto e         WHERE           e.supplytolabel = mir.atype           AND e.supplytoid = mir.aid           AND e.isdeleted IS NOT FALSE       )   ) SELECT   eh.orglabel,   eh.orgid,   eh.rate,   eh.level,   eh.path,   q.monitoredlabel,   q.monitoredid,   q.energytype,   qm.quantityobject_id,   qm.stationid,   qm.channelid,   qm.deviceid,   qm.logicalindex,   qm.dataid FROM   energy_hierarchy eh   JOIN quantityobject q ON q.monitoredlabel = eh.monitoredlabel   AND q.monitoredid = eh.monitoredid   JOIN quantityobjectmap qm ON q.id = qm.quantityobject_id   JOIN (     WITH       p AS (         SELECT           m.monitoredlabel,           m.monitoredid,           p.deviceid,           p.metertype,           CASE p.metertype             WHEN 9 THEN 1             WHEN 1 THEN 2             WHEN 4 THEN 3             WHEN 11 THEN 4             ELSE 5           END AS priority         FROM           pecdeviceextend p           JOIN measuredby m ON p.deviceid = m.measuredby       )     SELECT       p.monitoredlabel,       p.monitoredid,       p.deviceid     FROM       p       JOIN (         SELECT           monitoredlabel,           monitoredid,           MIN(priority) AS minpriority         FROM           p         GROUP BY           monitoredlabel,           monitoredid       ) mp ON p.monitoredlabel = mp.monitoredlabel       AND p.monitoredid = mp.monitoredid       AND p.priority = mp.minpriority   ) md ON q.monitoredlabel = md.monitoredlabel   AND q.monitoredid = md.monitoredid   AND qm.deviceid = md.deviceid WHERE   q.quantitycategory IN (5, 20, 21) ORDER BY   eh.orglabel,   eh.orgid,   q.monitoredlabel,   q.monitoredid;", "name": "业务维度配置输入算子", "type": "CfgSource", "nodeType": "source", "dataStream": "map", "modelLabel": "data-center-agg", "fieldsArray": {"path": "String", "rate": "double", "level": "int", "orgid": "long", "dataid": "long", "deviceid": "long", "orglabel": "String", "channelid": "long", "stationid": "long", "energytype": "int", "monitoredid": "long", "logicalindex": "int", "monitoredlabel": "String", "quantityobject_id": "long"}, "fieldsPosition": {"path": 9, "rate": 11, "level": 10, "orgid": 12, "dataid": 1, "deviceid": 0, "orglabel": 13, "channelid": 3, "stationid": 4, "energytype": 6, "monitoredid": 7, "logicalindex": 2, "monitoredlabel": 8, "quantityobject_id": 5}}], "resource": {"jobName": "zdsz", "jobmanager": "1024M", "parallelism": 1, "taskmanager": "1024M", "mode": "run"}}]