[{"sink": [{"id": "T00003", "from": ["T00002"], "name": "redis输出算子", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["deviceid", "dataid", "logicalindex", "stationid"], "dataStream": "map", "redisTableName": "abd"}], "trans": [{"id": "T00002", "to": "T00002", "from": ["T00001"], "name": "筛选算子", "type": "GeneralFilterFunction", "nodeType": "trans", "dataStream": "map", "fieldsArray": {"path": "String", "rate": "double", "level": "int", "orgid": "long", "dataid": "long", "deviceid": "long", "orglabel": "String", "channelid": "long", "stationid": "long", "energytype": "int", "monitoredid": "long", "logicalindex": "int", "monitoredlabel": "String", "quantityobject_id": "long"}, "filterTable": [{"field": "deviceid", "fieldType": "long", "description": "设备id", "frontedType": "number"}, {"field": "dataid", "fieldType": "long", "description": "数据id", "frontedType": "number"}, {"field": "logicalindex", "fieldType": "int", "description": "回路号id", "frontedType": "number"}, {"field": "channelid", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "totalFilter": "&&", "filterCondition": " deviceid != nil  && monitoredlabel == nil", "filterConditionArray": [{"rangeType": "", "inputValue": 7, "selectField": "deviceid", "selectFunctionName": "neq"}, {"rangeType": "", "inputValue": "", "selectField": "monitoredlabel", "selectFunctionName": "null"}]}], "source": [{"id": "T00001", "to": "T00001", "sql": "WITH RECURSIVE   energy_hierarchy AS (     SELECT       e.supplytolabel AS orglabel,       e.supplytoid AS orgid,       e.objectlabel AS monitoredlabel,       e.objectid AS monitoredid,       e.rate,       0 AS level,       '/' || e.supplytolabel || '[' || e.supplytoid || ']' as path     FROM       energysupplyto e     WHERE       e.isdeleted IS NOT TRUE     UNION ALL     SELECT       mir.atype AS orglabel,       mir.aid AS orgid,       eh.monitoredlabel,       eh.monitoredid,       eh.rate,       eh.level + 1,       '/' || mir.atype || '[' || mir.aid || ']' || eh.path as path     FROM       energy_hierarchy eh       JOIN model_instance_relationship mir ON eh.orglabel = mir.btype       AND eh.orgid = mir.bid     WHERE       NOT EXISTS (         SELECT           1         FROM           energysupplyto e         WHERE           e.supplytolabel = mir.atype           AND e.supplytoid = mir.aid           AND e.isdeleted IS NOT FALSE       )   ) SELECT   eh.orglabel,   eh.orgid,   eh.rate,   eh.level,   eh.path,   q.monitoredlabel,   q.monitoredid,   q.energytype,   qm.quantityobject_id,   qm.stationid,   qm.channelid,   qm.deviceid,   qm.logicalindex,   qm.dataid FROM   energy_hierarchy eh,   quantityobject q,   quantityobjectmap qm,   (     WITH       p AS (         SELECT           m.monitoredlabel,           m.monitoredid,           p.deviceid,           p.metertype,           CASE p.metertype             WHEN 9 THEN 1             WHEN 1 THEN 2             WHEN 4 THEN 3             WHEN 11 THEN 4             ELSE 5           END AS priority         FROM           pecdeviceextend p,           measuredby m         WHERE           p.deviceid = m.measuredby       )     SELECT       p.monitoredlabel,       p.monitoredid,       p.deviceid     FROM       p,       (         SELECT           monitoredlabel,           monitoredid,           MIN(priority) minpriority         FROM           p         GROUP BY           monitoredlabel,           monitoredid       ) mp     WHERE       p.monitoredlabel = mp.monitoredlabel       AND p.monitoredid = mp.monitoredid       AND p.priority = mp.minpriority   ) md where   q.monitoredlabel = eh.monitoredlabel   AND q.monitoredid = eh.monitoredid   and q.id = qm.quantityobject_id   and q.monitoredlabel = md.monitoredlabel   and q.monitoredid = md.monitoredid   and qm.deviceid = md.deviceid   and q.quantitycategory IN (5, 20, 21) ORDER BY   eh.orglabel,   eh.orgid,   q.monitoredlabel,   q.monitoredid;", "name": "业务维度配置输入算子", "type": "CfgSource", "nodeType": "source", "dataStream": "map", "modelLabel": "data-center-agg", "fieldsArray": {"path": "String", "rate": "double", "level": "int", "orgid": "long", "dataid": "long", "deviceid": "long", "orglabel": "String", "channelid": "long", "stationid": "long", "energytype": "int", "monitoredid": "long", "logicalindex": "int", "monitoredlabel": "String", "quantityobject_id": "long"}}], "resource": {"jobName": "filterTest", "jobmanager": "1024M", "parallelism": 1, "taskmanager": "1024M"}}]