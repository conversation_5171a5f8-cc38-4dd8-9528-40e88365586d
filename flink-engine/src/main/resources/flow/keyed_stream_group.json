[{"sink": [{"id": "T00039", "from": ["T00038"], "name": "redis输出算子", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm", "aggregationcycle"], "dataStream": "stream", "redisTableName": "aaa"}], "trans": [{"id": "T00038", "to": "T00038", "from": ["T00040"], "func": 4, "name": "分组汇总算子", "type": "StreamGroupByFunction", "groupBy": ["devID", "dataID", "tm"], "nodeType": "trans", "funcField": "val", "joinTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "fieldsPosition": {"devID": 0, "dataID": 1, "logicalID": 2, "dataTypeID": 3, "tm": 4, "val": 5, "status": 6}, "dataStream": "stream", "fieldsArray": {"devID": "int", "dataID": "int", "tm": "long", "vMin": "double", "aggregationcycle": "int"}, "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 7}], "resultField": "vMin"}], "source": [{"id": "T00040", "to": "T00040", "name": "定时记录输入算子", "type": "DatalogDataStreamSource", "nodeType": "source", "RecalcTime": 1732032000000, "dataStream": "stream", "fieldsArray": {"tm": "long", "val": "double", "devID": "int", "dataID": "int", "status": "byte", "logicalID": "short", "dataTypeID": "short"}, "latenessDay": 1}], "resource": {"jobName": "bbbb", "jobmanager": "1024M", "parallelism": 1, "taskmanager": "1024M", "mode": "run"}}]