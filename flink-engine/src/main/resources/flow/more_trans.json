[{"sink": [{"id": "T00006", "from": ["T00005"], "name": "redis输出算子", "type": "CommonRedisSink", "nodeType": "sink", "rediskey": ["devID", "dataID", "tm"], "dataStream": "stream", "filterTable": [{"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "T00004vAvg", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}], "redisTableName": "tableGDG"}], "trans": [{"id": "T00003", "to": "T00003", "from": ["T00001", "T00002"], "name": "join连接算子", "type": "StreamJoinFunction", "tableA": [{"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "tableB": [{"field": "deviceid", "label": "设备id（deviceid）", "fieldType": "long", "description": "设备id", "frontedType": "number"}, {"field": "dataid", "label": "数据id（dataid）", "fieldType": "long", "description": "数据id", "frontedType": "number"}, {"field": "logicalindex", "label": "回路号id（logicalindex）", "fieldType": "int", "description": "回路号id", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "streamA": "DatalogDataStreamSource", "streamB": "data-center-agg", "joinType": "leftjoin", "nodeType": "trans", "streamAM": "T00001", "streamBM": "T00002", "dataStream": "stream", "joinCondition": "a.devID = b.deviceid", "associationTableData": [{"selectedAField": "devID", "selectedBField": "deviceid"}]}, {"id": "T00004", "to": "T00004", "from": ["T00003"], "func": 11, "name": "分组汇总算子", "type": "StreamGroupByFunction", "groupBy": ["devID", "dataID", "tm"], "nodeType": "trans", "funcField": "dataID", "joinTable": [{"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "label": "回路号id（logicalID）", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "label": "数据类型id（dataTypeID）", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "label": "表码值（val）", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "label": "状态（status）", "fieldType": "byte", "description": "状态", "frontedType": "number"}, {"field": "dataid", "label": "数据id（dataid）", "fieldType": "long", "description": "数据id", "frontedType": "number"}, {"field": "logicalindex", "label": "回路号id（logicalindex）", "fieldType": "int", "description": "回路号id", "frontedType": "number"}, {"field": "channelid", "label": "通道id（channelid）", "fieldType": "long", "description": "通道id", "frontedType": "number"}, {"field": "stationid", "label": "厂站id（stationid）", "fieldType": "long", "description": "厂站id", "frontedType": "number"}, {"field": "quantityobject_id", "label": "物理量id（quantityobject_id）", "fieldType": "long", "description": "物理量id", "frontedType": "number"}, {"field": "energytype", "label": "能源类型（energytype）", "fieldType": "int", "description": "能源类型", "frontedType": "number"}, {"field": "<PERSON>id", "label": "管网id（monitoredid）", "fieldType": "long", "description": "管网id", "frontedType": "number"}, {"field": "monitoredlabel", "label": "管网标签（monitoredlabel）", "fieldType": "String", "description": "管网标签", "frontedType": "string"}, {"field": "path", "label": "路径（path）", "fieldType": "String", "description": "路径", "frontedType": "string"}, {"field": "level", "label": "层级（level）", "fieldType": "int", "description": "层级", "frontedType": "number"}, {"field": "rate", "label": "比例（rate）", "fieldType": "double", "description": "比例", "frontedType": "number"}, {"field": "orgid", "label": "组织层级id（orgid）", "fieldType": "long", "description": "组织层级id", "frontedType": "number"}, {"field": "orglabel", "label": "组织层级标签（orglabel）", "fieldType": "String", "description": "组织层级标签", "frontedType": "string"}], "dataStream": "stream", "periodField": [{"key": "date", "label": "tm分组", "value": "tm", "period": 14}]}, {"id": "T00005", "to": "T00005", "from": ["T00004"], "name": "筛选算子", "type": "GeneralFilterFunction", "nodeType": "trans", "dataStream": "stream", "filterTable": [{"field": "devID", "label": "设备id（devID）", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "label": "数据id（dataID）", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "tm", "label": "时间（tm）", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "T00004vAvg", "fieldType": "double", "description": "平均值", "frontedType": "number"}, {"field": "aggregationcycle", "fieldType": "int", "description": "聚合周期", "frontedType": "number"}], "totalFilter": "&&", "filterCondition": " devID == 25.000000  && tm == 1732677514000  && aggregationcycle == 35.000000", "filterConditionArray": [{"rangeType": "", "inputValue": "25.000000", "selectField": "devID", "selectFunctionName": "eq"}, {"rangeType": "", "inputValue": 1732677514000, "selectField": "tm", "selectFunctionName": "eq"}, {"rangeType": "", "inputValue": "35.000000", "selectField": "aggregationcycle", "selectFunctionName": "eq"}]}], "source": [{"id": "T00001", "to": "T00001", "name": "定时记录输入算子", "type": "DatalogDataStreamSource", "nodeType": "source", "RecalcTime": 1732032000000, "dataStream": "stream", "latenessDay": 5}, {"id": "T00002", "to": "T00002", "sql": "WITH RECURSIVE energy_hierarchy AS ( SELECT e.supplytolabel AS orglabel, e.supplytoid AS orgid, e.objectlabel AS monitoredlabel, e.objectid AS monitoredid, e.rate, 0 AS level, '/' || e.supplytolabel || '[' || e.supplytoid || ']' as path FROM energysupplyto e WHERE e.isdeleted IS NOT TRUE UNION ALL SELECT mir.atype AS orglabel, mir.aid AS orgid, eh.monitoredlabel, eh.monitoredid, eh.rate, eh.level + 1, '/' || mir.atype || '[' || mir.aid || ']' || eh.path as path FROM energy_hierarchy eh JOIN model_instance_relationship mir ON eh.orglabel = mir.btype AND eh.orgid = mir.bid WHERE NOT EXISTS ( SELECT 1 FROM energysupplyto e WHERE e.supplytolabel = mir.atype AND e.supplytoid = mir.aid AND e.isdeleted IS NOT FALSE ) ) SELECT eh.orglabel, eh.orgid, eh.rate, eh.level, eh.path, q.monitoredlabel, q.monitoredid, q.energytype, qm.quantityobject_id, qm.stationid, qm.channelid, qm.deviceid, qm.logicalindex, qm.dataid FROM energy_hierarchy eh, quantityobject q, quantityobjectmap qm, ( WITH p AS ( SELECT m.monitoredlabel, m.monitoredid, p.deviceid, p.metertype, CASE p.metertype WHEN 9 THEN 1 WHEN 1 THEN 2 WHEN 4 THEN 3 WHEN 11 THEN 4 ELSE 5 END AS priority FROM pecdeviceextend p, measuredby m WHERE p.deviceid = m.measuredby ) SELECT p.monitoredlabel, p.monitoredid, p.deviceid FROM p, ( SELECT monitoredlabel, monitoredid, MIN(priority) minpriority FROM p GROUP BY monitoredlabel, monitoredid ) mp WHERE p.monitoredlabel = mp.monitoredlabel AND p.monitoredid = mp.monitoredid AND p.priority = mp.minpriority ) md where q.monitoredlabel = eh.monitoredlabel AND q.monitoredid = eh.monitoredid and q.id = qm.quantityobject_id and q.monitoredlabel = md.monitoredlabel and q.monitoredid = md.monitoredid and qm.deviceid = md.deviceid and q.quantitycategory IN (5, 20, 21) ORDER BY eh.orglabel, eh.orgid, q.monitoredlabel, q.monitoredid;", "name": "业务维度配置输入算子", "type": "CfgSource", "nodeType": "source", "dataStream": "map", "modelLabel": "data-center-agg"}], "resource": {"jobName": "ceshi"}}]