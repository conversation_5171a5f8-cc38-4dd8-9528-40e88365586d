[{"sink": [], "trans": [{"id": "T00012", "to": "T00012", "from": ["T00006"], "name": "筛选算子", "type": "GeneralFilterFunction", "nodeType": "trans", "dataStream": "stream", "fieldsArray": {"tm": "long", "val": "double", "devID": "int", "dataID": "int", "status": "byte", "logicalID": "short", "dataTypeID": "short"}, "filterTable": [{"field": "devID", "fieldType": "int", "description": "设备id", "frontedType": "number"}, {"field": "dataID", "fieldType": "int", "description": "数据id", "frontedType": "number"}, {"field": "logicalID", "fieldType": "short", "description": "回路号id", "frontedType": "number"}, {"field": "dataTypeID", "fieldType": "short", "description": "数据类型id", "frontedType": "number"}, {"field": "tm", "fieldType": "long", "description": "时间", "frontedType": "date"}, {"field": "val", "fieldType": "double", "description": "表码值", "frontedType": "number"}, {"field": "status", "fieldType": "byte", "description": "状态", "frontedType": "number"}], "totalFilter": "&&", "filterCondition": " devID == 5.000000", "filterConditionArray": [{"rangeType": "", "inputValue": "5.000000", "selectField": "devID", "selectFunctionName": "eq"}]}], "source": [{"id": "T00006", "to": "T00006", "name": "定时记录输入算子", "type": "DatalogDataStreamSource", "nodeType": "source", "RecalcTime": 1731945600000, "dataStream": "stream", "fieldsArray": {"tm": "long", "val": "double", "devID": "int", "dataID": "int", "status": "byte", "logicalID": "short", "dataTypeID": "short"}, "latenessDay": 2}], "resource": {"jobName": "bbbb", "jobmanager": "1024M", "parallelism": 1, "taskmanager": "1024M"}}]