package com.cet.engine.model;

import com.cet.fbp.common.bean.DatalogVal;

import java.io.Serializable;

public class DifferenceLog implements Serializable {
    private ExceptionLog exceptionLog;
    private DatalogVal priorDtVal;    // 前表码值
    private DatalogVal latterDtVal;   // 后表码值

    // 构造方法
    public DifferenceLog(ExceptionLog exceptionLog, DatalogVal priorDtVal, DatalogVal latterDtVal) {
        this.exceptionLog = exceptionLog;
        this.priorDtVal = priorDtVal;
        this.latterDtVal = latterDtVal;
    }

    // Getter和Setter方法
    public ExceptionLog getExceptionLog() {
        return exceptionLog;
    }

    public void setExceptionLog(ExceptionLog exceptionLog) {
        this.exceptionLog = exceptionLog;
    }

    public DatalogVal getPriorDtVal() {
        return priorDtVal;
    }

    public void setPriorDtVal(DatalogVal priorDtVal) {
        this.priorDtVal = priorDtVal;
    }

    public DatalogVal getLatterDtVal() {
        return latterDtVal;
    }

    public void setLatterDtVal(DatalogVal latterDtVal) {
        this.latterDtVal = latterDtVal;
    }

    @Override
    public String toString() {
        return "DifferenceLog{" +
                "exceptionLog=" + exceptionLog +
                ", priorDtVal=" + priorDtVal +
                ", latterDtVal=" + latterDtVal +
                '}';
    }
}

