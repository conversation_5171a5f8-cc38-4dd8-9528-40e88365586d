package com.cet.engine.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class FieldSetting implements Serializable {

    private String id;

    private String name;

    private List<String> from;

    private List<FormulaField> fieldTable;

    private LinkedHashMap<String, String> fieldsArray;
}
