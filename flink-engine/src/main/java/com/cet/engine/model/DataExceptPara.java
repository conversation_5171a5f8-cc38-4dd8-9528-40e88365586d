package com.cet.engine.model;

import java.io.Serializable;

public class DataExceptPara implements Serializable {
    private Double turnOVerEng;//翻转值
    private Double ratedPower;//额定功率
    private boolean bFixedEmpirical;//是否固定经验值
    private Double maxDifEng;//经验值
    private Double maxMultiEng;//欧式距离倍数
    private Double ignoreDifEng;//忽略经验值
    private Double limitDifEng;//两点间最大差值
    private int cacheDtlogNum;//异常判断缓存个数
    private int dtlogNum4CheckEngDown;//重算时后续下降点判断个数

    public DataExceptPara() {
        turnOVerEng = 999999999.0;
        ratedPower = null;
        bFixedEmpirical = false;
        maxDifEng=500.0;
        maxMultiEng = 10.0;
        ignoreDifEng = 1.0;
        limitDifEng = 100000.0;
        cacheDtlogNum = 288;
        dtlogNum4CheckEngDown = 12;
    }

    public Double getTurnOVerEng() {
        return turnOVerEng;
    }

    public void setTurnOVerEng(Double turnOVerEng) {
        this.turnOVerEng = turnOVerEng;
    }

    public Double getRatedPower() {
        return ratedPower;
    }

    public void setRatedPower(Double ratedPower) {
        this.ratedPower = ratedPower;
    }

    public boolean isbFixedEmpirical() {
        return bFixedEmpirical;
    }

    public void setbFixedEmpirical(boolean bFixedEmpirical) {
        this.bFixedEmpirical = bFixedEmpirical;
    }

    public Double getMaxDifEng() {
        return maxDifEng;
    }

    public void setMaxDifEng(Double maxDifEng) {
        this.maxDifEng = maxDifEng;
    }

    public Double getMaxMultiEng() {
        return maxMultiEng;
    }

    public void setMaxMultiEng(Double maxMultiEng) {
        this.maxMultiEng = maxMultiEng;
    }

    public Double getIgnoreDifEng() {
        return ignoreDifEng;
    }

    public void setIgnoreDifEng(Double ignoreDifEng) {
        this.ignoreDifEng = ignoreDifEng;
    }

    public Double getLimitDifEng() {
        return limitDifEng;
    }

    public void setLimitDifEng(Double limitDifEng) {
        this.limitDifEng = limitDifEng;
    }

    public int getCacheDtlogNum() {
        return cacheDtlogNum;
    }

    public void setCacheDtlogNum(int cacheDtlogNum) {
        this.cacheDtlogNum = cacheDtlogNum;
    }

    public int getDtlogNum4CheckEngDown() {
        return dtlogNum4CheckEngDown;
    }

    public void setDtlogNum4CheckEngDown(int dtlogNum4CheckEngDown) {
        this.dtlogNum4CheckEngDown = dtlogNum4CheckEngDown;
    }
}

