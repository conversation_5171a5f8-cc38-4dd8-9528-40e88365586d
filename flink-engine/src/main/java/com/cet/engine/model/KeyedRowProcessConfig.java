package com.cet.engine.model;

import com.cet.engine.plugin.base.flink.transform.GeneralPeriodAggregator;
import com.cet.engine.plugin.base.flink.transform.PeriodAggregator;
import com.cet.engine.utils.UnnaturalSet;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class KeyedRowProcessConfig implements Serializable {
    /**
     * 分组字段列表
     */
    private List<String> groupBy;
    private List<String> keyFields;
    /**
     * 分组汇总字段，如：val
     */
    private String funcField;
    /**
     * 子分组字段列表
     */
    private List<String> subFields;

    private AggregationCycleEnum aggregationCycle;

    private PeriodField periodField;

    private List<GeneralPeriodAggregator> aggregators;

    private UnnaturalSet unnaturalSet = new UnnaturalSet();

    private Integer latenessHour = 48;
    /**
     * 输出字段列表
     */
    private LinkedHashMap<String, String> fieldsArray;

    public List<String> getValueFields() {
        List<String> valueFields = new ArrayList<>();
        for(PeriodAggregator aggregator : aggregators){
            valueFields.add(aggregator.getFieldName());
        }
        return valueFields;
    }
}
