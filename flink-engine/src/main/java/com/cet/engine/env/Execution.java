package com.cet.engine.env;

import com.cet.engine.apis.BaseSink;
import com.cet.engine.apis.BaseSource;
import com.cet.engine.apis.BaseTransform;
import com.cet.engine.enums.RunModel;
import com.cet.engine.plugin.Plugin;

import java.util.List;

public interface Execution<SR extends BaseSource, TF extends BaseTransform, SK extends BaseSink> extends Plugin<Void> {
    void start(List<SR> sources, List<TF> transforms, List<SK> sinks, RunModel runModel) throws Exception;
}
