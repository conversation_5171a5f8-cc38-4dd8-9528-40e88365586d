package com.cet.engine.common;

public class ConstDef {
    public static final String CommonVerInfo= "Common Version=1.0.115";

    public static final long NA = 0x80000000;//PecStar系统无效值
    public static final long MAX_UNIX_TIME = 4070880000000L;//13位数 2099-1-1

    public static final int DATACACHE_ACCESS_WIN = 0x80000000 | 0x40000000;
    public static final int DATACACHE_ACCESS_LINUX = 0x0002;  // open for reading and writing
    public static final int DATACACHE_IDX_FILE_LEN = 32 * 1024 * 1024;
    public static final int DATACACHE_DAT_FILE_LEN = 128 * 1024 * 1024;
    /**
     * peccore常规缓存类型
     */
    public static final short CACHE_TYPE_PECCORE = 1;
    /**
     * FLINK缓存数据类型
     */
    public static final short CACHE_TYPE_FLINK = 2;

    /**
     * 查询datacache定时记录文件索引信息时的globalFileType
     */
    public static final short GLOBAL_FILE_TYPE_DATALOG = 4;
    /**
     * 波形缓存数据
     */
    public static final int DATACACHE_TYPE_WFORMLOG = 7;
    public static final int DATACACHE_TYPE_DATALOG_NORMAL = 7; ///< 普通定时记录类型
    public static final int DATACACHE_TYPE_DATALOG_HIGHSPEED = 8; ///< 高速定时记录类型
    public static final int DATACACHE_TYPE_DATALOG_SECOND = 9; ///< 秒级定时记录类型
    public static final int DATACACHE_NOERROR = 0;
    public static final int DATALOG_READ_NUM = 500;

    public static final int ENERGY_DATA_ID = 4000004;//正向有功电能DataID=4000004
    public static final int ENERGY1_DATA_ID = 4000008;//反向有功电能DataID=4000008
    public static final int ENERGY2_DATA_ID = 4000020;//正向无功电能DataID=4000020
    public static final int ENERGY3_DATA_ID = 10000001;//能源累积量DataID=10000001

    public static final int DEMAND_DATA_ID = 7000017;//需量DataID=7000017

    public static final byte STATUS_LINEAR_COMPENSATION = 20;//线性补点
    public static final byte STATUS_EXCEPT_RANGE = 21;//超出量程异常
    public static final byte STATUS_EXCEPT_DOWN = 22;//后续点下降异常
    public static final byte STATUS_EXCEPT_LIMIT = 23;//超出两点限值异常
    public static final byte STATUS_EXCEPT_EUCLIDEAN_DISTANCE = 24;//超出欧式距离异常
    public static final byte STATUS_AUTO_CHANGE_METER = 25;//自动换表
    public static final byte STATUS_MANUAL_SET_VAL = 30;//手动设值
    public static final byte STATUS_MANUAL_CHANGE_METER = 31;//手动换表

    //按照Matterhorn模型库给的定义来定义周期名称和值
    public static final String[] PeriodName = {"","1Sec","3Sec","1Min","5Min","10Min","30Min","Hour","2Hour","3Hour","6Hour","12Hour","Day","Week","Month","Quarter","6Month","Year","15Min"};
    public static final int PERIOD_MINUTE = 3;
    public static final int PERIOD_HOUR = 7;
    public static final int PERIOD_DAY = 12;
    public static final int PERIOD_WEEK = 13;
    public static final int PERIOD_MONTH = 14;
    public static final int PERIOD_QUARTER = 15;
    public static final int PERIOD_YEAR = 17;
    public static final int[] PERIOD_ARRAY = {PERIOD_HOUR,PERIOD_DAY,PERIOD_WEEK,PERIOD_MONTH,PERIOD_QUARTER,PERIOD_YEAR};

    public static final int PERIOD_BELONG_START = 1;
    public static final int PERIOD_BELONG_END = 2;

    public static final int AGGREGATION_TYPE_ACC = 10;//累加

    public static final int DELAY_INTERVAL=300000;//300秒

    public static final double MaxMeterFullValue = 999999999.0;//表码满度值9个9
    public static final double TurnoverValue = 10.0;//电度翻转判断值， 差负10度才认为表码翻转

    public static final int AlarmGrade = 1;	//普通报警等级
    public static final int SeriousAlarmGrade = 2;//严重报警等级

    public static final int ClearBalancePara = 1002;//清除余额遥控参数号
    public static final int ClearBillPara = 1008;//清除预付费表计账单参数号
    //public static final int BalanceDataID = 12010001;//账户余额DataID
    //public static final int OverBalanceDataID = 12010002;//账户透支余额DataID

    public static final int BalanceParaID = 10001;//账户余额ParaID
    public static final int OverBalanceID = 10002;//账户透支金额ParaID
    public static final int OverBalanceParaID = 10008;//账户透支额度ParaID
    public static final int StatusMeasureID = 4;//状态measureID

    public static final int FeeDataID = 6004067;//账单中费用DataID
    public static final int FeeDataTypeID = 1;//本月费用DataTypeID
    public static final int LastFeeDataTypeID = 3001;//上月费用DataTypeID
    //public static final int EnergyDataID = 4000429;//账单中电能DataID
    public static final int EnergyDataTypeID = 1;//本月电能DataTypeID
    public static final int LastEnergyDataTypeID = 3001;//上月电能DataID

    //public static final int LastFeeDataID = 6004067;//上月费用DataID
    //public static final int LastEnergyDataID = 4000004;

    //public static final int BeforeRechargeDataID = 6004039;//充值前余额DataID
    public static final int RechargeDataID = 6004038;//充值金额DataID
    //public static final int BeforeWriteBackDataID = 6004045;//冲红前余额DataID
    public static final int WriteBackDataID = 6004044;//冲红DataID

    public static final int BeginOfMonth = 1;
    public static final int EndOfMonth = 2;

    public static final int NumOfUpdateCharge = 2500;
    public static final int NumOfDeductFee = 2500;
    public static final int NumOfDeductSubsidy4Pre = 2500;
    public static final int NumOfDeductSubsidy4After = 2500;
    public static final int NumOfGenerateSubBill = 5000;
    public static final int NumOfGenerateBill = 2500;
    public static final int NumOfCalcOverduefine = 2500;
    public static final int NumOfNotice = 2500;

    public static final String CalcFileLock = "calc.pid";
    public static final String PayFileLock = "pay.pid";

    public static final int ApportionType = 2;//计算动态分摊标识
	
	public static final byte  LOADCLASS_IT = 2;
    public static final byte  LOADCLASS_AIRCONDITION = 3;
    public static final byte  LOADCLASS_ILLUMINATION = 4;
    public static final byte  LOADCLASS_OTHER = 5;
    public static final byte  LOADCLASS_NETWORK = 6;
	
	public static final byte ROOMTYPE_IT = 2;
	public static final byte ROOMTYPE_NETWORK = 7;

    /**
     * 设备数据服务查询时要求的时间格式
     */
    public static final String LONG_TIME = "yyyy-MM-dd HH:mm:ss";

    public static final String DATACACHE_TEST_URL = "/Cet/DataCache/TestServer";
    //查询staID、chnID配置接口
    public static final String DATALOG_STAIDCHNIDS_URL = "/Cet/DataCache/QueryStaIDChnIDs";
    //新的定时记录获取索引数量查询接口
    public static final String DATALOG_CACHEGLOBALDATA_URL = "/Cet/DataCache/QueryDataLogCacheGlobalData";

	// 查询缓存更新时间
    public static final String DATALOG_UPDATE_TIME_URL = "/Cet/DataCache/QueryDataLogUpdateTime";
    // 查询定时记录获取索引数量查询接口
    public static final String DATALOG_IDX_COUNT_URL = "/Cet/DataCache/QueryDataLogIdxCount";
    // 定时记录顺序查询
    public static final String DATALOG_SEQUENCE_URL = "/Cet/DataCache/QuerySequenceDataLog";
    // 定时记录查询
    public static final String DATALOG_DATACACHE_URL = "/Cet/DataCache/QueryDataLog";

    //数据中心业务根节点名称常量
    public static final String DCBASE = "dcbase";
    /**
     * 二进制类型返回值（通过datacachelibsrv查询的返回值类型）
     */
    public static final Integer DATACACHE_BINARY_RESULT = 1;
    /**
     * JSON类型返回值（通过datacachelibsrv查询的返回值类型）
     */
    public static final Integer DATACACHE_JSON_RESULT = 2;

    /**
     * 指定查询定时记录
     */
    public static final Integer DATALOG_TYPE = 7;

    public static final Integer PUE_ENG_FLAG_TOTAL = 0;//PUE总能耗标志
    public static final Integer PUE_ENG_FLAG_SUB = 1;//PUE分项能耗标志
    public static final Integer PUE_ENG_FLAG_ROOMTYPE2OR7 = 2;//PUE总IT和Network能耗标志
    public static final Integer PUE_ENG_FLAG_PCLF = 3;//PUE的plf和clf能耗标志


    public static final String APPLICATION_JSON = "application/json";
    public static final String UTF8 = "utf-8";

}
