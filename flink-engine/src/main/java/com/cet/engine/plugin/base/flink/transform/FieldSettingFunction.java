package com.cet.engine.plugin.base.flink.transform;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.cet.engine.common.CheckResult;
import com.cet.engine.common.Const;
import com.cet.engine.enums.ExpressionType;
import com.cet.engine.flink.FlinkEnvironment;
import com.cet.engine.flink.stream.FlinkStreamTransform;
import com.cet.engine.flink.util.SchemaUtil;
import com.cet.engine.model.FieldNode;
import com.cet.engine.model.FieldSetting;
import com.cet.engine.model.FormulaField;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.types.RowUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class FieldSettingFunction implements FlinkStreamTransform<Row, Row> {
    private JSONObject config;
    private JSONObject schemaInfo;
    private FieldSetting fieldSetting = new FieldSetting();

    @Getter
    private final Map<String, FieldNode> fieldNodeMap = new HashMap<>();

    public static Pattern VARIABLE_PATTERN = Pattern.compile("\\$([^#]*)#");

    public static Pattern ADJACENT_VARIABLE_PATTERN = Pattern.compile("\\$([^#]*)#\\$([^#]*)#");

    @Override
    public DataStream<Row> processStream(FlinkEnvironment env, Map<String, DataStream<Row>> dataStreams) {
        if (MapUtils.isEmpty(dataStreams)) {
            return null;
        }
        TypeInformation<Row> typeInfo = SchemaUtil.getTypeInformation(schemaInfo);
        SingleOutputStreamOperator<Row> stream = dataStreams.values().iterator().next().map(value -> value);
        return stream.map(createRichMapFunction(this.fieldSetting))
                .uid(this.fieldSetting.getId())
                .name(this.fieldSetting.getId())
                .returns(typeInfo);
    }

    public RichMapFunction<Row, Row> createRichMapFunction(final FieldSetting fieldConfig) {
        return new RichMapFunction<Row, Row>() {
            @Override
            public Row map(Row r) {
                Set<String> fieldNames = r.getFieldNames(true);
                if (CollectionUtils.isEmpty(fieldNames)) {
                    return r;
                }
                if (MapUtils.isEmpty(fieldConfig.getFieldsArray())) {
                    return r;
                }
                Row row = Row.withNames();
                Map<String, Object> rowFieldValue = getRefFieldValueMapFromRow(r, fieldNames, fieldNodeMap);
                for (Map.Entry<String, String> kvp : fieldConfig.getFieldsArray().entrySet()) {
                    FieldNode fieldNode = fieldNodeMap.get(kvp.getKey());
                    if (fieldNode != null) {
                        Object value = evaluateNestedExpression(fieldNodeMap.get(kvp.getKey()), rowFieldValue);
                        if (value != null) {
                            row.setField(kvp.getKey(), NumberUtils.createDouble(value.toString()));
                        }
                    } else {
                        if (fieldNames.contains(kvp.getKey())) {
                            row.setField(kvp.getKey(), r.getField(kvp.getKey()));
                        }
                    }
                }
                return row;
            }
        };
    }

    private Map<String, Object> getRefFieldValueMapFromRow(Row r, Set<String> fieldNames, Map<String, FieldNode> fieldNodeMap) {
        Map<String, Object> rowFieldValue = Maps.newHashMap();
        fieldNodeMap.forEach((key, value) -> {
            value.getRefFields().forEach(refField -> {
                if (!fieldNodeMap.containsKey(refField) && fieldNames.contains(refField)) {
                    Object fieldValue = r.getField(refField);
                    rowFieldValue.put(refField, fieldValue);
                }
            });
        });

        return rowFieldValue;
    }

    private Object evaluateFieldValue(Map<String, Object> fieldValueMap, FieldNode fieldNode) {
        Map<String, Object> variableMap;
        Object value = null;
        if (Objects.equals(ExpressionType.NUMBER.getType(), fieldNode.getFormulaField().getFrontedType())) {
            variableMap = getNumberVariableMap(fieldValueMap, fieldNode.getRefFields());
            if (fieldNode.getRefFields().size() == variableMap.size()) {
                value = this.evaluateNumericalExpression(variableMap, fieldNode.getExpression());
                variableMap.put(fieldNode.getFormulaField().getField(), value);
            } else {
                log.error("not enough variables:{} for expression:{}", variableMap.keySet(), fieldNode.getExpression());
            }
        } else {
            log.error("not support expressionType:{}, expression:{}", fieldNode.getFormulaField().getFieldType(), fieldNode.getExpression());
        }
        return value;
    }

    public Object evaluateNestedExpression(
            FieldNode fieldNode,
            Map<String, Object> fieldValueMap) {
        if (fieldValueMap.containsKey(fieldNode.getFormulaField().getField())) {
            return fieldValueMap.get(fieldNode.getFormulaField().getField());
        }
        Object value;
        for (FieldNode child : fieldNode.getChildren()) {
            if (fieldValueMap.containsKey(child.getFormulaField().getField())) {
                continue;
            }
            boolean hasVariable = child.getRefFields().stream().allMatch(fieldValueMap::containsKey);
            if (CollectionUtils.isEmpty(child.getChildren()) || hasVariable) {
                value = evaluateFieldValue(fieldValueMap, child);
                fieldValueMap.put(child.getFormulaField().getField(), value);
            } else {
                evaluateNestedExpression(child, fieldValueMap);
            }
        }
        value = evaluateFieldValue(fieldValueMap, fieldNode);
        fieldValueMap.put(fieldNode.getFormulaField().getField(), value);

        return value;
    }

    private Map<String, Object> getNumberVariableMap(Map<String, Object> fieldValueMap, List<String> variableNames) {
        Map<String, Object> variables = Maps.newHashMapWithExpectedSize(variableNames.size());
        if (CollectionUtils.isEmpty(variableNames)) {
            return variables;
        }
        for (String variableName : variableNames) {
            Object obj = fieldValueMap.get(variableName);
            if (obj == null) {
                log.error("the field:{} value is null", variableName);
            } else {
                if (obj instanceof Number) {
                    variables.put(variableName, obj);
                } else {
                    log.error("the field:{} value:{} is not number", variableName, obj);
                }
            }
        }
        return variables;
    }

    private List<String> parseVariables(String expression) {
        List<String> variables = new ArrayList<>();
        Matcher matcher = VARIABLE_PATTERN.matcher(expression);
        String variable;
        while (matcher.find()) {
            variable = matcher.group();
            variable = variable.replaceAll("[$#]", "");
            if (StringUtils.isNotBlank(variable) && !variables.contains(variable)) {
                variables.add(variable);
            }
        }
        return variables;
    }

    public Object evaluateNumericalExpression(Map<String, Object> variableMap, String expression) {
        try {
            return AviatorEvaluator.execute(expression, variableMap);
        } catch (Exception ex) {
            log.error("evaluate numerical expression failed", ex);
        }

        return null;
    }

    public CheckResult checkExpression(String expression) {
        CheckResult checkResult = new CheckResult(true, "");
        try {
            AviatorEvaluator.compile(expression);
        } catch (Exception ex) {
            checkResult.setMsg(ex.getMessage());
            checkResult.setSuccess(false);
        }

        return checkResult;
    }

    @Override
    public void setConfig(JSONObject config) {
        this.config = config;
    }

    @Override
    public JSONObject getConfig() {
        return this.config;
    }

    @Override
    public CheckResult checkConfig() {
        CheckResult checkResult = parseConfig(this.config);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        if (CollectionUtils.isEmpty(this.fieldSetting.getFieldTable())) {
            return checkResult;
        }
        List<String> types = Lists.newArrayList(ExpressionType.values()).stream().map(ExpressionType::getType).collect(Collectors.toList());
        for (Map.Entry<String, FieldNode> kvp: this.fieldNodeMap.entrySet()) {
            if (!types.contains(kvp.getValue().getFormulaField().getFrontedType())) {
                checkResult.setSuccess(false);
                checkResult.setMsg(String.format("not support field:%s formula type:%s",
                        kvp.getValue().getFormulaField().getField(), kvp.getValue().getFormulaField().getFrontedType()));
            }
            CheckResult checkExp = this.checkExpression(kvp.getValue().getExpression());
            if (!checkExp.isSuccess()) {
                return checkExp;
            }
        }

        return checkResult;
    }

    @Override
    public void prepare(FlinkEnvironment prepareEnv) {
        schemaInfo = JSONObject.parseObject(config.getString(Const.FIELDS_ARRAY), Feature.OrderedField);
    }

    private CheckResult parseConfig(JSONObject jsonObject) {
        CheckResult checkResult = new CheckResult(true, "");
        try {
            this.fieldSetting = jsonObject.toJavaObject(FieldSetting.class);
            if (CollectionUtils.isEmpty(this.fieldSetting.getFieldTable())) {
                return checkResult;
            }
            for (FormulaField f: this.fieldSetting.getFieldTable()) {
                if (StringUtils.isEmpty(f.getFormula())) {
                    continue;
                }
                FieldNode fieldNode = new FieldNode();
                fieldNode.setFormulaField(f);
                fieldNode.setRefFields(parseVariables(f.getFormula()));
                fieldNode.setExpression(fieldNode.getFormulaField().getFormula().replaceAll("[$#]", ""));
                this.fieldNodeMap.put(f.getField(), fieldNode);
            }
            this.fieldNodeMap.forEach((k, v) -> {
                if (CollectionUtils.isNotEmpty(v.getRefFields())) {
                    v.getRefFields().forEach(f -> {
                        this.fieldNodeMap.computeIfPresent(f, (field, node) -> {
                            v.getChildren().add(node);
                            return node;
                        });
                    });
                }
            });
            for (Map.Entry<String, FieldNode> entry : this.fieldNodeMap.entrySet()) {
                if (this.hasAdjacentVariables(entry.getValue().getFormulaField().getFormula())) {
                    checkResult.setMsg("adjacent variables not allowed: " + entry.getValue().getFormulaField().getFormula());
                    checkResult.setSuccess(false);
                    return checkResult;
                }
                if (checkCircularRef(entry.getValue(), null)) {
                    checkResult.setSuccess(false);
                    checkResult.setMsg(String.format("the field:%s formula has circular references:%s",
                            entry.getKey(), entry.getValue().getFormulaField().getFormula()));
                    return checkResult;
                }
            }
        } catch (Exception e) {
            checkResult.setSuccess(false);
            checkResult.setMsg(e.getMessage());
        }

        return checkResult;
    }

    public boolean checkCircularRef(FieldNode fieldNode, List<FieldNode> parentNodes) {
        if (CollectionUtils.isNotEmpty(parentNodes) && parentNodes.contains(fieldNode)) {
            return true;
        } else {
            for (FieldNode child : fieldNode.getChildren()) {
                List<FieldNode> newParentNodes = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(parentNodes)) {
                    newParentNodes.addAll(parentNodes);
                }
                newParentNodes.add(fieldNode);
                boolean checkResult = checkCircularRef(child, newParentNodes);
                if (checkResult) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean hasAdjacentVariables(String formula) {
        return ADJACENT_VARIABLE_PATTERN.matcher(formula).find();
    }

    public CheckResult checkOperatorConfig(JSONObject operatorConfig) {
        this.config = operatorConfig;
        return this.checkConfig();
    }
}
