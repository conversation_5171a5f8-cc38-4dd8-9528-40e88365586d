package com.cet.engine.plugin.base.flink.source;

import com.alibaba.fastjson.JSONObject;
import com.cet.engine.common.CheckResult;
import com.cet.engine.common.Const;
import com.cet.engine.flink.FlinkEnvironment;
import com.cet.engine.flink.stream.FlinkStreamSource;
import com.cet.engine.utils.CheckConfigUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.KafkaAdminClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Properties;
import java.util.Set;

public class KafkaStreamSource implements FlinkStreamSource<Row> {
    private static final Logger log = LoggerFactory.getLogger(KafkaStreamSource.class);
    private JSONObject config;


    private static final String TOPICS = "topics";
    private static final String SCHEMA = "schema";
    private static final String SOURCE_FORMAT = "format.type";
    private static final String GROUP_ID = "group.id";
    private static final String BOOTSTRAP_SERVERS = "bootstrap.servers";
    private static final String OFFSET_RESET = "offset.reset";
    private static final String COMMIT_INTERVAL = "auto.commit.interval.ms";

    @Override
    public Integer getParallelism() {
        // 默认为1,
        return config.getInteger(Const.PARALLELISM) == null ? 1 : config.getInteger(Const.PARALLELISM);
    }

    @Override
    public String getName() {
        return StringUtils.isEmpty(config.getString(Const.NAME)) ? config.getString(Const.PLUGIN_NAME) : config.getString(Const.NAME);
    }

    @Override
    public DataStream<Row> getStreamData(FlinkEnvironment env) throws Exception {
        DataStream<Row> rowStream = null;

        //TODO 2）创建数据源
        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers", config.getString(BOOTSTRAP_SERVERS));
        properties.setProperty("group.id", config.getString(GROUP_ID));
        properties.setProperty("flink.partition-discovery.interval-millis", config.getString(COMMIT_INTERVAL));
        properties.setProperty("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.setProperty("value.deserializer", "org.apache.kafka.common.serialization.ByteArraySerializer");
        properties.setProperty("auto.offset.reset", config.getString(OFFSET_RESET));
        properties.setProperty("enable.auto.commit", "true");
//        properties.setProperty("compression.type", "lz4");
        String topic = config.getString(TOPICS);
        if (checkTopic(topic, properties)) {

            FlinkKafkaConsumer<Row> consumer = new FlinkKafkaConsumer<>(topic, new Deserialization(config), properties);
            consumer.setStartFromEarliest();
            DataStreamSource<Row> stream= env.getStreamExecutionEnvironment().addSource(consumer);
            rowStream =  stream.flatMap(new FlatMapFunction<Row, Row>() {

                @Override
                public void flatMap(Row s, Collector<Row> collector) throws Exception {
                    if (!s.toString().isEmpty()) {
                        collector.collect(s);
                    }
                }
            });

        }
        return rowStream;

    }

    @Override
    public void setConfig(JSONObject config) {
        this.config = config;
    }

    @Override
    public JSONObject getConfig() {
        return config;
    }

    @Override
    public CheckResult checkConfig() {
        return CheckConfigUtil.check(config, TOPICS, SCHEMA, SOURCE_FORMAT, RESULT_TABLE_NAME);
    }

    @Override
    public void prepare(FlinkEnvironment prepareEnv) {

    }

    private static boolean checkTopic(String topic, Properties props) throws Exception {
        // 创建 AdminClient 对象
        AdminClient client = KafkaAdminClient.create(props);

        // 获取 topic 列表
        Set<String> topics = client.listTopics().names().get();
        System.out.println("topics exist:" + topics);

        ArrayList<String> ts = new ArrayList<>();
        ts.add(topic);

        if (!topics.containsAll(ts)) {
            log.warn("topic: " + topic + " not exist! all topics = " + topics);
            return false;
        }

        client.close();
        return true;
    }
}
