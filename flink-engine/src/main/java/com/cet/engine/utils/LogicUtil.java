package com.cet.engine.utils;

import java.util.List;

/**
 * 业务逻辑方法类
 */
public class LogicUtil {

    /**
     * 判断数据完整性
     * @param lastIndexOfHour 小时内最后一个元素的序号
     * @param valueArrays 值数组
     * @param isDelay 是否过期
     * @return 状态值，0-不完整，1-完整， 2-不完整且过期，3-完整且过期
     */
    public static byte getStatus(int lastIndexOfHour, int hourElementCount, List<double[]> valueArrays, boolean isDelay) {
        byte status = 1;
        for(int j = lastIndexOfHour - hourElementCount + 1; j <= lastIndexOfHour; j++){
            for (double[] valueArray : valueArrays) {
                if (Double.isNaN(valueArray[j])){
                    status = 0;
                    break;
                }
            }
        }
        //数据过期判断，第二位为0-未过期（0,1），第二位为1-过期（2,3）
        if(isDelay)
            status = (byte)(status | 2);
        return status;
    }



    public static int getCompletElementCount(List<double[]> valueArrays, int elementCountOfPeriod){
        for(int j = 0; j < valueArrays.get(0).length; j++){
            for (double[] valueArray : valueArrays) {
                if (Double.isNaN(valueArray[j])){
                    return j/elementCountOfPeriod*elementCountOfPeriod; //每次清理一个小时数据
                }
            }
        }
        return valueArrays.get(0).length;
    }

}
