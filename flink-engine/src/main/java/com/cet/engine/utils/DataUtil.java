package com.cet.engine.utils;

import com.alibaba.fastjson.JSONArray;
import com.cet.fbp.common.util.PublicFunc;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class DataUtil {
    public static void main(String[] args) {
        String expression = "((b*5-dd?/|)<Cc=10)";
        parseExpression(expression);
        String v = roundToDecimalPlaces(1.050505550101, 6);
        double v1 = Double.parseDouble(v);
    }

    public static void parseExpression(String expression){
        ArrayList<String> list = new ArrayList<>();
        // > < = + - * / % # ^ & ?
        String[] split = expression.split("\\(|\\)|>|<|=|\\+|-|\\*|/|%|#|^|&|\\?");
        for (String str : split) {
            if (str.trim().matches("^[A-Za-z]+$")) {
//                System.out.println("str: " + str);
                list.add(str.trim());
            }
        }

    }

    public static Set<String> parseFilterInfo(JSONArray array){
        Set<String> result = new HashSet<>();
        for (int i = 0; i < array.size(); i++) {
            result.add(array.getJSONObject(i).getString("selectField"));
        }
        return result;
    }

    public static String roundToDecimalPlaces(double value, int places) {
        if (places < 0) {
            throw new IllegalArgumentException("Decimal places must be non-negative");
        }
        BigDecimal bd = new BigDecimal(String.valueOf(value));
        return bd.setScale(places, RoundingMode.HALF_UP).toString();
    }
}
