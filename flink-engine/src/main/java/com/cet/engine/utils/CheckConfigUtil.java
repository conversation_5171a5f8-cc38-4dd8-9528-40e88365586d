package com.cet.engine.utils;


import com.alibaba.fastjson.JSONObject;
import com.cet.engine.common.CheckResult;

public class CheckConfigUtil {

    public static CheckResult check(JSONObject config, String... params) {
        for (String param : params) {
            if (!config.containsKey(param)) {
                return new CheckResult(false, "please specify [" + param + "] as non-empty");
            }
        }
        return new CheckResult(true,"");
    }
}
