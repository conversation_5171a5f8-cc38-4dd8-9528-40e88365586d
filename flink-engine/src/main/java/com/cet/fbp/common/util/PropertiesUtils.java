package com.cet.fbp.common.util;

import java.io.File;
import java.util.Properties;

/**
 * 描述：配置文件读取工具类
 * 其他类型的数据请直接使用 {@link org.apache.flink.util.PropertiesUtil}
 *
 * <AUTHOR>
 * @date 2023/5/10
 */
public class PropertiesUtils {
    public static String getString(Properties propFile, String key, String defaultValue) {
        return propFile.getProperty(key);
    }

    public static String getPropertiesPath(String fileName){
        File directory = new File("");
        String binPath = directory.getAbsolutePath();
        File binDirectory = new File(binPath);
        File flinkDirectory = binDirectory.getParentFile();
        String pecfbpPath = String.format("%s\\conf\\%s",flinkDirectory.getAbsolutePath(),fileName);
        return pecfbpPath;
    }
}
