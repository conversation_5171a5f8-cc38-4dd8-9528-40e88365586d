package com.cet.fbp.common.bean;

import com.cet.fbp.common.define.ConstDef;
import com.cet.fbp.common.util.TimeFunc;

import java.util.Date;
import java.util.Objects;

public class SYSTEMTIME {
    public short wYear;/** 年 */
    public short wMonth;/** 月 */
    public short wDayOfWeek; /** 日(一周七天) 0~6 分别是周日、周一 ~ 周六*/
    public short wDay; /** 日(月) */
    public short wHour;  /** 小时 */
    public short wMinute;  /** 分钟 */
    public short wSecond;  /** 秒 */
    public short wMilliseconds;  /** 毫秒 */

    public SYSTEMTIME() {
        this.wYear = 0;
        this.wMonth = 0;
        this.wDayOfWeek = 0;
        this.wDay = 0;
        this.wHour = 0;
        this.wMinute = 0;
        this.wSecond = 0;
        this.wMilliseconds = 0;
    }

    public SYSTEMTIME(short wYear, short wMonth, short wDayOfWeek, short wDay, short wHour, short wMinute, short wSecond, short wMilliseconds) {
        this.wYear = wYear;
        this.wMonth = wMonth;
        this.wDayOfWeek = wDayOfWeek;
        this.wDay = wDay;
        this.wHour = wHour;
        this.wMinute = wMinute;
        this.wSecond = wSecond;
        this.wMilliseconds = wMilliseconds;
    }

    public SYSTEMTIME(SYSTEMTIME s1) {
        this.wYear = s1.wYear;
        this.wMonth = s1.wMonth;
        this.wDayOfWeek = s1.wDayOfWeek;
        this.wDay = s1.wDay;
        this.wHour = s1.wHour;
        this.wMinute = s1.wMinute;
        this.wSecond = s1.wSecond;
        this.wMilliseconds = s1.wMilliseconds;
    }


    public short getwDay() {
        return wDay;
    }


    public String toAlignStr(){
        return wYear +
                "-" + String.format("%02d", wMonth) +
                "-" + String.format("%02d", wDay) +
                " " + String.format("%02d", wHour) +
                ":" + String.format("%02d", wMinute) +
                ":" + String.format("%02d", wSecond);
    }


    @Override
    public String toString() {
        return wYear +
                "-" + wMonth +
                "-" + wDay +
                " " + wHour +
                ":" + wMinute +
                ":" + wSecond +
                "." + wMilliseconds;
                //+ " DayOfWeek" + wDayOfWeek;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SYSTEMTIME)) return false;
        SYSTEMTIME that = (SYSTEMTIME) o;
        return wYear == that.wYear && wMonth == that.wMonth && wDay == that.wDay && wHour == that.wHour
                && wMinute == that.wMinute && wSecond == that.wSecond && wMilliseconds == that.wMilliseconds;
    }

    @Override
    public int hashCode() {
        return Objects.hash(wYear, wMonth, wDay, wHour, wMinute, wSecond, wMilliseconds);
    }







    public void AddDifDay(int difDay){
        Date d = new Date(wYear-1900,wMonth-1, wDay);
        d.setTime(d.getTime()+ difDay*24*60*60*1000);//加差异天数
        wYear = (short)(d.getYear()+1900);
        wMonth = (short)(d.getMonth()+1);
        wDay = (short)d.getDate();
        short modDay = (short)(wDayOfWeek+difDay);
        while (modDay<0){
            modDay+=7;
        }
        modDay = (short)(modDay%7);
        wDayOfWeek = modDay;
    }


}
