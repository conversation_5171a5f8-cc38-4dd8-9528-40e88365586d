package com.cet.fbp.common.util;

import com.cet.fbp.common.frame.EncryptionSvr;
import com.cet.fbp.common.frame.FbpProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.Date;
import java.util.HashMap;
import java.util.Objects;


public class RedisUtil extends Singleton {
    protected static final Logger log = LoggerFactory.getLogger(RedisUtil.class);

    public static RedisUtil instance() {
        return (RedisUtil) getInstance(RedisUtil.class);
    }

    public static final int TIMEOUT = 3000;
    private JedisPool m_jedisPool;

    //上回连接断开的毫秒数，超过3秒再尝试重连
    private Long m_lastDisconnectMs = 0L;

    @Override
    public void Init() {
        try {
            String redisPwd = EncryptionSvr.instance().getRedisPwd();
            if (redisPwd.isEmpty()){
                redisPwd = FbpProperties.instance().getRedisPwd();
            }
            JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
            jedisPoolConfig.setMaxWaitMillis(TIMEOUT);
            jedisPoolConfig.setMaxTotal(100);
            jedisPoolConfig.setMaxIdle(100);
            m_jedisPool = new JedisPool(jedisPoolConfig,
                    FbpProperties.instance().getRedisHost(),
                    FbpProperties.instance().getRedisPort(), TIMEOUT,
                    redisPwd,2);
            log.info("RedisUtil get JedisPool succ, RedisHost={},RedisPort={}",FbpProperties.instance().getRedisHost(),
                    FbpProperties.instance().getRedisPort());
        } catch (Exception e) {
            log.error("RedisUtil get JedisPool failed, RedisHost={},RedisPort={} ",FbpProperties.instance().getRedisHost(),
                    FbpProperties.instance().getRedisPort(), e);
        }
    }

    /*
     * 从连接池中获取连接
     * */
    public Jedis GetJedis(){
        long nowMs = (new Date()).getTime();
        if (Math.abs(nowMs-m_lastDisconnectMs)<TIMEOUT) {
            return null;
        }
        return GetJedisDirect();
    }

    public Jedis GetJedisDirect(){
        Jedis jedis = null;
        try {
            jedis = m_jedisPool.getResource();
            jedis.select(3);
//            jedis.select(FbpProperties.instance().getDbNum());
            return jedis;
        } catch (Exception e) {
            log.error("GetJedis failed", e);
            UpdateDisconectMs();
            CloseJedis(jedis);
        }
        return null;
    }
    /*
     * 关闭资源
     * */
    public void CloseJedis(Jedis jedis){
        if(jedis!=null){
            jedis.close();
        }
    }

    public void UpdateDisconectMs(){
        Date now = new Date();
        m_lastDisconnectMs = now.getTime();
    }

    public Jedis tryGetJedis() throws InterruptedException {
        return tryGetJedis(3);
    }

    public Jedis tryGetJedis(int retryTimes) throws InterruptedException {
        Jedis jedis = null;
        for (int i = 0; i < retryTimes; i++) {
            jedis = RedisUtil.instance().GetJedis();
            if (Objects.nonNull(jedis)) {
                return jedis;
            }

            Thread.sleep(10000L);
        }

        log.info("尝试{}次未获取到jedis对象实例", retryTimes);
        return jedis;
    }

    public static boolean hsetToRedis(Jedis jedis, String baseKey, HashMap<String,String> redisKeyValues) {
        if(!redisKeyValues.isEmpty()){
            for (int j = 0; j <= 3; j++) {
                long result = jedis.hset(baseKey, redisKeyValues);
                if(result > 0){
                    return true;
                }
            }
            return false;
        }
        return true;
    }

}