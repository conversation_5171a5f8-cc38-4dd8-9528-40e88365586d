package com.cet.fbp.common.bean.datacachelibsrv;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ：DataLogSequenceParam
 * @date ：Created in 2021/2/23 10:04
 * @description： 查询定时记录参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataLogSequenceResult {
    private Long chnID;
    private Integer count;
    private List<DataLogSequenceData> datas;
}
