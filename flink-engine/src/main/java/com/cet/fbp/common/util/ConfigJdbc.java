package com.cet.fbp.common.util;

import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.cet.fbp.common.frame.FbpProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.*;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;
import java.util.Properties;

/**
 * config库连接，待修改合并
 */
public class ConfigJdbc extends Singleton {

    private static final Logger log = LoggerFactory.getLogger(ConfigJdbc.class);

    private DataSource m_ds=null;

    public static ConfigJdbc instance() {
        return (ConfigJdbc) Singleton.getInstance(ConfigJdbc.class);
    }

    @Override
    public void Init() {
        while (true){
            try {
                //创建Properties对象，用于加载配置文件
                Properties pro = new Properties();
                //设置默认参数
                pro.setProperty("initialSize", "5");
                pro.setProperty("maxActive", "100");
                pro.setProperty("maxWait", "3000");
                boolean isWin = System.getProperty("os.name").toLowerCase().contains("win");
                if (isWin) {
                    String propertiesFileName = "configdruid.properties";
                    InputStream resourceAsStream = FbpProperties.class.getClassLoader().getResourceAsStream(propertiesFileName);
                    if(resourceAsStream != null){
                        pro.load(resourceAsStream);
                    }
                    else{
                        String propertiesPath = PropertiesUtils.getPropertiesPath(propertiesFileName);

                        try (BufferedReader bufferedReader = new BufferedReader(new FileReader(propertiesPath))){
                            pro.load(bufferedReader);//加载配置文件
                        }
                    }
                }
                else {
                    // 兼容之前和之后的
                    File file = new File("/opt/CET/Common/flink/conf/configdruid.properties");
                    if (file.exists()) {
                        try (BufferedReader bufferedReader = new BufferedReader(new FileReader("/opt/CET/Common/flink/conf/configdruid.properties"))){
                            pro.load(bufferedReader);//加载配置文件
                            log.info("linux druid.properties load");
                        }

                    }
                    // 加载 .env 公共环境变量
                    Map<String, String> map = System.getenv();
                    pro.putAll(map);
//                        log.info("all env variables：" + map);

                    if (pro.containsKey("config-driverClassName")) {
                        pro.setProperty("driverClassName", pro.getProperty("config-driverClassName",""));
                    }
                    if (pro.containsKey("config-url")) {
                        pro.setProperty("url", pro.getProperty("config-url",""));
                    }
                    if (pro.containsKey("config-username")) {
                        pro.setProperty("username", pro.getProperty("config-username",""));
                    }
                    if (pro.containsKey("config-password")) {
                        pro.setProperty("password", pro.getProperty("config-password",""));
                    }
                    log.info("env variables load");
                }

                //解密配置中的密文
                JasyptUtil.decrypt(pro);

                String sUrl = pro.getProperty("url","");
                log.info("isWin={},sUrl={}", isWin, sUrl);
                //获取数据库连接池对象
                m_ds = DruidDataSourceFactory.createDataSource(pro);
                if (m_ds==null) {
                    log.error("Init m_ds=null failed");
                    Thread.sleep(3000);
                    continue;
                }
                return;
            } catch (IOException e) {
                log.error("Init failed", e);
            } catch (Exception e) {
                log.error("Init failed", e);
            }
            TimeFunc.Sleep(3000);
        }
    }


    /*
     * 从连接池中获取连接
     * */
    public Connection GetConn(){
        try {
            return m_ds.getConnection();
        } catch (SQLException e) {
            log.error("GetConn failed", e);
        }
        return null;
    }



}
