package com.cet.fbp.common.bean.datacachelibsrv;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 一天1个通道的索引文件结构
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataLogIdx {
    private Integer count;
    private Double time;
    @JsonAlias({"datas","result"})
    private List<DataLogIdxData> datas;

    /**
     * 一个datacache文件索引块信息，包括文件id和最大块id
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataLogIdxData {
        private Integer count;
        private Short fileID;
    }
}
