package com.cet.engine.plugin.base.flink.transform;

import com.cet.engine.model.DataExceptPara;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.streaming.api.operators.KeyedProcessOperator;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.streaming.util.KeyedOneInputStreamOperatorTestHarness;
import org.apache.flink.types.Row;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.flink.table.shaded.org.reflections.Reflections.log;

public class EnergyExceptionProcessorCasesTest {

    private final RowTypeInfo rowTypeInfo = new RowTypeInfo(new TypeInformation<?>[]{Types.INT, Types.DOUBLE, Types.LONG,Types.DOUBLE, Types.INT, Types.STRING, Types.DOUBLE, Types.BYTE, Types.LONG, Types.LONG,Types.BYTE,Types.BYTE}, new String[]{"devID","val","latterTmT5","latterValT5", "dataID", "logicalID","priorValT5","latterStatusT5","tm", "priorTmT5", "priorStatusT5", "status"});

    private KeyedOneInputStreamOperatorTestHarness<Row, Row, Row> testHarness;
    private EnergyExceptionProcessor function;

    @Before
    public void setup() throws Exception {

        Map<String, String> row2TableMap = new HashMap<String, String>() {{
            put("devID", "devID");
            put("dataID", "dataID");
            put("logicalID", "logicalID");
            put("val", "val");
            put("tm", "tm");
            put("status", "status");
        }};

        Map<String, String> table2RowMap = new HashMap<String, String>() {{
            put("devID", "devID");
            put("dataID", "dataID");
            put("logicalID", "logicalID");
            put("val", "val");
            put("tm", "tm");
            put("status", "status");
        }};

        Map<String, String> groupByMap = new HashMap<String, String>() {{
            put("devID", "int");
            put("dataID", "int");
            put("logicalID", "shot");
        }};

        DataExceptPara dataExceptPara = new DataExceptPara();
        dataExceptPara.setTurnOVerEng(99999.99);
        dataExceptPara.setRatedPower(null);
        dataExceptPara.setbFixedEmpirical(true);
        dataExceptPara.setMaxDifEng(100.0);
        dataExceptPara.setMaxMultiEng(2.0);
        dataExceptPara.setIgnoreDifEng(3.0);
        dataExceptPara.setLimitDifEng(10000.0);
        dataExceptPara.setCacheDtlogNum(5);
        dataExceptPara.setDtlogNum4CheckEngDown(2);

        Map<String, Object> exceptPara = new HashMap<String, Object>(){{
            put("inRange", true);
            put("fallback", true);
            put("difValLimit", true);
            put("euclideanDistance", true);
        }};
        String id = "T5";
        function = new EnergyExceptionProcessor(row2TableMap, table2RowMap, groupByMap, dataExceptPara, exceptPara, id);
        KeyedProcessOperator<Map<String, Object>, Row, Row> operator = new KeyedProcessOperator<>(this.function);
        // 创建测试Harness
        testHarness = new KeyedOneInputStreamOperatorTestHarness<>(operator, (row) -> {
            Row keyRow = Row.withNames();
            keyRow.setField("devID", row.getField("devID"));
            keyRow.setField("dataID", row.getField("dataID"));
            keyRow.setField("logicalID", row.getField("logicalID"));
            return keyRow;
        }, rowTypeInfo);
        TypeSerializer<Row> rowSerializer = rowTypeInfo.createSerializer(new ExecutionConfig());
        testHarness.setup(rowSerializer);
        // 打开算子
        testHarness.open();
    }

    @Test
    public void testProcessElement_tuBian_01() throws Exception {

        //测试场景：1、只存在一次突变【向上突变】：突变值超过欧式距离,后续数据点，小于上一电度值,异常数据列表超过配置cacheDtlogNum=5
        //预期结果：1、遍历数据点，通过数据有效性校验，会进行后续下降点校验：对应01:30 进行后续下降点校验，发现01:45 02:00下降，且小于上一电度值，继续遍历
        //        2、01:45之后数据点超过欧式距离，会加入到异常列表中
        //        3、遍历02:20这个点时，异常数据列表个数=cacheDtlogNum=5，取第一个异常值：01:45按照自动换表处理，差值为0，表码值5000；
        //        4、继续遍历02:30这个数据点，上一电度值更新为5000，超出2点间差值，加入到异常数据列表，且个数=cacheDtlogNum=5，取第一个异常值：02:00按照自动换表处理，差值为0，表码值100；
        //        5、在处理剩余异常数据点时，上一电度值为100，对应异常列表中，110,120,130,140，均满足数据有效性校验，直接输出，并更新上一电度值为140
        //        6、继续遍历02:34则个数据点并输出


        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,100, "2024-12-20T01:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,200, "2024-12-20T01:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,300, "2024-12-20T01:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,5000, "2024-12-20T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,100, "2024-12-20T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,110, "2024-12-20T02:10:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,120, "2024-12-20T02:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,130, "2024-12-20T02:20:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,140, "2024-12-20T02:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,150, "2024-12-20T02:34:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(100, "2024-12-20T01:00:00",1, assertData); 作为上一个电度值，不输出
        generateAssertData(200, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(300, "2024-12-20T01:30:00",1, assertData);
        generateAssertData(5000, "2024-12-20T01:45:00",25, assertData);
        generateAssertData(100, "2024-12-20T02:00:00",25, assertData);
        generateAssertData(110, "2024-12-20T02:10:00",23, assertData);
        generateAssertData(120, "2024-12-20T02:15:00",23, assertData);
        generateAssertData(130, "2024-12-20T02:20:00",23, assertData);
        generateAssertData(140, "2024-12-20T02:30:00",23, assertData);
        generateAssertData(150, "2024-12-20T02:34:00",1, assertData);


        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian0() throws Exception {

        //测试场景：1、只存在一次突变【向上突变】：突变值超过欧式距离,后续数据点，小于上一电度值
        //预期结果：1、遍历数据点，通过数据有效性校验，会进行后续下降点校验：对应01:30 进行后续下降点校验，发现01:45 02:00下降，且小于上一电度值，继续遍历
        //        2、01:45之后数据点超过欧式距离，会加入到异常列表中

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,100, "2024-12-20T01:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,200, "2024-12-20T01:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,300, "2024-12-20T01:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,5000, "2024-12-20T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,100, "2024-12-20T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,200, "2024-12-20T02:10:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(100, "2024-12-20T01:00:00",1, assertData); 作为上一个电度值，不输出
        generateAssertData(200, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(300, "2024-12-20T01:30:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian1() throws Exception {

        //测试场景：1、只存在一次突变【向上突变】：突变值超过欧式距离,后续数据点，大于上一电度值
        //预期结果：1、从输出结果看，这个点被线性补点；
        //        2、逻辑上，该点因为超过欧氏距离，加入到异常列表exceptDtList中，继续遍历后续数据点，并进行15分钟线性补点，对应status=24，是因为线性补点之后，有一个异常数据状态更新

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,100, "2024-12-20T01:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,200, "2024-12-20T01:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,300, "2024-12-20T01:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,5000, "2024-12-20T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,500, "2024-12-20T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,600, "2024-12-20T02:10:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(100, "2024-12-20T01:00:00",1, assertData); 作为上一个电度值，不输出
        generateAssertData(200, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(300, "2024-12-20T01:30:00",1, assertData);
        generateAssertData(400, "2024-12-20T01:45:00",24, assertData);
        generateAssertData(500, "2024-12-20T02:00:00",1, assertData);
        generateAssertData(500, "2024-12-20T02:00:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian2() throws Exception {

        //测试场景：1、只存在一次突变【向上突变】：突变值未超过欧式距离【计算每分钟差值<100*2】，后续下降点值大于上一电度值
        //预期结果：1、因后续点数据下降，且大于上一电度值，此突变值以上一个电度值替换，对应差值为0，设置status=22；
        //        2、后续点下降，因大于上一电度值，正常是输出

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,100, "2024-12-20T01:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,110, "2024-12-20T01:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,120, "2024-12-20T01:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,419, "2024-12-20T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,140, "2024-12-20T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,150, "2024-12-20T02:10:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(100, "2024-12-20T01:00:00",1, assertData);
        generateAssertData(110, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(120, "2024-12-20T01:30:00",1, assertData);
        generateAssertData(120, "2024-12-20T01:45:00",22, assertData);
        generateAssertData(140, "2024-12-20T02:00:00",1, assertData);
        generateAssertData(150, "2024-12-20T02:10:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian3() throws Exception {

        //测试场景：1、只存在一次突变【向上突变】：突变值未超过欧式距离【计算每分钟差值<100*2】，后续下降点值小于上一电度值
        //预期结果：1、突变值未超过欧式距离，当做正常值输出，后续下降点，小于上一电度值，正常遍历下降点，因为突变值到后续下降点，按照翻转计算difVal,但超过2点最大差值，给抛弃掉；
        //        2、02:00、02:10、02:30时刻点，因超过2点最大差值，抛弃掉，02:30正常，正常线性补点：02:00、02:15

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,100, "2024-12-20T01:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,110, "2024-12-20T01:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,120, "2024-12-20T01:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,419, "2024-12-20T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,105, "2024-12-20T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,106, "2024-12-20T02:10:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,130, "2024-12-20T02:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,449, "2024-12-20T02:30:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(100, "2024-12-20T01:00:00",1, assertData);
        generateAssertData(110, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(120, "2024-12-20T01:30:00",1, assertData);
        generateAssertData(419, "2024-12-20T01:45:00",1, assertData);
        generateAssertData(429, "2024-12-20T02:00:00",23, assertData);
        generateAssertData(439, "2024-12-20T02:15:00",23, assertData);
        generateAssertData(449, "2024-12-20T02:30:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian4() throws Exception {

        //测试场景：1、只存在一次突变【向下突变】：突变值超过欧式距离
        //预期结果：1、此点被线性补点

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,99000, "2024-12-20T01:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99100, "2024-12-20T01:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99200, "2024-12-20T01:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,2300, "2024-12-20T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99300, "2024-12-20T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99400, "2024-12-20T02:10:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(99000, "2024-12-20T01:00:00",1); 作为上一个电度值，不输出
        generateAssertData(99100, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(99200, "2024-12-20T01:30:00",1, assertData);
        generateAssertData(99250, "2024-12-20T01:45:00",24, assertData);
        generateAssertData(99300, "2024-12-20T02:00:00",1, assertData);
        generateAssertData(99400, "2024-12-20T02:10:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian5() throws Exception {

        //测试场景：1、只存在一次突变【向下突变】：突变值超过欧式距离,后续点小于上一个电度值，模拟换表操作
        //预期结果：1、突变点被线性补点

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,99000.99, "2024-12-20T01:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99100.99, "2024-12-20T01:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99299.99, "2024-12-20T01:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,2400, "2024-12-20T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,2450, "2024-12-20T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,2460, "2024-12-20T02:10:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(99000.99, "2024-12-20T01:00:00",1); 作为上一个电度值，不输出
        generateAssertData(99100.99, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(99299.99, "2024-12-20T01:30:00",1, assertData);
        generateAssertData(875, "2024-12-20T01:45:00",24, assertData);
        generateAssertData(2450, "2024-12-20T02:00:00",1, assertData);
        generateAssertData(2460, "2024-12-20T02:10:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian6() throws Exception {

        //测试场景：1、只存在一次突变【向下突变】：突变值超过欧式距离,后续点小于上一个电度值,且满足数据有效性校验，目前逻辑上，不能判断为自动换表
        //预期结果：1、突变点被线性补点；
        //        2、后续点按照翻转处理

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,99000.99, "2024-12-20T01:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99100.99, "2024-12-20T01:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99299.99, "2024-12-20T01:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,8400, "2024-12-21T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,8500, "2024-12-21T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,8600, "2024-12-21T02:10:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,8700, "2024-12-21T02:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,8710, "2024-12-21T02:45:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(99000.99, "2024-12-20T01:00:00",1); 作为上一个电度值，不输出
        generateAssertData(99100.99, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(99299.99, "2024-12-20T01:30:00",1, assertData);
        generateAssertData(99393.80, "2024-12-20T01:45:00",20, assertData);
        generateAssertData(99487.61, "2024-12-20T02:00:00",20, assertData);
        generateAssertData(7649.48, "2024-12-20T23:45:00",20, assertData);
        generateAssertData(7743.29, "2024-12-21T00:00:00",20, assertData);
        generateAssertData(7837.11, "2024-12-21T00:15:00",20, assertData);
        generateAssertData(7930.92, "2024-12-21T00:30:00",20, assertData);
        generateAssertData(8024.74, "2024-12-21T00:45:00",20, assertData);
        generateAssertData(8400, "2024-12-21T01:45:00",1, assertData);
        generateAssertData(8500, "2024-12-21T02:00:00",1, assertData);
        generateAssertData(8600, "2024-12-21T02:10:00",1, assertData);
        generateAssertData(8700, "2024-12-21T02:15:00",1, assertData);
        generateAssertData(8705, "2024-12-21T02:30:00",20, assertData);
        generateAssertData(8710, "2024-12-21T02:45:00",1, assertData);



        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian7() throws Exception {

        //测试场景：1、只存在一次突变【向下突变】：突变值超过欧式距离,后续点小于上一个电度值

        //预期结果：1、遍历23:15数据点时，后续下降点校验，23:45小于上一电度值，继续遍历；
        //        2、遍历23:30数据点，后续下降点校验，23:45、00:00小于上一电度值，继续遍历；
        //        3、遍历23:45数据点，未超过欧式距离、未超过两点距离，按照正常翻转输出;
        //        4、后续点正常输出

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,99000.99, "2024-12-20T23:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99100.99, "2024-12-20T23:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99299.99, "2024-12-20T23:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,1200, "2024-12-20T23:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,1300, "2024-12-21T00:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,1400, "2024-12-21T00:10:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,1500, "2024-12-21T00:25:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,1600, "2024-12-21T00:30:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(99000.99, "2024-12-20T23:00:00",1); 作为上一个电度值，不输出
        generateAssertData(99100.99, "2024-12-20T23:15:00",1, assertData);
        generateAssertData(99299.99, "2024-12-20T23:30:00",1, assertData);
        generateAssertData(1200, "2024-12-20T23:45:00",1, assertData);
        generateAssertData(1300, "2024-12-21T00:00:00",1, assertData);
        generateAssertData(1400, "2024-12-21T00:10:00",1, assertData);
        generateAssertData(1433.33, "2024-12-21T00:15:00",20, assertData);
        generateAssertData(1500, "2024-12-21T00:25:00",1, assertData);
        generateAssertData(1600, "2024-12-21T00:30:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_tuBian8() throws Exception {

        //测试场景：1、只存在一次突变【向下突变】：未超过欧式距离，未超过2点差值，后续点小于上一个电度值

        //预期结果：1、01：45及之后的数据点，均因为数据有效性未通过，添加至异常列表exceptDtList，cacheDtlogNum=5，表示5个校验未通过数据
        //        2、时间跨度拉长，计算每分钟差值，小于欧式距离时，做中间的整15分钟线性补点1

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,99000.99, "2024-12-20T23:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99100.99, "2024-12-20T23:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,99299.99, "2024-12-20T23:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,9400, "2024-12-21T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,9500, "2024-12-21T02:00:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,9600, "2024-12-21T02:10:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,9700, "2024-12-21T02:15:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,9710, "2024-12-21T02:25:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,9780, "2024-12-21T02:35:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(99000.99, "2024-12-20T23:00:00",1); 作为上一个电度值，不输出
        generateAssertData(99100.99, "2024-12-20T01:15:00",1, assertData);
        generateAssertData(99100.99, "2024-12-20T23:15:00",1, assertData);
        generateAssertData(99299.99, "2024-12-20T23:30:00",1, assertData);
        generateAssertData(99299.99, "2024-12-20T23:45:00",20, assertData);
        generateAssertData(99299.99, "2024-12-21T00:00:00",20, assertData);
        generateAssertData(99299.99, "2024-12-21T00:15:00",20, assertData);
        generateAssertData(99299.99, "2024-12-21T00:30:00",20, assertData);
        generateAssertData(99299.99, "2024-12-21T00:45:00",20, assertData);
        generateAssertData(99299.99, "2024-12-21T01:00:00",20, assertData);
        generateAssertData(99299.99, "2024-12-21T01:15:00",20, assertData);
        generateAssertData(99299.99, "2024-12-21T01:30:00",20, assertData);
        generateAssertData(9400, "2024-12-21T01:45:00",1, assertData);
        generateAssertData(9500, "2024-12-21T02:00:00",1, assertData);
        generateAssertData(9600, "2024-12-21T02:10:00",1, assertData);
        generateAssertData(9700, "2024-12-21T02:15:00",1, assertData);
        generateAssertData(9710, "2024-12-21T02:25:00",1, assertData);
        generateAssertData(9745, "2024-12-21T02:30:00",20, assertData);
        generateAssertData(9780, "2024-12-21T02:35:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }

    @Test
    public void testProcessElement_huiGui1() throws Exception {

        //回归测试：1、未来时间剔除，过滤能源累积量的定时记录，做排序

        //预期结果：1、未来时间剔除，过滤能源累积量的定时记录，做排序

        List<Row> dataSet = new ArrayList<>();
        generateDataToRow(10,4000004,1,1,100, "2024-12-20T23:00:00",1,dataSet);
        generateDataToRow(10,10000001,1,1,200, "2024-12-20T23:15:00",1,dataSet);
        generateDataToRow(10,2000004,1,1,300, "2024-12-20T23:30:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,320, "2024-12-21T01:45:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,790, "2024-12-21T02:35:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,600, "2024-12-21T02:10:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,600, "2024-12-21T02:10:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,690, "2024-12-21T02:25:00",1,dataSet);
        generateDataToRow(10,4000004,1,1,780, "2026-12-21T02:30:00",1,dataSet);

        //构建断言预期
        List<OutPutDTOTest> assertData = new ArrayList<>();
        //generateAssertData(100, "2024-12-20T23:00:00",1); 作为上一个电度值，不输出
        generateAssertData(120, "2024-12-20T23:15:00",20, assertData);
        generateAssertData(140, "2024-12-20T23:30:00",20, assertData);
        generateAssertData(160, "2024-12-20T23:45:00",20, assertData);
        generateAssertData(180, "2024-12-21T00:00:00",20, assertData);
        generateAssertData(200, "2024-12-21T00:15:00",20, assertData);
        generateAssertData(220, "2024-12-21T00:30:00",20, assertData);
        generateAssertData(240, "2024-12-21T00:45:00",20, assertData);
        generateAssertData(260, "2024-12-21T01:00:00",20, assertData);
        generateAssertData(280, "2024-12-21T01:15:00",20, assertData);
        generateAssertData(300, "2024-12-21T01:30:00",20, assertData);
        generateAssertData(320, "2024-12-21T01:45:00",1, assertData);
        generateAssertData(488, "2024-12-21T02:00:00",20, assertData);
        generateAssertData(600, "2024-12-21T02:10:00",1, assertData);
        generateAssertData(630, "2024-12-21T02:15:00",20, assertData);
        generateAssertData(690, "2024-12-21T02:25:00",1, assertData);
        generateAssertData(740, "2024-12-21T02:30:00",20, assertData);
        generateAssertData(790, "2024-12-21T02:35:00",1, assertData);

        testProcessElement(dataSet, assertData);
    }



    public void outputPrint(ConcurrentLinkedQueue<Object> outputList){

        if(outputList.isEmpty()){
            if (log != null) {
                log.info("异常数据处理算子，输出结果为空");
            }
            return;
        }

        log.info("异常算子输出数据条数：{}", outputList.size());
        for (Object output : outputList) {
            String outputStr = output.toString();
            int tmIndex = outputStr.indexOf("tm=");
            if (tmIndex != -1) {
                int start = tmIndex + 3;
                int end = outputStr.indexOf(",", start);
                if (end == -1) {
                    end = outputStr.indexOf("}", start);
                }
                String timestampStr = outputStr.substring(start, end);
                long timestamp = Long.parseLong(timestampStr);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                outputStr = outputStr.replace(timestampStr, sdf.format(new Date(timestamp)));
            }
            log.info("异常算子输出：{}", outputStr);
        }
    }

    public void outPutData(ConcurrentLinkedQueue<Object> outputList, List<OutPutDTOTest> outPutDTOTestList) {

        if(outputList.isEmpty()){
            if (log != null) {
                log.info("异常数据处理算子，输出结果为空");
            }
            return;
        }

        for (Object output : outputList) {

            OutPutDTOTest outPutDTOTest = new OutPutDTOTest();
            String outputStr = output.toString();

            //解析输出结果中的timestamps
            int tmIndex = outputStr.indexOf("tm=");
            if (tmIndex != -1) {
                int start = tmIndex + 3;
                int end = outputStr.indexOf(",", start);
                if (end == -1) {
                    end = outputStr.indexOf("}", start);
                }
                String timestampStr = outputStr.substring(start, end);
                long timestamp = Long.parseLong(timestampStr);
                outPutDTOTest.setLogTime(timestamp);
            }

            //解析输出结果中的value
            int tmIndex1 = outputStr.indexOf("latterValT5=");
            if (tmIndex1 != -1) {
                int start1 = tmIndex1 + 12;
                int end1 = outputStr.indexOf(",", start1);
                if (end1 == -1) {
                    end1 = outputStr.indexOf("}", start1);
                }
                String valueStr = outputStr.substring(start1, end1);
                double value = Double.parseDouble(valueStr);
                outPutDTOTest.setValue(value);
            }

            //解析输出结果中的status
            int tmIndex2 = outputStr.indexOf("latterStatusT5=");
            if (tmIndex2 != -1) {
                int start2 = tmIndex2 + 15;
                int end2 = outputStr.indexOf(",", start2);
                if (end2 == -1) {
                    end2 = outputStr.indexOf("}", start2);
                }
                String valueStr = outputStr.substring(start2, end2);
                int status = Integer.parseInt(valueStr);
                outPutDTOTest.setStatus(status);
            }
            outPutDTOTestList.add(outPutDTOTest);
        }
    }

    public void generateAssertData(double value, String logTime, int status, List<OutPutDTOTest> assertData) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'H:mm:ss");
        // 解析为 LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.parse(logTime, formatter);
        // 转换为时间戳（毫秒，基于系统默认时区）
        long timestamp = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        OutPutDTOTest outPutDTOTest = new OutPutDTOTest();

        outPutDTOTest.setLogTime(timestamp);
        outPutDTOTest.setValue(value);
        outPutDTOTest.setStatus(status);

        assertData.add(outPutDTOTest);
    }

    public boolean resultAssert(List<OutPutDTOTest> assertData, List<OutPutDTOTest> outPutDTOTestList){

        if(outPutDTOTestList.isEmpty()){
            return true;
        }

        Map<Long, OutPutDTOTest> outPutMap = outPutDTOTestList.stream()
                .collect(Collectors.toMap(OutPutDTOTest::getLogTime, Function.identity(),
                        (existing, replacement) -> existing));

        for(OutPutDTOTest expected : assertData){
            OutPutDTOTest actual = outPutMap.get(expected.getLogTime());

            if(actual == null){
                log.info("同时刻点：预期数据：{}，在算子输出中未找到", expected.toString());
                return false;
            }


            if ((Math.abs(expected.getValue()-actual.getValue()) >= 1) ||
            !Objects.equals(expected.getStatus(), actual.getStatus())) {
                log.info("同时刻点：预期数据：{}，与输出结果：{}不一致", expected.toString(), actual.toString());
                return false;
            }
        }
        return true;
    }

    public void generateDataToRow(int devId, int dataID, int logicalId, int dataTypeId, double val, String logTime, int status, List<Row> dataList){

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'H:mm:ss");
        // 解析为 LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.parse(logTime, formatter);
        // 转换为时间戳（毫秒，基于系统默认时区）
        long timestamp = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        Row tmp = Row.withNames();
        tmp.setField("devID", devId);
        tmp.setField("dataID", dataID);
        tmp.setField("logicalID", logicalId);
        tmp.setField("dataTypeID", dataTypeId);
        tmp.setField("val", val);
        tmp.setField("tm", timestamp);
        tmp.setField("status", status);
        dataList.add(tmp);
    }

    public void testProcessElement(List<Row> dataRow, List<OutPutDTOTest> assertDataList) throws Exception {

        int index = 0;
        for (Row inputRow : dataRow) {
            testHarness.processElement(new StreamRecord<>(inputRow, System.currentTimeMillis()));
            //log.info("");
            Thread.sleep(10);
            index++;
        }

        DateTime triggerTime = new DateTime();
        // 触发定时器
        testHarness.setProcessingTime(triggerTime.getMillis() + 60 * 1000);
        // 结果
        ConcurrentLinkedQueue<Object> outputList = testHarness.getOutput();

        List<OutPutDTOTest> outPutDTOTestList = new ArrayList<>();

        outPutData(outputList, outPutDTOTestList);

        outputPrint(outputList);
        testHarness.close();

        //断言
        Boolean result = resultAssert(assertDataList, outPutDTOTestList);
        Assert.assertEquals("本次测试未通过",true, result);

    }
}

