package com.cet.engine.file.util.writer;

import com.alibaba.fastjson.JSONObject;
import com.cet.engine.file.util.common.Const;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.DefaultRollingPolicy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class FileUtil {
    private static final Logger log = LoggerFactory.getLogger(FileUtil.class);

    public static <T> void sinkFile(DataStream<T> fileStream, String pathStr, String label, String aggregationCycle, String[] fields, String[] types) {

        // 1. 提取字段元数据
        fileStream.map(new MapFunction<T, Object>() {

            Boolean writeMeta = true;
            @Override
            public Object map(T t) throws Exception {
                //第一次启动进行写入元数据
                if (writeMeta) {
                    writeMetaData(pathStr,label,t, fields, types);
                }
                writeMeta = false;
                return t;
            }
        } ).addSink(getFileSink(pathStr,label,aggregationCycle,fields));

    }
    private static <T> StreamingFileSink<T> getFileSink(String pathStr, String label, String aggregationCycle, String[] fields) {
        if (pathStr == null || pathStr.isEmpty()) {
            pathStr = String.format("%s/%s", Const.FIRST_PATH, label);
        } else {
            pathStr = String.format("%s/%s", pathStr, label);
        }
        return StreamingFileSink
                .<T>forRowFormat(new org.apache.flink.core.fs.Path(pathStr),
                        new DataEncoder<T>(fields))
                // 下面添加自定义桶分配器（分文件夹）
                .withBucketAssigner(new DayAndAggBucketAssigner<T>(aggregationCycle))
                .withRollingPolicy(
                        DefaultRollingPolicy.builder()
                                //包含超过1天数据，输出新文件
                                .withRolloverInterval(TimeUnit.DAYS.toMillis(1))
                                //1天无新数据，输出新文件
                                .withInactivityInterval(TimeUnit.DAYS.toMillis(1))
                                //超过100MB，输出新文件
                                .withMaxPartSize(100 * 1024 * 1024)
                                .build())
                .build();
    }

    private static <T> void writeMetaData(String pathStr, String label,T t, String[] fields, String[] types) throws Exception {
        if (pathStr == null || pathStr.isEmpty()) {
            pathStr = String.format("%s/%s%s", Const.FIRST_PATH, label, Const.META_PATH);
        } else {
            pathStr = String.format("%s/%s%s", pathStr, label, Const.META_PATH);
        }

        JSONObject content = new JSONObject();
        content.fluentPut(Const.fields, fields)
                .fluentPut(Const.types, types);
        Path path = Paths.get(pathStr);

        // 保证父目录存在
        if (path.getParent() != null && !Files.exists(path.getParent())) {
            Files.createDirectories(path.getParent());
        }

        if (!Files.exists(path)) {
            // 文件不存在：创建并写入内容
            Files.write(path, content.toString().getBytes(), StandardOpenOption.CREATE_NEW);
            log.info("文件不存在，已新建并写入内容。");
        } else {
            // 文件存在，删除原来的数据重新写入
            Files.delete(path);
            Files.write(path, content.toString().getBytes(), StandardOpenOption.CREATE_NEW);
            log.info("文件为空，已重新写入内容。");
        }
    }


}
