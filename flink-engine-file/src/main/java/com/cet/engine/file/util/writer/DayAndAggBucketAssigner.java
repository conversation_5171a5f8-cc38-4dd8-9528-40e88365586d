package com.cet.engine.file.util.writer;

import org.apache.flink.core.io.SimpleVersionedSerializer;
import org.apache.flink.streaming.api.functions.sink.filesystem.BucketAssigner;
import org.apache.flink.streaming.api.functions.sink.filesystem.bucketassigners.SimpleVersionedStringSerializer;

import java.io.Serializable;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class DayAndAggBucketAssigner<T> implements BucketAssigner<T, String>, Serializable {
    private final String aggregationCycle;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.of("UTC"));

    public DayAndAggBucketAssigner(String aggregationCycle) {
        this.aggregationCycle = aggregationCycle;
    }

    @Override
    public String getBucketId(T element, Context context) {
        long ts = context.currentProcessingTime();
        String day = DATE_FORMATTER.format(Instant.ofEpochMilli(ts));
        return String.format("%s/%s", day, aggregationCycle);
    }

    @Override
    public SimpleVersionedSerializer<String> getSerializer() {
        return SimpleVersionedStringSerializer.INSTANCE;
    }
}