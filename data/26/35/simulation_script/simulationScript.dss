clear

new Circuit.circuit phases=3 bus=bus_2371821
~ basekv=10 pu=1


set defaultbasefrequency=50
set mode=daily stepsize=15m number=96 hour=0 sec=0
set voltagebases=[0.23,0.38,10] 


new Transformer.T1
~ phases=3 windings=2 
~ XHL=0.01 %loadloss=0.1
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=bus_2371821 kv=10 kva=100 conn=DELT %R=0.1 tap=1
~ wdg=2 bus=bus_266b35c_4790d959 kv=0.4 kva=100 conn=WYE %R=0.1 tap=1
new Transformer.R1
~ phases=3 windings=2 bank=RegControl_4_R1
~ XHL=0.01 %loadloss=0.01
~ maxtap=1.2 mintap=0.8 numtaps=64
~ wdg=1 bus=bus_266b35c_0117789 kv=0.4 kva=20 conn=DELT %R=0.01 
~ wdg=2 bus=bus_2c206a1_4790d959 kv=0.4 kva=20 conn=WYE %R=0.01 

new RegControl.RegControl_4_R1 transformer=R1 winding=2
~ vreg=3.7 band=0.05 ptratio=60
~ enabled=false















new LineCode.LGJ25
~ nphases=3 basefreq=50 units=km R1=1.37 X1=0.38

new LineCode.JKLYJ35
~ nphases=3 basefreq=50 units=km R1=0.3238 X1=0.303

new LineCode.JKLYJ70
~ nphases=3 basefreq=50 units=km R1=0.46 X1=0.398




new Line.Line_266b35c_1 phases=3 bus1=bus_266b35c_4790d959 bus2=bus_266b35c_0117789
~ length=1 lineCode=JKLYJ35
new Line.Line_2c206a1_1 phases=3 bus1=bus_2c206a1_4790d959 bus2=bus_2c206a1_0117789
~ length=1 lineCode=JKLYJ35


//负荷相关
new LoadShape.LoadShape_Load
~ npts=96 interval=0.15
~ mult=(0.0099,0.0098,0.0097,0.0096,0.0096,0.011,0.011,0.0112,0.0109,0.011,0.0112,0.0112,0.0112,0.0109,0.0109,0.0106,0.0104,0.0102,0.0098,0.0063,0.0064,0.0064,0.0063,0.0053,0.0053,0.0053,0.0053,0.1525,0.0108,0.0107,0.0107,0.0109,0.011,0.011,0.0111,0.0111,0.011,0.0111,0.0112,0.0122,0.0125,0.0124,0.0066,0.0119,0.0174,0.0559,0.0564,0.0113,0.0112,0.0116,0.0114,0.011,0,0.0107,0.0109,0.0107,0.0107,0.0109,0.0128,0.0127,0.1672,0.0125,0.0118,0.0117,0.0116,0.0115,0.103,0.0156,0.0155,0.0153,0.0151,0.0152,0.0149,0.0437,0.0057,0.0063,0.0106,0.0109,0.0117,0.0174,0.0179,0.0175,0.0166,0.0166,0.0164,0.0164,0.1163,0.1165,0.1166,0.1162,0.1112,0.0113,0.0114,0.0113,0.0113,0.0113)

new Load.Load phases=3 bus1=bus_2c206a1_0117789.1.2.3
~ model=1 kv=0.4 kvar=0 kw=10
~ daily=LoadShape_Load



//光伏相关


//储能相关


//关键参数监控
New Monitor.monitor_Load_VI  element=Load.Load terminal=1 mode=0 ppolar=0
New Monitor.monitor_Load_PQ  element=Load.Load terminal=1 mode=1 ppolar=0


calcv
solve
export monitor monitor_Load_VI
export monitor monitor_Load_PQ
