package com.cet.engine.file.util.query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

//单例基类泛型实现，需要单例的子类直接继承Singleton，然后按(子类名)Singleton.getInstance(子类名.class);封装调用
//语法糖，因为子类的构造函数必须要public，无法阻止其直接new
//子类如果需要在创建单例中进行一次初始化，则重写Init方法即可
public class Singleton {
	protected static final Logger log = LoggerFactory.getLogger(Singleton.class);

	//volatile代表了多线程环境下，根据JVM规范，只要instanceMap改变了，其他线程将读到最新的值，并且禁止对其进行指令重排序优化
	private static final Map<Class<? extends Singleton>, Singleton> instanceMap = new ConcurrentHashMap<>();

	protected static <T extends Singleton> Singleton getInstance(Class<T> instanceClass) {
        Singleton instance = instanceMap.get(instanceClass);
        //  双检测锁定(多线程)
        if (instance == null)
        {
        	synchronized (instanceMap)
        	{
        		instance = instanceMap.get(instanceClass);
        		if(instance==null)
        		{
                    log.info(instanceClass.getName() + " new Instance");
        			try {
						instance = instanceClass.newInstance();
						instance.Init();
						instanceMap.put(instanceClass, instance);
					} catch (InstantiationException | IllegalAccessException e) {
//						PublicFunc.LogOutException(e, instanceClass.getName()+" new Instance");
					}
        		}
        	}        
        }
        return instance;
    }
    
    protected Singleton(){
    }
    
    //子类如果需要在创建单例中进行一次初始化，则重写Init方法即可
    protected void Init() {
        log.info("Singleton init");
    }

	public static <T extends Singleton> void freeInstance(Class<T> instanceClass){
		Singleton instance = instanceMap.get(instanceClass);
		//  双检测锁定(多线程)
		if (instance == null) {
            return;
        }
		synchronized (instanceMap)
		{
			instance = instanceMap.get(instanceClass);
			if(instance==null) {
                return;
            }
			log.info(instanceClass.getName() + " free Instance");
			instanceMap.remove(instanceClass);
		}
	}

}
