package com.cet.engine.file.util.writer;

import org.apache.flink.api.common.serialization.Encoder;
import org.apache.flink.types.Row;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class DataEncoder<T> implements Encoder<T> {

    // 字段缓存: 类->可序列化字段
    private static final Map<Class<?>, List<Field>> FIELD_CACHE = new ConcurrentHashMap<>();

    private String[] fields;
    public DataEncoder(String[] fields) {
        this.fields = fields;
    }

    private static List<Field> getSerializableFields(Class<?> clazz) {
        return FIELD_CACHE.computeIfAbsent(clazz, key -> {
            List<Field> fields = new ArrayList<>();
            for (Field field : clazz.getDeclaredFields()) {
                int mods = field.getModifiers();
                if (Modifier.isStatic(mods) || Modifier.isTransient(mods)) continue;
                field.setAccessible(true); // 批量设置，无需每次操作
                fields.add(field);
            }
            return fields;
        });
    }

    @Override
    public void encode(T element, OutputStream stream) throws IOException {
        List<String> values = new ArrayList<>();
        if (element.getClass() == Row.class) {
            // flink 12 版本暂没有Row格式数据处理
        } else {
            for (Field field : getSerializableFields(element.getClass())) {
                Object value;
                try {
                    value = field.get(element);
                } catch (IllegalAccessException e) {
                    value = "";
                }
                values.add(value != null ? value.toString() : "");
            }
        }
        String line = String.join(",", values) + "\n";
        stream.write(line.getBytes(StandardCharsets.UTF_8));
    }
}
