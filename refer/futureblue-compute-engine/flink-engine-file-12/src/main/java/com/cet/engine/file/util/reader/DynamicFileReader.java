package com.cet.engine.file.util.reader;

import com.alibaba.fastjson2.JSONObject;
import com.cet.engine.file.util.common.Const;
import com.cet.engine.file.util.query.QueryFile;
import org.apache.commons.io.monitor.FileAlterationMonitor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.flink.types.Row;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DynamicFileReader extends RichSourceFunction<Row> implements CheckpointedFunction {

    private volatile boolean isRunning = true;
    private final String label;
    private final String id;
    // 保存文件偏移量
    private transient ListState<Map<String,Long>> offsetState;

    public DynamicFileReader(String label, String id) {
        this.label = label;
        this.id = id;
    }

    @Override
    public void run(SourceContext<Row> ctx) throws Exception {
        String pathStr = String.format("%s/%s", Const.FIRST_PATH, label);
        JSONObject metaJson = QueryFile.instance().getModelList(label);

        File file = new File(pathStr);
        FileAlterationObserver observer = new FileAlterationObserver(file);
        // 60秒扫描间隔
        FileAlterationMonitor monitor = new FileAlterationMonitor(10000);

        observer.addListener(new FileListener(ctx, offsetState.get().iterator().next(), metaJson));
        monitor.addObserver(observer);
        monitor.start();
        while (isRunning) {
            Thread.sleep(5000);
        }
        monitor.stop();
    }

    @Override
    public void cancel() {
        isRunning = false;
    }

    // 检查点状态管理
    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        offsetState.clear();
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        ListStateDescriptor<Map<String, Long>> descriptor =
                new ListStateDescriptor<>("offsetState" + id, TypeInformation.of(new TypeHint<Map<String, Long>>() {}) );
        offsetState = context.getOperatorStateStore().getListState(descriptor);
        offsetState.add(new HashMap<>());
    }


}
