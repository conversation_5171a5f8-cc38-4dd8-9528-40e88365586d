version: '3'
services:
  #redis
  redis:
    image: *************/base/redis:v7.2.3
    privileged: true
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${redis_ip}
        aliases:
        - redis
    restart: always
    ports:
    - 0.0.0.0:26379:6379
    #command: redis-server --requirepass Ceiec4567!
    volumes:
      - /etc/CET/docker/redis/redis.conf:/data/redis.conf
    command: redis-server redis.conf
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai

  #rabitmq
  rabbitmq:
    image: *************/base/rabbitmq:3.8.34-management
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${rabbitmq_ip}
        aliases:
        - rabbitmq
    restart: always
    volumes:
    - /usr/share/zoneinfo/Asia:/usr/share/zoneinfo/Asia
    - /etc/CET/docker/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    ports:
    - 0.0.0.0:15672:15672
    - 0.0.0.0:5672:5672
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai

  service-center-1:
    image: *************/matterhorn/service-center:${service_center_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${service_center_ip}
        aliases:
        - discovery1
    restart: always
    hostname: discovery1
    ports:
    - 0.0.0.0:1001:1001
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx256m -Xms256m
      SERVER_PORT: 1001
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      spring_security_enabled: 'true'
      spring_security_user_name: ${SpringUserName}
      spring_security_user_password: ${SpringPassWord}
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      EUREKA_CLIENT_REGISTER-WITH-EUREKA: 'false'
      EUREKA_CLIENT_FETCH-REGISTRY: 'false'
      SPRING_RABBITMQ_ADDRESSED: ${master_network_segment}.${rabbitmq_ip}:${RabbitmqPort}
      SPRING_RABBITMQ_USERNAME: ${RabbitmqUser}
      SPRING_RABBITMQ_PASSWORD: ${RabbitmqPwd}
      eureka_server_peer-node-read-timeout-ms: 1500
      management_endpoints_web_exposure_include: health,info
    
  service-monitor:
    image: *************/matterhorn/service-monitor:${service_monitor_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${service_monitor_ip}
        aliases:
        - monitor1
    restart: always
    hostname: monitor1
    ports:
    - 0.0.0.0:8788:8788
    depends_on:
    - service-center-1
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx256m -Xms256m
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      management_endpoints_web_exposure_include: health,info
      spring_security_user_name: cet
      spring_security_user_password: sA135246@?/

  api-gateway-1:
    image: *************/matterhorn/gateway-zuul:${gateway_zuul_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${gateway_ip}
        aliases:
        - gateway1
    restart: always
    hostname: gateway1
    ports:
    - 0.0.0.0:4001:4001
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx256m -Xms256m
      SERVER_PORT: 4001
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/      
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      #cet_auth-service-name: auth-server
      auth-config_permitall[0]_pattern: /auth/**
      auth-config_permitall[1]_pattern: /api/auth/**
      auth-config_permitall[2]_pattern: /actuator/**
      auth-config_permitall[3]_pattern: /data-center-service/data-center/v1/common/downloadExcel
      auth-config_permitall[4]_pattern: /data-center-service/data-center/v1/common/queryTimeMillis
      auth-config_permitall[5]_pattern: /data-center-service-epms/datacenter/epms/v1/picture-config/get-picture?**
      #auth-config_permitall[6]_pattern: /only-report-service/only-report/**
      auth-config_permitall[6]_pattern: /data-center-service-epms/datacenter/epms/v1/system-settings/query/language-status
      HYSTRIX_COMMAND_DEFAULT_EXECUTION_ISOLATION_THREAD_TIMEOUTINMILLISECONDS: 400000
      HYSTRIX_COMMAND_DEFAULT_REQUESTLOG_ENABLED: 'false'
      RIBBON_READTIMEOUT: 200000
      RIBBON_CONNECTTIMEOUT: 200000
      SPRING_SERVLET_MULTIPART_MAX-FILE-SIZE: 20MB
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      #是否启用本地化校验token
      cet.gateway.check-token-local: 'false'
      #是否跳过redis校验
      cet.gateway.skip-token-valid: 'false'
      #token过期时间，单位分钟
      cet.auth.token-expire-time: 43200
      #management_endpoints_web_exposure_include: health,info
      management_server_port: 8888
      CET_I18N_DIR: /config/i18n
      
  #权限服务 
  cloud-auth-service:
    image: *************/matterhorn/cloud-auth-service:${auth_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${auth_ip}
        aliases:
        - cauth1
    restart: always
    hostname: cauth1
    ports:
    - 0.0.0.0:2014:2014
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx512m -Xms256m
      SERVER_PORT: 2014
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      SPRING_DATASOURCE_DRUID_AUTH_USERNAME: ${DataBaseUser}
      SPRING_DATASOURCE_DRUID_AUTH_PASSWORD: ${DataBasePwd}
      SPRING_DATASOURCE_DRUID_AUTH_URL: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/Auth_${DBProjectName}
      SPRING_DATASOURCE_DRUID_AUTH_driver-class-name: org.postgresql.Driver
      DATABASE_TYPE: postgresql
      server_compression_enabled: 'true'
      server_compression_mime-types: 'application/json,application/xml,text/html,text/plain,text/css,application/x-javascript'
      server_compression_min-response-size: 10240
      #30天登录过期，单位分钟
      cet_auth_token-expire-time: 43200
      cet_auth_use-captcha: 'false'
      SPRING_REDIS_JEDIS_POOL_MAX-ACTIVE: 8
      SPRING_REDIS_JEDIS_POOL_MAX-IDLE: null
      SPRING_REDIS_JEDIS_POOL_MAX-WAIT: null
      cet_i18n_language: zh_cn
      cet_access-limit_timeout: 1
      cet_access-limit_limit: 100
      #management_endpoints_web_exposure_include: health,info
      management_server_port: 8888

  model-service:
    image: *************/matterhorn/model-service:${model_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${model_service_ip}
        aliases:
        - model1
    restart: always
    hostname: model1
    ports:
    - 0.0.0.0:8085:8085
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx1024m -Xms512m
      SERVER_PORT: 8085
      SERVER_TOMCAT_MAX-THREADS: 20
      SERVER_TOMCAT_MAX-HTTP-POST-SIZE: 20MB
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      SPRING_PROFILES_ACTIVE: release
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      SPRING_APPLICATION_NAME: model-service
      SPRING_REDIS_JEDIS_POOL_MAX-ACTIVE: 8
      SPRING_REDIS_JEDIS_POOL_MAX-IDLE: null
      SPRING_REDIS_JEDIS_POOL_MAX-WAIT: null
      #spring_datasource_druid_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/${MatterhornDBName}
      #spring_datasource_druid_username: ${DataBaseUser}
      #spring_datasource_druid_password: ${DataBasePwd}
      #spring_datasource_druid_driver-class-name: org.postgresql.Driver
      datasource_druid_max-active: 50
      datasource_druid_min-idle: 5
      datasource_druid_max-wait: 60000
      spring_datasource_druid_matterhorn_config_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/${MatterhornDBName}
      spring_datasource_druid_matterhorn_config_username: ${DataBaseUser}
      spring_datasource_druid_matterhorn_config_password: ${DataBasePwd}
      spring_datasource_druid_matterhorn_config_driver-class-name: org.postgresql.Driver

      spring_datasource_druid_matterhorn_history_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/${MatterhornDBName}
      spring_datasource_druid_matterhorn_history_username: ${DataBaseUser}
      spring_datasource_druid_matterhorn_history_password: ${DataBasePwd}
      spring_datasource_druid_matterhorn_history_driver-class-name: org.postgresql.Driver
      JEDIS_POOL_HOST: ${master_network_segment}.${redis_ip}
      JEDIS_POOL_PORT: ${RedisPort}
      JEDIS_POOL_PASSWORD: ${RedisPwd}
      JEDIS_POOL_TIMEOUT: 2000
      JEDIS_POOL_DATABASE: 1
      JEDIS_POOL_CONFIG_MAXTOTAL: null
      JEDIS_POOL_CONFIG_MAXIDLE: 8
      JEDIS_POOL_CONFIG_MAXWAITMILLIS: null
      INSTANCE-SHIP-QUERY_CONFIG_MAXROOTROWS: 200000
      INSTANCE-SHIP-QUERY_CONFIG_MAXLEVELS: 5
      INSTANCE-SHIP-QUERY_CONFIG_MAXROWSOFEVERYSUBLEVEL: 100000
      instance-ship-query_config_maxoffset: 200000
      cet_model-service_task_dataFillNull_cron: 0 0/10 * * * ?
      cet_model-service_task_dataFillNull_aggregationcycle:  7,12,14,17,18
      #开启查询FLINK REDIS缓存
      CET_USE-CACHE: 'true'
      #是否启动主备
      cet_base-service_cluster-assistant_enabled: '${ClusterEnable}'
      cet_base-service_cluster-assistant_url: "${LocalIP}:8888"
      #开启查询FLINK REDIS缓存
      CET_USE-CACHE: 'true'
      cet_model-service_flink-data_version: 2    # 老版本flink写入是1，新版本flink写入是2
      # 新增的flink redis查询接口, 默认flink redis查询数据量超过阈值抛异常处理
      instance-ship-query_config_trimFlinkData: 'false'
      #新增flink 中间数据查询数据量(新增接口的限制), instance-ship-query_config_maxRowsOfFlinkData, 默认是1000
      instance-ship-query_config_maxRowsOfFlinkData: 10000
      # 宽表相关配置(可都采用默认配置)
      # 单批次confId转存过期时间(单批次执行时间为分钟级, 建议设置为1~2小时). 单位为小时
      cet_model-service_cache_storage_expire-time: 2
      # CONF ID分割的规模, 默认为5000.(单日单类型数据量为60万)
      cet_model-service_cache_storage_conf-id-partition-size: 5000
      # 转存触发时间, 仅转存updateTime距离当前一个月之外的数据.(单位为日)
      cet_model-service_cache_storage_store-trigger-threshold: 30
      # 转存执行起始时间, 仅在指定起始时间之后执行转存任务(h, 包含, 默认凌晨一点之后).
      cet_model-service_cache_storage_store-execute-start-time: 1
      # 转存执行终止时间, 仅在指定终止时间之前执行转存任务(h, 不含, 默认凌晨六点之前).
      cet_model-service_cache_storage_store-execute-end-time: 6
      #对于接口请求时间超时,打印超时接口信息日志功能配置，默认不开启，如需开启配置如下
      cet_model-service_request_enabled: 'true'
      cet_model-service_request_timeout: 5000
      # 层级配置总开关(默认关闭)
      cet_model-service_configuration-update_enabled: 'true'
      # 层级配置更新开关(默认关闭)
      cet_model-service_configuration-update_update-parent: 'true'
      swagger_enabled: 'false'
      #management_endpoints_web_exposure_include: health,info
      management_server_port: 8888
      cet_model-service_redis_refresh-cache_enabled: 'true'

  device-data-service:
    image: *************/matterhorn/device-data-service:${device_data_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${device_data_service_ip}
        aliases:
        - devicedata1
    restart: always
    hostname: devicedata1
    ports:
    - 0.0.0.0:5050:5050
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx2048m -Xms512m
      CET_PECCORE_REFRESH-INTERVAL: 600000
      SERVER_PORT: 5050
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      CET_REMOTE-CONTROL_READTIMEOUT: 100000
      SPRING_RABBITMQ_ADDRESSES: ${master_network_segment}.${rabbitmq_ip}:${RabbitmqPort}
      SPRING_RABBITMQ_USERNAME: ${RabbitmqUser}
      SPRING_RABBITMQ_PASSWORD: ${RabbitmqPwd}
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      SPRING_APPLICATION_NAME: device-data-service
      database_type: postgresql
      spring_datasource_type: com.alibaba.druid.pool.DruidDataSource
      spring_datasource_druid_datalog_driver-class-name: org.postgresql.Driver
      spring_datasource_druid_datalog_username: ${DataBaseUser}
      spring_datasource_druid_datalog_password: ${DataBasePwd}
      spring_datasource_druid_datalog_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/PECSTAR_DATALOG_${DBProjectName}
      spring_datasource_druid_config_driver-class-name: org.postgresql.Driver
      spring_datasource_druid_config_username: ${DataBaseUser}
      spring_datasource_druid_config_password: ${DataBasePwd}
      spring_datasource_druid_config_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/PECSTAR_CONFIG_${DBProjectName}
      spring_datasource_druid_data_driver-class-name: org.postgresql.Driver
      spring_datasource_druid_data_username: ${DataBaseUser}
      spring_datasource_druid_data_password: ${DataBasePwd}
      spring_datasource_druid_data_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/PECSTAR_DATA_${DBProjectName}
      spring_datasource_druid_business_username: ${DataBaseUser}
      spring_datasource_druid_business_password: ${DataBasePwd}
      spring_datasource_druid_business_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/${MatterhornDBName}
      #压缩返回结果
      server_compression_enabled: 'true'
      server_compression_mime-types: 'application/json,application/xml,text/html,text/plain,text/css,application/x-javascript'
      server_compression_min-response-size: 10240      
      cet_webview_realTimeMeasureRefreshInterval: 2000
      cet_webview_enableRemote: 1
      #秒级定时记录数据源 0-关系数据库;1-VM;2-InfluxDB
      cet_datalog_second_database-type: 1
      cet_datalog_second_vm[0]_url: http://${VMIP1}:${VMPort1}
      cet_datalog_second_vm[0]_metric: "cet_Value"
      cet_datalog_second_vm[1]_url: http://${VMIP2}:${VMPort2}
      cet_datalog_second_vm[1]_metric: "cet_Value"
      cet_datalog_normal_database-type: 1
      cet_datalog_normal_vm[0]_url: http://${NormalVMIP1}:${NormalVMPort1}
      cet_datalog_normal_vm[0]_metric: "cet_Value"
      cet_datalog_normal_vm[1]_url: http://${NormalVMIP2}:${NormalVMPort2}
      cet_datalog_normal_vm[1]_metric: "cet_Value"
      cet_datalog_high_database-type: 1
      cet_datalog_high_vm[0]_url: http://${NormalVMIP1}:${NormalVMPort1}
      cet_datalog_high_vm[0]_metric: "cet_Value"
      cet_datalog_high_vm[1]_url: http://${NormalVMIP2}:${NormalVMPort2}
      cet_datalog_high_vm[1]_metric: "cet_Value"
      #2表示influxdb版本为2.x,1则为1.x版本
      #cet_datalog_influx_version: 1
      #cet_datalog_influx_enabled: 'true'
      #cet_datalog_influx_database: PECSTAR_DATALOG_${DBProjectName}_S
      #cet_datalog_influx_second-datalog-table-name: s
      #cet_datalog_influx_dblist[0]_url: http://${InfluxdbIP1}:${InfluxdbPort1}
      #cet_datalog_influx_dblist[0]_user: "111"
      #cet_datalog_influx_dblist[0]_password: "111"
      #实时数据获取地址
      cet_peccore_realtimeurls[0]: http://${PecServiceIP1}:4118
      
      #主备控制
      cet_base-service_cluster-assistant_enabled: '${ClusterEnable}'
      cet_base-service_cluster-assistant_url: "${LocalIP}:8888"
      SPRING_REDIS_JEDIS_POOL_MAX-ACTIVE: 8
      SPRING_REDIS_JEDIS_POOL_MAX-IDLE: null
      SPRING_REDIS_JEDIS_POOL_MAX-WAIT: null
      CET_DATACACHELIBSERVER_USEORIGINALDATALOGAPI: 'true'
      cet_peccore_realtimeReadTimeout: 10000
      management_endpoints_web_exposure_include: health,info
      #management_server_port: 8888

  flink-dc:
    image: *************/datacenter/flink-dc:${flink_version}
    hostname: flink-dc
    cap_add:
     - SYS_PTRACE
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${flink_ip}
        aliases:
        - flink-dc
    container_name: flink-dc
    restart: always
    hostname: flink-dc
    ports:
      - "0.0.0.0:18081:8081"
      - "0.0.0.0:18082:8082"
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    volumes:
      #- /opt/CET/Common/flink-1.12.5:/opt/CET/Common/flink-1.12.5
      - /var/log/CET/docker/flink/log:/opt/CET/Common/flink-1.12.5/log
      - /etc/CET/docker/flink/checkpoint:/opt/CET/Common/flink-1.12.5/checkpoint
      - /etc/CET/docker/flink/savepoint:/opt/CET/Common/flink-1.12.5/savepoint
      - /etc/CET/docker/flink/conf:/opt/CET/Common/flink-1.12.5/conf
      - /etc/CET/docker/flink/bin/entrypoint.sh:/opt/CET/Common/flink-1.12.5/bin/entrypoint.sh
      - /etc/CET/docker/flink/output:/opt/CET/Common/flink-1.12.5/output
      - /etc/CET/docker/flink/recalcdata:/opt/CET/Common/flink-1.12.5/recalcdata
      - /etc/CET/docker/flink/bin/run-job.sh:/opt/CET/Common/flink-1.12.5/bin/run-job.sh
    environment:
      TZ: Asia/Shanghai
      WAIT_HOSTS: ${master_network_segment}.${redis_ip}:6379,${master_network_segment}.${model_service_ip}:8085,${master_network_segment}.${device_data_service_ip}:5050
      management_endpoints_web_exposure_include: health,info
      
  flink-engine:
    image: *************/base/flink-engine:1.0.7
    ipc: host
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${flink_ip}
        aliases:
          - flinkEngine
    restart: always
    ports:
      - 18085:8081
      - 18084:18084
      #- 18082:8082
      - 26123:6123
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    volumes:
      - /etc/CET/docker/flink-1.14/conf:/opt/CET/Common/flink/conf
        # - /etc/CET/docker/flink-1.14/bin/entrypoint.sh:/opt/CET/Common/flink/bin/entrypoint.sh
        # - /var/log/CET/docker/flink-1.14/checkpoint:/opt/CET/Common/flink/checkpoint
        # - /etc/CET/docker/flink-1.14/output:/opt/CET/Common/flink/output
      - /var/log/CET/docker/flink-1.14/log:/opt/CET/Common/flink/log
        # - /etc/CET/docker/flink-1.14/savepoint:/opt/CET/Common/flink/savepoint
    environment:
      WAIT_HOSTS: ${master_network_segment}.${redis_ip}:6379,${master_network_segment}.${model_service_ip}:8085,${master_network_segment}.${device_data_service_ip}:5050
      TZ: Asia/Shanghai
      SPRING_PROFILES_ACTIVE: prod


#数据中心服务
  data-center-service-epms:
    image: *************/datacenter/datacenter-service-se-epms:${epms_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${data_center_service_ip}
        aliases:
        - datacenter-epms
    restart: always
    hostname: datacenter-epms
    ports:
    - 0.0.0.0:8066:8066
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    volumes:
    - /var/cache/CET/databaseback/manual/data/:/var/cache/CET/databaseback/manual/data/
    - /var/cache/CET/databaseback/manual/logs/:/var/cache/CET/databaseback/manual/logs/
    - /var/cache/CET/databaseback/auto/data/:/var/cache/CET/databaseback/auto/data/
    - /var/cache/CET/databaseback/auto/logs/:/var/cache/CET/databaseback/auto/logs/
    - /etc/CET/docker/databaseback/:/etc/CET/docker/databaseback/
    #文件导出
    - /var/cache/CET/filemanager/attachmentFile:/attachmentFile
    - /var/cache/CET/filemanager/picture:/var/cache/CET/picture
    - /var/cache/CET/filemanager/kb:/etc/CET/kb
    - /var/cache/CET/filemanager/chrony/chrony.conf:/templates/chrony.conf
    #v3.3-odcc配置
    - /var/cache/CET/filemanager/odcc/:/odcc/
    #v3.5-模拟演练配置
    - /var/cache/CET/filemanager/exercise:/var/cache/CET/exercise
    command: sh -c "/wait && java -Xmx3072m -Xms512m -Djava.security.egd=file:/dev/./urandom -jar -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 /datacenter-service-se-epms.jar"
    environment:
      WAIT_HOSTS: ${master_network_segment}.${service_center_ip}:1001,${master_network_segment}.${device_data_service_ip}:5050,${master_network_segment}.${model_service_ip}:8085,${master_network_segment}.${gateway_ip}:4001
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx3072m -Xms512m
      SERVER_PORT: 8066
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      SPRING_DATASOURCE_DATA_DRIVER-CLASS-NAME: org.postgresql.Driver
      SPRING_DATASOURCE_DATA_USERNAME: ${DataBaseUser}
      SPRING_DATASOURCE_DATA_PASSWORD: ${DataBasePwd}
      SPRING_DATASOURCE_DATA_URL: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/${DBProjectName}
      spring_datasource_username:  ${DataBaseUser}
      spring_datasource_password: ${DataBasePwd}
      spring_datasource_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/activiti
      SPRING_APPLICATION_NAME: data-center-service-epms
      SPRING_PROFILES_ACTIVE: prod
      SPRING_SERVLET_MULTIPART_MAX-FILE-SIZE: 20MB
      SPRING_REDIS_JEDIS_POOL_MAX-ACTIVE: 8
      SPRING_REDIS_JEDIS_POOL_MAX-IDLE: null
      SPRING_REDIS_JEDIS_POOL_MAX-WAIT: null

      #蓄电池内阻测试发送遥控命令用户名和密码
      DEFAULT_USERNAME: ${BatteryTestUser}
      DEFAULT_USERPASSWORD: ${BatteryTestPwd}
      battery_dataid_soc: 6007303
      #自动确认
      scheduling_alarmAutoConfirmEnable: 'true'
      #小铃铛统计未确认未恢复告警数量
      scheduling_alarmRingDamon: 'true'
      scheduling_alarmConvergenceDataCleanEnable: 'true'
      #波形匹配任务
      scheduling_eventWaveMatch: 'true'    
      #容量统计
      scheduling_capacityDistribution: 'true'
      #返回事件自动确认补录
      scheduling_endEventAutoConfirmEnable: 'true'
      #自动备份数据库
      scheduling_databackup: 'false'
      scheduling_dbDataBackupEnable: 'false'
      #告警总览数据预处理
      scheduling_alarmOverviewCalculateEnable: 'true'
      #告警状态流转
      scheduling_eventAlarmEnable: 'true'
      scheduling_fileSyncronize: 'true'
      #onlyreport同步配置
      scheduling_onlyReportConfigSync: 'true'
      scheduling_batteryMonitorCache: 'true'
      #配置测点策略事件定时产生开关
      scheduling_safetyEvent: 'true'
      #定时任务的cron
      taskcron_linkageStrategy: 0/30 * * * * ?
      taskcron_dbsnapshot: 0 0 0 1/1 * ?
      taskcron_fileSyncronize: 0 */3 * * * ?
      taskcron_eventWaveMatch: 0/10 * * * * ?
      taskcron_cabinetUseAnalysis: 0 5 0 1/1 * ?
      taskcron_endEventAutoConfirmCron: 0 */10 * * * ?
      #task_createAISchedulePlan: '0 */1 * * * ?'
      taskcron_cabinetElecCurrentTrack: '-'
      taskcron_alarmOverviewCalculateCron: 0 */5 * * * ?
      taskcron_onlyReportConfigSyncCron: 0 0/3 * * * ?
      taskcron_batteryMonitorCache: 0 0/2 * * * ?
      #数据库备份配置
      DATABACKUP_CMDPATH: /etc/CET/docker/databaseback
      DATABACKUP_ROOTPATH: /var/cache/CET/databaseback
      DATABACKUP_PROJECTNAME: ${DBProjectName}
      DATABACKUP_SNAPSHOTCAPACITY: 5

      itrack_folderpath: /etc/CET/itrackImportFilePath
      #原始事件0转换后等级
      event_level_conversion_value: 4
      #支路名称开关
      realtime_branchnameflag: 'true'
      #根据PecService个数配置下方PecService连接，每个连接用英文逗号隔开
      pecservice_endpoint: http://${PecServiceIP1}:4118
      #文件同步配置
      fileSyncronized_targetFileDir: /var/cache/CET/filemanager/
      fileSyncronized_targetUrl: ${RemoteIP}
      #数据同步
      ssh_ip: ${LocalIP}
      ssh_user: root
      ssh_password: ${rootPwd}
      cet_configserver_host: ${MasterFrontSCIP}
      cet_configserver_port: 4115
      #主备控制
      cet_base-service_cluster-assistant_enabled: '${ClusterEnable}'
      cet_base-service_cluster-assistant_url: "${LocalIP}:8888"
      
      #电能质量页面获取项电压还是线电压(phase为相电压，其他其他为线电压)
      pq_harmonictype: 其他
      #UPS使用哪个回路的功率值显示,默认使用回路2
      ups_powerLogicalid: 2
      #视频服务连接地址
      videoservice_host: http://${LocalIP}:8082
      videoservice_ip: ${LocalIP}
      videoservice_port: 8082
      switch_overturn: '${switch_overturn}'
      ##订阅个数配置上限配置量，默认10条
      alarmsubscrip_config_max: 9
      scheduling_changeStorageSyncEnable: 'true'
      taskcron_changeStorageSyncCron: 0 0 3 * * ?
      scheduling_communicationManagementSyncEnable: 'true'
      taskcron_communicationManagementSyncCron: 0 0/15 * * * ?
      onlyreportservice_host: http://${master_network_segment}.${only_report_server_ip}
      onlyreportservice_port: 4000
      # 知识库附件存放路径
      folder_kb-attachment-path: /etc/CET/kb
      # 附件文件夹大小
      folder_attachment-max-size: 20 
      #蓄电池定时任务的cron
      scheduling_batteryMonitorCache: 'true'
      taskcron_batteryMonitorCache: 0 0/2 * * * ?
      configservice_host: http://${MasterFrontSCIP}:4119,http://${SlaveFrontSCIP}:4119
      serverStatus_url_prometheus: ${postgresIP}:9091,${postgresSlaveIP}:9091
      #v3.3-odcc配置
      odcc_config_path: /odcc/odcc-config.xlsx
      odcc_push_host: http://${LocalIP}:8764
      arraycabinet_new_config_scheme: 'true'
      locale_type: 1
      #v3.5-模拟演练配置
      exercise_plan_maxlimit: 200
      exercise_plan_limit: 15
      exercise_content_limit: 3
      swagger_enable: 'false'
      #management_endpoints_web_exposure_include: health,info
      logging_file_name: logs/data-center-service-epms.log
      management_server_port: 8888
      cet_i18n_language: zh_CN
      data_center_log_type: 1

#  数据中心告警事件服务    
  datacenter-common-alarmer: 
    image: *************/datacenter/datacenter-common-alarmer:${alarmer_version}
    cap_add:
        - SYS_ADMIN
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${alarmer_ip}
        aliases:
        - datacenter-alarmer
    restart: always
    hostname: datacenter-alarmer
    ports:
    - 0.0.0.0:8166:8166
    - 0.0.0.0:1299:1299
    volumes:
    - /etc/CET/docker/alarmer/:/var/cache/CET/
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 15s
      restart_policy:
        condition: on-failure
      resources:
        limits:
          memory: 6G
    command: sh -c "/wait && java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar -Xmx1024m -Xms512m -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 /datacenter-common-alarmer.jar"
    environment:
      WAIT_HOSTS: ${master_network_segment}.${service_center_ip}:1001,${master_network_segment}.${model_service_ip}:8085,${master_network_segment}.${gateway_ip}:4001,${master_network_segment}.${notice_ip}:5070
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx1024m -Xms512m
      SERVER_PORT: 8166
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      #SPRING_REDIS_JEDIS_POOL_MAX-WAIT: '50000'
      SPRING_REDIS_JEDIS_POOL_MAX-ACTIVE: 8
      SPRING_REDIS_JEDIS_POOL_MAX-IDLE: null
      SPRING_REDIS_JEDIS_POOL_MAX-WAIT: null
      SPRING_PROFILES_ACTIVE: prod
      SPRING_APPLICATION_NAME: data-center-service-alarmer
      cet_base-service_cluster-assistant_url: "${LocalIP}:8888"
      cet_base-service_cluster-assistant_enabled: '${ClusterEnable}'
      #是否开启事件匹配
      scheduling_alarmFalshBack: 'true'
      cet_pecservice_url: ${MasterFrontSCIP}:4118
      local-file_port: 4500
      local-file_urls: ${MasterFrontSCIP}
      local-file_enable: 'false'
      #厂站ID
      local-file_staid: 1
      #任务轮询获取事件间隔周期，单位毫秒
      cet_smsevent_fixed-delay: 5000
      #短信有效期范围，单位秒
      sms_msg-expirationtime: 600
      #单个手机号每小时可发送的短信条数---定时任务
      sms_cache-flowvelocity: 5
      #定时任务，每30秒间隔执行周期。两个逻辑1.清理超过10分钟的发送记录；2.刷新订阅配置缓存
      scheduling_alarmSmsDataClean: 'true'
      #是否启用超量事件推送方案，默认为false，即不启用，使用之前的推送方案；启用，则配置为true
      cet_alarm-event_scheme_excess_enabled: 'true'
      #一次推送事件的最大条数，默认为100
      cet_alarm-event_limit: 100
      #事件推送间隔，默认为100，单位为毫秒
      cet_alarm-event_fixed-delay: 1000
      #事件缓存更新间隔，默认为100，单位为毫秒
      cet_alarm-event_cache_fixed-delay: 1000
      #事件缓存更新一次读取的最大事件条数，默认为1000
      cet_alarm-event_cache_limit: 1000
      # 事件缓存的长度，默认为200
      cet_alarm-event_cache_size: 200
      #是否按照用户权限推送，默认为false，即不按照用户权限推送
      cet_auth-push: 'false'
      #在线用户id列表更新间隔，默认为1000，单位为毫秒
      cet_alarm-event_online-user_update_fixed-delay: 1000
      #保护模式限制
      protect-mode_limit: 4000000
      #保护模式预警事件限制
      protect-mode_pre-limit: 3000000
      #是否为dcim系统
      combination_dcim: '${DCIMenable}'
      scheduling_pushsmsturnoff: 1
      #未匹配告警入库时间
      event-limit_store-limit: 24
      #开关量反转匹配
      switch_overturn: '${switch_overturn}'
      #消息通知播报方式，baidu表示百度的语音播报方式，default表示默认播报方式
      alarm-name_type: ${alarm_type}
      #语音播报对应关系表，默认路径无需修改
      alarm-name_device-name_file: /var/cache/CET/VoiceBroadcastRelationshipTable.xlsx
      alarm-name_double-di_file: /var/cache/CET/doubleDI.xlsx
      #百度短信模板不包含content变量(除百度现场，其他现场都要配)
      sms_template_cust: ''
      onlyreportservice_host: http://${master_network_segment}.${only_report_server_ip}
      onlyreportservice_port: 4000
      #短信定时任务
      sms_swtich: 'false'
      #management_endpoints_web_exposure_include: health,info
      logging_file_name: logs/data-center-service-alarmer.log
      management_server_port: 8888

#数据中心web
  dcim-web:
    image: *************/lowcode/flowchart-web:v1.0.16
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${dcim_web_ip}
        aliases:
        - dcim-web
    restart: always
    hostname: dcim-web
    extra_hosts:
      - "sloth:${docker0}"
      - "backup:${RemoteIP}"
    ports:
    - 0.0.0.0:9090:80
    volumes:
    # - /etc/CET/docker/web/static/deviceimg:/usr/share/nginx/html/static/deviceimg
    # - /etc/CET/docker/web/static/assets:/usr/share/nginx/html/static/assets
    # - /etc/CET/docker/web/static/alarmsound:/usr/share/nginx/html/static/alarmsound
    # - /etc/CET/docker/web/static/SystemCfg.json:/usr/share/nginx/html/static/SystemCfg.json
    # - /etc/CET/docker/web/static/RealtimeDataCfg.json:/usr/share/nginx/html/static/RealtimeDataCfg.json
    - /etc/CET/docker/nginx/nginx.conf:/etc/nginx/nginx.conf
    #- /etc/CET/docker/web/static/DataQueryCfg.json:/usr/share/nginx/html/static/DataQueryCfg.json
    #- /etc/CET/docker/web/static/navmenu.json:/usr/share/nginx/html/static/navmenu.json
    depends_on:
    - device-data-service
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      
  #filemanager-service服务
  filemanager-service:
    image: *************/matterhorn/filemanager-service:${filemanager_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${filemanager_ip}
        aliases:
        - filemanager1
    restart: always
    hostname: filemanager1
    ports:
    - 0.0.0.0:8200:8200
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    volumes:
    - /var/cache/CET/filemanager:/user/file
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx512m -Xms256m
      SERVER_PORT: 8200
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      SPRING_RABBITMQ_ADDRESSES: ${master_network_segment}.${rabbitmq_ip}:${RabbitmqPort}
      SPRING_RABBITMQ_USERNAME: ${RabbitmqUser}
      SPRING_RABBITMQ_PASSWORD: ${RabbitmqPwd}
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      SPRING_PROFILES_ACTIVE: prod
      management_endpoints_web_exposure_include: health,info

  notice-service:
    image: *************/matterhorn/notice-service:${notice_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${notice_ip}
        aliases:
        - notice1
    restart: always
    hostname: notice1
    ports:
    - 0.0.0.0:5070:5070
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx1024m -Xms256m
      SERVER_PORT: 5070
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      SPRING_RABBITMQ_ADDRESSES: ${master_network_segment}.${rabbitmq_ip}:${RabbitmqPort}
      SPRING_RABBITMQ_USERNAME: ${RabbitmqUser}
      SPRING_RABBITMQ_PASSWORD: ${RabbitmqPwd}
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      spring_datasource_config_driver-class-name: org.postgresql.Driver
      spring_datasource_config_username: ${DataBaseUser}
      spring_datasource_config_password: ${DataBasePwd}
      spring_datasource_config_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/${MatterhornDBName}
      CET_NOTICE_MSGTIMESPAN: 0
      CET_NOTICE_MSGMAXCOUNT: 0
      spring_autoconfigure_exclude[0]: org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration
      SPRING_PROFILES_ACTIVE: prod
      cet_notice_msglog_web_enabled: 'false'
      logging_level_WebSocketServer: warn
      SPRING_REDIS_JEDIS_POOL_MAX-ACTIVE: 8
      SPRING_REDIS_JEDIS_POOL_MAX-IDLE: null
      SPRING_REDIS_JEDIS_POOL_MAX-WAIT: null
      cet_notice_sms_mobile_enabled: 'true'    #默认不启动针对手机号发送短信的限制
      cet_notice_sms_mobile_maxPerDay: 20    #默认每个自然日内给手机号发送最多20条短信
      cet_notice_sms_mobile_maxPerMin: 2    #默认一分钟内给手机号发送最多2条短信制
      cet_notice_zht_ip: '${zhtIP}' #纵横通设备的ip，默认*************
      cet_notice_sessionMaxCount: 10
      cet_notice_midea_api_ak: ''
      cet_notice_midea_address: ''
      cet_notice_midea_secret: ''
      management_endpoints_web_exposure_include: health,info
      cet_notice_encryption_enabled: 'true'
      #management_server_port: 8888
            
#节点服务
  pec-node-service:
    image: *************/matterhorn/pec-node-service:${pec_node_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${pec_node_service_ip}
        aliases:
        - pec-node-service
    restart: always
    hostname: pec-node-service
    ports:
    - 0.0.0.0:8180:8180
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx4096m -Xms4096m -XX:SurvivorRatio=5 -XX:MetaspaceSize=192m -XX:ParallelGCThreads=10 -Xmn2048m 
      FEIGN_CONNECT_TIMEOUT: 15000
      FEIGN_READ_TIMEOUT: 15000
      RIBBON_MAX_AUTO_RETRIES: 0
      RIBBON_MAX_AUTO_RETRIES_NEXT_SERVER: 0
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      SPRING_APPLICATION_NAME: pec-node-service
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      spring_datasource_type: com.alibaba.druid.pool.DruidDataSource
      spring_datasource_druid_config_driver-class-name: org.postgresql.Driver
      spring_datasource_druid_config_username: ${DataBaseUser}
      spring_datasource_druid_config_password: ${DataBasePwd}
      spring_datasource_druid_config_removeAbandoned: 'false'
      spring_datasource_druid_config_url: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/PECSTAR_CONFIG_${DBProjectName}  
      cet_datasync_pec-device-extend_enabled: 'false'
      cet_datasync_pec-device-extend_cron: '*/5 * * * * ?'        
      cet_nodecache_enabled: 'true'
      #压缩返回结果
      server_compression_enabled: 'true'
      server_compression_mime-types: 'application/json,application/xml,text/html,text/plain,text/css,application/x-javascript'
      server_compression_min-response-size: 10240
      cet_base-service_cluster-assistant_url: "${LocalIP}:8888"
      cet_base-service_cluster-assistant_enabled: '${ClusterEnable}'
      SPRING_REDIS_JEDIS_POOL_MAX-ACTIVE: 8
      SPRING_REDIS_JEDIS_POOL_MAX-IDLE: null
      SPRING_REDIS_JEDIS_POOL_MAX-WAIT: null
      cet_configserver_url: ${MasterFrontSCIP}:4116
      cet_base-service_config-server_url: ${PecServiceIP1}:4119
      cet_configserver_notice_url: ${PecServiceIP1}:4116,${PecServiceIP2}:4116
      cet_warn-node_cache_enabled: 'true'
      SPRING_PROFILES_ACTIVE: prod
      tx-lcn_client_enableTxlcn: 'false'
      management_endpoints_web_exposure_include: health,info
      #management_server_port: 8888

# 报表服务
  data-center-report-service:
    image: *************/datacenter/epms-report-service:${report_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${report_service_ip}
        aliases:
        - datacenter-report-service
    restart: always
    hostname: datacenter-report-service
    ports:
    - 0.0.0.0:8099:8099
    volumes:
    - /var/cache/CET/filemanager/attachmentFile:/attachmentFile
    - /var/cache/CET/filemanager/itrackImportFilePath:/etc/CET/itrackImportFilePath
    - /var/cache/CET/filemanager/report/auto/data/:/var/cache/CET/report/auto/data/
    - /var/cache/CET/filemanager/report/custom/data:/var/cache/CET/report/custom/data
    environment:
      WAIT_HOSTS: ${master_network_segment}.${service_center_ip}:1001,${master_network_segment}.${device_data_service_ip}:5050,${master_network_segment}.${model_service_ip}:8085,${master_network_segment}.${gateway_ip}:4001
      TZ: Asia/Shanghai
      JAVA_OPTS: -Xmx4048m -Xms1024m
      SERVER_PORT: 8099
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      SPRING_REDIS_HOST: ${master_network_segment}.${redis_ip}
      SPRING_REDIS_PORT: ${RedisPort}
      SPRING_REDIS_PASSWORD: ${RedisPwd}
      SPRING_SERVLET_MULTIPART_MAX-FILE-SIZE: 20MB
      #根据PecService个数配置下方PecService连接，每个连接用英文逗号隔开
      pecservice_endpoint: http://${PecServiceIP1}:4118
      SPRING_APPLICATION_NAME: data-center-report-service
      SPRING_PROFILES_ACTIVE: prod
      itrack_folderpath: /etc/CET/itrackImportFilePath
      auto_folderpath: /var/cache/CET/report/auto/data
      custom_folderpath: /var/cache/CET/report/custom/data
      logging_level_com_cet: info
      #Oppo定制报表，默认为false，oppo现场为true
      REPORT_ENERGYCONSUMPTION_ENABLE: 'false'
      cet_base-service_cluster-assistant_url: "${LocalIP}:8888"
      cet_base-service_cluster-assistant_enabled: '${ClusterEnable}'
      HYSTRIX_COMMAND_DEFAULT_EXECUTION_ISOLATION_THREAD_TIMEOUTINMILLISECONDS: 300000
      RIBBON_READTIMEOUT: 200000
      RIBBON_CONNECTTIMEOUT: 30000
      task_itcabinetcache_enable: 'true'
      #是否为dcim系统
      combination_dcim: '${DCIMenable}'
      powerCapacityReport_enable: 'true'
      task_itcabinetcache_cron: 0 0 10,14,19 * * ?
      refrigerating-output_submeter-types[0]: 67
      refrigerating-output_submeter-types[1]: 68
      it-report_pue-max-room-count: 10
      #限制IT机柜最值/时刻值单证报表机柜最大数量
      it-report.limit: 600
      roomMaximumReport_enable: 'true'
      onlyreportservice_host: http://${master_network_segment}.${only_report_server_ip}:4000
      #management_endpoints_web_exposure_include: health,info
      logging_file_name: logs/data-center-report-service.log
      management_server_port: 8888

  only-report-docker-service:
    image: *************/matterhorn/only-report-docker-service:${only_report_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${only_report_server_ip}
        aliases:
          - only-report-service
          - kkfileview
    hostname: only-report-service
    restart: always
    links:
      - service-center-1:discovery1
    ports:
      - 0.0.0.0:4000:4000
      - 0.0.0.0:8012:8012
    volumes:
      # 模板文件映射目录
      - /var/cache/CET/filemanager/only-report/excelTemp:/excelTemp
      # mthmeta.json以及reportType.json映射目录
      - /etc/CET/only-report/config:/config
    deploy:
      replicas: 1
    environment:
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
      JAVA_OPTS: -Xmx2048m -Xms1024m
      REPORT_HOST: only-report-service
      #EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://discovery1:1001/eureka
      EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://${SpringUserName}:${SpringPassWord}@${master_network_segment}.${service_center_ip}:1001/eureka/
      EUREKA_INSTANCE_PREFER-IP-ADDRESS: 'true'
      CET_REPORT_MATTERHORN_META: file:./config/mthmeta.json
      CET_REPORT_REPORTTYPE: file:./config/reportType.json
      SPRING_MVC_STATIC-PATH-PATTERN: /**
      CET_REPORT_TOOL_VIEWPATH: "http://kkfileview:8012/"
      CET_REPORT_P35: "false"
      CET_REPORT_MAX-EXCEL-VIEW-SIZE: 500
      # 默认为false，会从设备数据服务获取监测设备节点树，设置为true，则从配置表中获取监测设备节点树
      CET_REPORT_MONITOR-DEVICE-TYPE-ENABLE: "true"
      #报表类型关联绑定配置，true为绑定，默认为false
      CET_REPORT_TYPE-BIND: "true"
      # 临时导出文件在docker中的目录
      CET_REPORT_TEMPDIR: "./tempFile"
      SPRING_APPLICATION_NAME: only-report-service
      #CET_BASE-SERVICE_DEVICE-DATA-SERVICE_URL: http://************:5050
      #CET_BASE-SERVICE_MODEL-SERVICE_URL: http://************:8085
      #是否使用flink能耗数据 true从flink查询，false从kettle统计，设置配置项redisSearch.enable:false。设置为true时仅能支持energyconsumption等5张表的查询，暂不支持产量等的查询
      redisSearch_enable: "false"
      redisSearch_flinkEnable: 'true'
      SPRING_DATASOURCE_DRUID_URL: jdbc:postgresql://${DataBaseUrl}:${DataBasePort}/${MatterhornDBName}
      SPRING_DATASOURCE_DRUID_USERNAME: ${DataBaseUser}
      SPRING_DATASOURCE_DRUID_PASSWORD: ${DataBasePwd}
      # 1.4.0之前版本的设备数据服务需要设置为false
      device-data-service_precise: 'true'
      report_limit_maxCellCountHtml: 30000
      report_limit_minCellCountStream: 500000
      report_limit_maxCellCountStream: 4000000
      management_endpoints_web_exposure_include: health,info
 
  bff-service:
    image: *************/front-frame/bff-service:v1.4.0
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${bff_server_ip}
        aliases:
          - bff-service
    restart: always
    hostname: bff-service
    ports:
      - 0.0.0.0:3005:3005
    volumes:
    - /var/cache/CET/filemanager/bff:/home/<USER>/var
    environment:
      # 前端认证密码
      ADMIN_PASSWORD: sA123456@
      # 服务启动端口
      SERVICE_PORT: 3005
    deploy:
      replicas: 1
      update_config:
        parallelism: 2
        delay: 10s
      restart_policy:
        condition: on-failure

  #视频服务
  video-management-service:
    image: *************/matterhorn/video-management-service:${video_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${video_service_ip}
        aliases:
        - video
        - media
    restart: always
    hostname: video
    volumes:
    - /usr/share/zoneinfo/Asia:/usr/share/zoneinfo/Asia
    - ./mediaServer/record/:/record
    - ./sqlite:/sqlite
    ports:
    - 0.0.0.0:8082:8082
    - 0.0.0.0:554:554
    - 0.0.0.0:332:332
    - 0.0.0.0:1935:1935
    - 0.0.0.0:19350:19350
    - 0.0.0.0:80:80
    - 0.0.0.0:443:443
    - 0.0.0.0:9000:9000
    - 0.0.0.0:10000:10000
    environment:
      TZ: Asia/Shanghai
      SQLCONF_USER: ${DataBaseUser}
      SQLCONF_PASSWORD: ${DataBasePwd}
      SQLCONF_HOST: ${DataBaseUrl}
      SQLCONF_PORT: ${DataBasePort}
      SQLCONF_NAME: ${MatterhornDBName}
      SQLCONF_TYPE: pg
      EUREKA_USER: ${SpringUserName}
      EUREKA_PASSWORD: ${SpringPassWord}
      EUREKA_SERVICEURL: http://${master_network_segment}.${service_center_ip}:1001
      SERVER_USEREGISTRY: 'true'
      SERVER_DEVELOPERMODE: 'false'
      management_endpoints_web_exposure_include: health,info

 #性能监控-普罗米修斯服务
  prometheus:
    image: *************/base/cet-prometheus:v2.22.2
    container_name: prometheus
    volumes:
  # 默认采集本机，如果要采集多个服务器，需要挂载prometheus.yml并扩展采集job项
      - /etc/CET/docker/monitor/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - /var/cache/CET/prometheus_data:/prometheus
      - /etc/localtime:/etc/localtime:ro
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${prometheus_ip}
        aliases:
        - prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-admin-api'
  #   - '--storage.tsdb.retention.time=30d'
  #   - '--web.enable-lifecycle'
  #   - '--storage.tsdb.max-block-duration=1h'
  #   - '--storage.tsdb.min-block-duration=1h'
    ports:
      - "0.0.0.0:9091:9090"
    restart: always
    user: root
    environment:
      TZ: Asia/Shanghai
      management_endpoints_web_exposure_include: health,info

  grafana:
    container_name: grafana
    image: *************/matterhorn/cet-grafana:${grafana_version}
    ports:
      - 0.0.0.0:3000:3000
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${grafana_ip}
        aliases:
        - grafana
    restart: always
    user: root
    environment:
      TZ: Asia/Shanghai
      ##true开启匿名登录，需要登录页强制输入账号时使用false
      GF_AUTH_ANONYMOUS_ENABLED: "true"
      ##true开启匿名登录，需要登录页强制输入账号时使用false
      GF_AUTH_PROXY_ENABLED: "true"
      GF_SECURITY_ALLOW_EMBEDDING: "true"

 #性能监控-alertmanager
  alertmanager:
    image: *************/base/alertmanager:v0.21.1
    container_name: alertmanager
    hostname: alertmanager
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${alertmanager_ip}
        aliases:
        - alertmanager
    volumes:
    - /etc/localtime:/etc/localtime:ro
    ports:
      - "0.0.0.0:9093:9093"
    restart: always
    environment:
      TZ: Asia/Shanghai

  sloth-monitor-server:
    image: *************/matterhorn/sloth-monitor-server:${sloth_monitor_version}
    networks:
      eureka-net:
        ipv4_address: ${master_network_segment}.${sloth_monitor_server_ip}
        aliases:
          - sloth
    restart: always
    hostname: sloth
    extra_hosts:
    # docker0的ip，一般固定*************
      - "docker0:${docker0}"
    ports:
      - "0.0.0.0:8078:8078"
      - "0.0.0.0:9273:9273"
    volumes:
      - "/sys:/host/sys"
      - "/proc:/host/proc"
      - "/var:/host/var"
      - "/run:/host/run"
      - "/dev:/host/dev"
      - "/etc:/host/etc"
      - "/:/host"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "/etc/passwd:/etc/passwd"
    privileged: true 
    environment:
      TZ: Asia/Shanghai
      SLOTH_DATABASE_IP: ${DataBaseUrl}
      #告警存储功能相关，默认false关闭，默认数据库为本机    前端没有存储则需要关闭
      DATABASE.ENABLE: "true"
      #数据库IP
      DATABASE.HOST: ${DataBaseUrl}
      #告警存储使用的数据库密码 对应application配置里的，使用数据库
      DATABASE.PASSWORD: ${AlarmDataBasePwd}
      #告警存储功能相关，默认false关闭，默认redis为本机   
      REDIS.ENABLE: "true"
      #主备环境，关联cluster-assistant服务，默认false关闭。数据中心主备双机应开启
      ALARM.HAENV: ${ClusterEnable}
      #当前机器IP，用于grafana中选择设备
      SLOTH_CET_NODE: ${LocalIP}
      DATABASE.DBNAME: ${MatterhornDBName}
      SLOTH_PROCSTAT_USER[0]: ${LinuxUserName}
      #监控的数据库密码 对应conf里的，监控数据库
      SLOTH_DATABASE_PASSWORD: ${DataBasePwd}
      #用于跳过某个指标类采集，默认全部采集,下方提供支持的指标类选项
      #SLOTH_INPUTS_SKIP: cpu,disk,diskio,kernel,mem,processes,swap,system,net,postgresql,postgresql_extensible,docker,redis,rabbitmq,procstat
      SLOTH_INPUTS_SKIP: docker,redis,rabbitmq

networks:
  eureka-net:
    driver: bridge
    ipam:
      driver: default
      config:
      - subnet: ${master_network_segment}.0/24
