FROM 10.12.135.233/base/flink-engine:1.0.0
COPY target/flink-engine-1.0-SNAPSHOT.jar /opt/CET/Common/flink/lib/
COPY target/flink-job-service.jar /opt/CET/Common/flink/jobservice/
COPY bin/ /opt/CET/Common/flink/bin/
COPY bin/start.sh /opt/CET/Common/flink/jobservice/
COPY conf/ /opt/CET/Common/flink/conf/
RUN chmod 755 /opt/CET/Common/flink/bin/* /opt/CET/Common/flink/jobservice/*
ENTRYPOINT [ "/opt/CET/Common/flink/bin/entrypoint.sh" ]