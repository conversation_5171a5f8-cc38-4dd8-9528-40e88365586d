package com.cet.engine.plugin.base.flink.transform;

import com.alibaba.fastjson.JSONObject;
import com.cet.engine.common.CheckResult;
import com.cet.engine.flink.FlinkEnvironment;
import com.cet.engine.model.FieldNode;
import com.cet.engine.model.FieldSetting;
import com.cet.engine.model.FormulaField;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.shaded.hadoop2.com.google.gson.Gson;
import org.apache.flink.shaded.hadoop2.com.google.gson.GsonBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.transformations.OneInputTransformation;
import org.apache.flink.types.Row;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {FieldSettingFunction.class})
public class FieldSettingFunctionTest {

    @InjectMocks
    private FieldSettingFunction fieldSettingFunction;

    @Test
    public void testCreateRichMapFunction() throws Exception {
        String jsonString = "{\n" +
                "        \"id\": \"T00038\",\n" +
                "        \"to\": \"T00038\",\n" +
                "        \"from\": [\n" +
                "          \"T00040\"\n" +
                "        ],\n" +
                "        \"name\": \"字段设置算子\",\n" +
                "        \"type\": \"FieldSettingFunction\",\n" +
                "        \"nodeType\": \"trans\",\n" +
                "        \"dataStream\": \"stream\",\n" +
                "        \"fieldTable\": [\n" +
                "          {\n" +
                "            \"field\": \"formula\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$devID#+8*7-9\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula1\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula2\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"$formula#+$formula1#\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"fieldsArray\": {\n" +
                "          \"devID\": \"int\",\n" +
                "          \"dataID\": \"int\",\n" +
                "          \"vCount\": \"double\",\n" +
                "          \"formula2\": \"double\",\n" +
                "          \"formula1\": \"double\",\n" +
                "          \"formula\": \"double\"\n" +
                "        }\n" +
                "      }";
        FieldSetting fieldSetting = JSONObject.parseObject(jsonString, FieldSetting.class);
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(jsonString));
        this.fieldSettingFunction.checkConfig();
        this.fieldSettingFunction.prepare(null);
        RichMapFunction<Row, Row> richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        Row row = Row.withNames();
        row.setField("devID", 1);
        row.setField("dataID", 1);
        row.setField("vCount", 1.2);
        Row newRow = richMapFunction.map(row);
        Double formula = (Double) newRow.getField("formula");
        Assert.assertEquals(2+1+8*7-9, formula, 0.01);

        richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        newRow = richMapFunction.map(row);
        Double formula1 = (Double) newRow.getField("formula1");
        Assert.assertEquals(2+50+8*7-9+1, formula1, 0.01);

        richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        newRow = richMapFunction.map(row);
        Double formula2 = (Double) newRow.getField("formula2");
        Assert.assertEquals(50+2+50+8*7-9+1, formula2, 0.01);

        row = Row.withNames();
        row.setField("devID", 1);
        row.setField("dataID", 1);
        row.setField("vCount", 1.2);
        newRow = richMapFunction.map(row);
        Set<String> names = newRow.getFieldNames(false);
        if (names != null) {
            Assert.assertTrue(names.contains("formula"));
        }
    }

    @Test
    public void testCreateRichMapFunction2() throws Exception {
        String jsonString = "{\n" +
                "        \"id\": \"T00038\",\n" +
                "        \"to\": \"T00038\",\n" +
                "        \"from\": [\n" +
                "          \"T00040\"\n" +
                "        ],\n" +
                "        \"name\": \"字段设置算子\",\n" +
                "        \"type\": \"FieldSettingFunction\",\n" +
                "        \"nodeType\": \"trans\",\n" +
                "        \"dataStream\": \"stream\",\n" +
                "        \"fieldTable\": [\n" +
                "          {\n" +
                "            \"field\": \"formula\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$devID# * 2+$devID#+8*7-9\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula1\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula2\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"$formula#+$formula1#\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"fieldsArray\": {\n" +
                "          \"devID\": \"int\",\n" +
                "          \"dataID\": \"int\",\n" +
                "          \"vCount\": \"double\",\n" +
                "          \"formula2\": \"double\",\n" +
                "          \"formula1\": \"double\",\n" +
                "          \"formula\": \"double\"\n" +
                "        }\n" +
                "      }";
        FieldSetting fieldSetting = JSONObject.parseObject(jsonString, FieldSetting.class);
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(jsonString));
        this.fieldSettingFunction.checkConfig();
        this.fieldSettingFunction.prepare(null);
        RichMapFunction<Row, Row> richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        Row row = Row.withNames();
        row.setField("devID", 1);
        row.setField("dataID", 1);
        row.setField("vCount", 1.2);
        Row newRow = richMapFunction.map(row);
        Double formula = (Double) newRow.getField("formula");
        Assert.assertEquals(2+1*2+1+8*7-9, formula, 0.01);

        fieldSetting.getFieldTable().get(1).setFormula("2+$formula#+$formula#+8*7-9+$devID#");
        richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(JSONObject.toJSONString(fieldSetting)));
        this.fieldSettingFunction.checkConfig();
        this.fieldSettingFunction.prepare(null);
        newRow = richMapFunction.map(row);
        Double formula1 = (Double) newRow.getField("formula1");
        Assert.assertEquals(2+52+52+8*7-9+1, formula1, 0.01);
    }

    @Test
    public void testCreateRichMapFunction3() throws Exception {
        String jsonString = "{\n" +
                "        \"id\": \"T00038\",\n" +
                "        \"to\": \"T00038\",\n" +
                "        \"from\": [\n" +
                "          \"T00040\"\n" +
                "        ],\n" +
                "        \"name\": \"字段设置算子\",\n" +
                "        \"type\": \"FieldSettingFunction\",\n" +
                "        \"nodeType\": \"trans\",\n" +
                "        \"dataStream\": \"stream\",\n" +
                "        \"fieldTable\": [\n" +
                "          {\n" +
                "            \"field\": \"formula\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$devID# * 2+$devID#+8*7-9\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula1\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula2\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"$formula#+$formula1#\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"fieldsArray\": {\n" +
                "          \"devID\": \"int\",\n" +
                "          \"dataID\": \"int\",\n" +
                "          \"vCount\": \"double\",\n" +
                "          \"formula2\": \"double\",\n" +
                "          \"formula1\": \"double\",\n" +
                "          \"formula\": \"double\"\n" +
                "        }\n" +
                "      }";
        FieldSetting fieldSetting = JSONObject.parseObject(jsonString, FieldSetting.class);
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(jsonString));
        this.fieldSettingFunction.checkConfig();
        this.fieldSettingFunction.prepare(null);
        RichMapFunction<Row, Row> richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        Row row = Row.withNames();
        row.setField("devID", 1);
        row.setField("dataID", 1);
        row.setField("vCount", 1.2);
        Row newRow = richMapFunction.map(row);
        Double formula = (Double) newRow.getField("formula");
        Assert.assertEquals(2+1*2+1+8*7-9, formula, 0.01);
    }

    @Test
    public void testCreateRichMapFunction_formula() throws Exception {
        String jsonString = "{\n" +
                "    \"id\": \"T6\",\n" +
                "    \"to\": \"T6\",\n" +
                "    \"from\": [\"T3\"],\n" +
                "    \"name\": \"新增计算字段\",\n" +
                "    \"type\": \"FieldSettingFunction\",\n" +
                "    \"nodeType\": \"trans\",\n" +
                "    \"dataStream\": \"stream\",\n" +
                "    \"fieldTable\": [{\n" +
                "      \"field\": \"formula_2\",\n" +
                "      \"formula\": \"$dataID#/1000.0 + $quantityobject_id#+ $stationid#* $rate#+ $val#*$status#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"合法公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"illegal_formula\",\n" +
                "      \"formula\": \"$val#/ 0.0 + 1000\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"非法公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"formula\",\n" +
                "      \"formula\": \"($val#*100+$val#)/100.0 + $dataTypeID# + $energytype#+$quantityobject_id#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"合法公式1\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"devID\",\n" +
                "      \"label\": \"设备id（devID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"设备id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataID\",\n" +
                "      \"label\": \"数据id（dataID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"数据id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"logicalID\",\n" +
                "      \"label\": \"回路号id（logicalID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"回路号id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataTypeID\",\n" +
                "      \"label\": \"数据类型id（dataTypeID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"数据类型id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"tm\",\n" +
                "      \"label\": \"时间（tm）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"时间\",\n" +
                "      \"frontedType\": \"date\"\n" +
                "    }, {\n" +
                "      \"field\": \"val\",\n" +
                "      \"label\": \"表码值（val）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"表码值\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"status\",\n" +
                "      \"label\": \"状态（status）\",\n" +
                "      \"fieldType\": \"byte\",\n" +
                "      \"description\": \"状态\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"channelid\",\n" +
                "      \"label\": \"通道id（channelid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"通道id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"stationid\",\n" +
                "      \"label\": \"厂站id（stationid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"厂站id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"quantityobject_id\",\n" +
                "      \"label\": \"物理量id（quantityobject_id）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"物理量id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"energytype\",\n" +
                "      \"label\": \"能源类型（energytype）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"能源类型\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredid\",\n" +
                "      \"label\": \"管网id（monitoredid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"管网id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredlabel\",\n" +
                "      \"label\": \"管网标签（monitoredlabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"管网标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"path\",\n" +
                "      \"label\": \"路径（path）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"路径\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"level\",\n" +
                "      \"label\": \"层级（level）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"层级\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"rate\",\n" +
                "      \"label\": \"比例（rate）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"比例\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orgid\",\n" +
                "      \"label\": \"组织层级id（orgid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"组织层级id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orglabel\",\n" +
                "      \"label\": \"组织层级标签（orglabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"组织层级标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }],\n" +
                "    \"fieldsArray\": {\n" +
                "      \"formula_2\": \"double\",\n" +
                "      \"illegal_formula\": \"double\",\n" +
                "      \"formula\": \"double\",\n" +
                "      \"tm\": \"long\",\n" +
                "      \"bbb\": \"double\",\n" +
                "      \"val\": \"double\",\n" +
                "      \"path\": \"String\",\n" +
                "      \"rate\": \"double\",\n" +
                "      \"devID\": \"int\",\n" +
                "      \"level\": \"int\",\n" +
                "      \"orgid\": \"long\",\n" +
                "      \"dataID\": \"int\",\n" +
                "      \"status\": \"byte\",\n" +
                "      \"orglabel\": \"String\",\n" +
                "      \"channelid\": \"long\",\n" +
                "      \"logicalID\": \"short\",\n" +
                "      \"stationid\": \"long\",\n" +
                "      \"dataTypeID\": \"short\",\n" +
                "      \"energytype\": \"int\",\n" +
                "      \"monitoredid\": \"long\",\n" +
                "      \"monitoredlabel\": \"String\",\n" +
                "      \"quantityobject_id\": \"long\"\n" +
                "    },\n" +
                "    \"filterTable\": [{\n" +
                "      \"field\": \"formula_2\",\n" +
                "      \"formula\": \"$dataID#/1000.0 + $quantityobject_id#+ $stationid#* $rate#+ $val#*$status#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"合法公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"illegal_formula\",\n" +
                "      \"formula\": \"$val#/ 0.0 + 1000\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"非法公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"formula\",\n" +
                "      \"formula\": \"($val#*100+$val#)/100.0 + $dataTypeID# + $energytype#+$quantityobject_id#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"合法公式1\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"devID\",\n" +
                "      \"label\": \"设备id（devID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"设备id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataID\",\n" +
                "      \"label\": \"数据id（dataID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"数据id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"logicalID\",\n" +
                "      \"label\": \"回路号id（logicalID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"回路号id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataTypeID\",\n" +
                "      \"label\": \"数据类型id（dataTypeID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"数据类型id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"tm\",\n" +
                "      \"label\": \"时间（tm）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"时间\",\n" +
                "      \"frontedType\": \"date\"\n" +
                "    }, {\n" +
                "      \"field\": \"val\",\n" +
                "      \"label\": \"表码值（val）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"表码值\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"status\",\n" +
                "      \"label\": \"状态（status）\",\n" +
                "      \"fieldType\": \"byte\",\n" +
                "      \"description\": \"状态\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"channelid\",\n" +
                "      \"label\": \"通道id（channelid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"通道id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"stationid\",\n" +
                "      \"label\": \"厂站id（stationid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"厂站id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"quantityobject_id\",\n" +
                "      \"label\": \"物理量id（quantityobject_id）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"物理量id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"energytype\",\n" +
                "      \"label\": \"能源类型（energytype）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"能源类型\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredid\",\n" +
                "      \"label\": \"管网id（monitoredid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"管网id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredlabel\",\n" +
                "      \"label\": \"管网标签（monitoredlabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"管网标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"path\",\n" +
                "      \"label\": \"路径（path）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"路径\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"level\",\n" +
                "      \"label\": \"层级（level）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"层级\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"rate\",\n" +
                "      \"label\": \"比例（rate）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"比例\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orgid\",\n" +
                "      \"label\": \"组织层级id（orgid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"组织层级id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orglabel\",\n" +
                "      \"label\": \"组织层级标签（orglabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"组织层级标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }],\n" +
                "    \"allfieldTable\": [{\n" +
                "      \"field\": \"formula_2\",\n" +
                "      \"formula\": \"$dataID#/1000.0 + $quantityobject_id#+ $stationid#* $rate#+ $val#*$status#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"合法公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"illegal_formula\",\n" +
                "      \"formula\": \"$val#/ 0.0 + 1000\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"非法公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"formula\",\n" +
                "      \"formula\": \"($val#*100+$val#)/100.0 + $dataTypeID# + $energytype#+$quantityobject_id#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"合法公式1\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"devID\",\n" +
                "      \"label\": \"设备id（devID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"设备id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataID\",\n" +
                "      \"label\": \"数据id（dataID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"数据id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"logicalID\",\n" +
                "      \"label\": \"回路号id（logicalID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"回路号id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataTypeID\",\n" +
                "      \"label\": \"数据类型id（dataTypeID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"数据类型id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"tm\",\n" +
                "      \"label\": \"时间（tm）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"时间\",\n" +
                "      \"frontedType\": \"date\"\n" +
                "    }, {\n" +
                "      \"field\": \"val\",\n" +
                "      \"label\": \"表码值（val）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"表码值\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"status\",\n" +
                "      \"label\": \"状态（status）\",\n" +
                "      \"fieldType\": \"byte\",\n" +
                "      \"description\": \"状态\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"channelid\",\n" +
                "      \"label\": \"通道id（channelid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"通道id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"stationid\",\n" +
                "      \"label\": \"厂站id（stationid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"厂站id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"quantityobject_id\",\n" +
                "      \"label\": \"物理量id（quantityobject_id）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"物理量id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"energytype\",\n" +
                "      \"label\": \"能源类型（energytype）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"能源类型\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredid\",\n" +
                "      \"label\": \"管网id（monitoredid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"管网id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredlabel\",\n" +
                "      \"label\": \"管网标签（monitoredlabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"管网标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"path\",\n" +
                "      \"label\": \"路径（path）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"路径\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"level\",\n" +
                "      \"label\": \"层级（level）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"层级\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"rate\",\n" +
                "      \"label\": \"比例（rate）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"比例\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orgid\",\n" +
                "      \"label\": \"组织层级id（orgid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"组织层级id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orglabel\",\n" +
                "      \"label\": \"组织层级标签（orglabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"组织层级标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }],\n" +
                "    \"fieldsPosition\": {\n" +
                "      \"tm\": 5,\n" +
                "      \"bbb\": 0,\n" +
                "      \"val\": 6,\n" +
                "      \"path\": 14,\n" +
                "      \"rate\": 16,\n" +
                "      \"devID\": 1,\n" +
                "      \"level\": 15,\n" +
                "      \"orgid\": 17,\n" +
                "      \"dataID\": 2,\n" +
                "      \"status\": 7,\n" +
                "      \"orglabel\": 18,\n" +
                "      \"channelid\": 8,\n" +
                "      \"logicalID\": 3,\n" +
                "      \"stationid\": 9,\n" +
                "      \"dataTypeID\": 4,\n" +
                "      \"energytype\": 11,\n" +
                "      \"monitoredid\": 12,\n" +
                "      \"monitoredlabel\": 13,\n" +
                "      \"quantityobject_id\": 10\n" +
                "    }\n" +
                "  }";
        FieldSetting fieldSetting = JSONObject.parseObject(jsonString, FieldSetting.class);
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(jsonString));
        this.fieldSettingFunction.checkConfig();
        this.fieldSettingFunction.prepare(null);
        RichMapFunction<Row, Row> richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        String dataJson = "{\n" +
                "\t\"devID\": 19,\n" +
                "\t\"val\": 2.7974365E7,\n" +
                "\t\"energytype\": 2,\n" +
                "\t\"level\": 0,\n" +
                "\t\"dataTypeID\": 1,\n" +
                "\t\"monitoredlabel\": \"itcabinet\",\n" +
                "\t\"formula_2\": 2.7978408004E7,\n" +
                "\t\"orgid\": 4,\n" +
                "\t\"illegal_formula\": null,\n" +
                "\t\"quantityobject_id\": 42,\n" +
                "\t\"path\": \"/room[4]\",\n" +
                "\t\"dataID\": 4000004,\n" +
                "\t\"logicalID\": 1,\n" +
                "\t\"rate\": 1.0,\n" +
                "\t\"formula\": 2.825415365E7,\n" +
                "\t\"tm\": 1736259900000,\n" +
                "\t\"monitoredid\": 4,\n" +
                "\t\"orglabel\": \"room\",\n" +
                "\t\"channelid\": 2,\n" +
                "\t\"status\": 1,\n" +
                "\t\"stationid\": 1\n" +
                "}";
        Row row = Row.withNames();
        Map<String, Object> data = JSONObject.parseObject(dataJson, Map.class);
        data.remove("illegal_formula");
        data.remove("formula_2");
        data.remove("formula");
        data.forEach(row::setField);
        Row newRow = richMapFunction.map(row);
        Double formula = (Double) newRow.getField("formula");
        Assert.assertEquals(2.825415365E7, formula, 0.01);
        Double formula_2 = (Double) newRow.getField("formula_2");
        Assert.assertEquals(2.7978408004E7, formula_2, 0.01);
        Double illegal_formula = (Double) newRow.getField("illegal_formula");
        Assert.assertFalse(Double.isFinite(illegal_formula));
    }

    @Test
    public void testCreateRichMapFunction_nest_formula() throws Exception {
        String jsonString = "{\n" +
                "    \"id\": \"T8\",\n" +
                "    \"to\": \"T8\",\n" +
                "    \"from\": [\"T3\"],\n" +
                "    \"name\": \"嵌套字段\",\n" +
                "    \"type\": \"FieldSettingFunction\",\n" +
                "    \"nodeType\": \"trans\",\n" +
                "    \"dataStream\": \"stream\",\n" +
                "    \"fieldTable\": [{\n" +
                "      \"field\": \"_field_1736218734956\",\n" +
                "      \"formula\": \"$nest_formula#+($formula#+ $val#*100+$logicalID#)/100.0\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"嵌套嵌套公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"nest_formula\",\n" +
                "      \"formula\": \"$formula#+$formula#*100 + $channelid#+$logicalID#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"嵌套字段\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"formula\",\n" +
                "      \"formula\": \"$devID#+($val#*100+$devID#) /100.0\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"公式字段\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"devID\",\n" +
                "      \"label\": \"设备id（devID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"设备id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataID\",\n" +
                "      \"label\": \"数据id（dataID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"数据id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"logicalID\",\n" +
                "      \"label\": \"回路号id（logicalID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"回路号id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataTypeID\",\n" +
                "      \"label\": \"数据类型id（dataTypeID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"数据类型id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"tm\",\n" +
                "      \"label\": \"时间（tm）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"时间\",\n" +
                "      \"frontedType\": \"date\"\n" +
                "    }, {\n" +
                "      \"field\": \"val\",\n" +
                "      \"label\": \"表码值（val）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"表码值\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"status\",\n" +
                "      \"label\": \"状态（status）\",\n" +
                "      \"fieldType\": \"byte\",\n" +
                "      \"description\": \"状态\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"channelid\",\n" +
                "      \"label\": \"通道id（channelid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"通道id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"stationid\",\n" +
                "      \"label\": \"厂站id（stationid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"厂站id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"quantityobject_id\",\n" +
                "      \"label\": \"物理量id（quantityobject_id）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"物理量id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"energytype\",\n" +
                "      \"label\": \"能源类型（energytype）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"能源类型\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredid\",\n" +
                "      \"label\": \"管网id（monitoredid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"管网id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredlabel\",\n" +
                "      \"label\": \"管网标签（monitoredlabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"管网标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"path\",\n" +
                "      \"label\": \"路径（path）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"路径\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"level\",\n" +
                "      \"label\": \"层级（level）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"层级\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"rate\",\n" +
                "      \"label\": \"比例（rate）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"比例\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orgid\",\n" +
                "      \"label\": \"组织层级id（orgid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"组织层级id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orglabel\",\n" +
                "      \"label\": \"组织层级标签（orglabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"组织层级标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }],\n" +
                "    \"filterTable\": [{\n" +
                "      \"field\": \"_field_1736218734956\",\n" +
                "      \"formula\": \"$nest_formula#+($formula#+ $val#*100+$logicalID#)/100.0\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"嵌套嵌套公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"nest_formula\",\n" +
                "      \"formula\": \"$formula#+$formula#*100 + $channelid#+$logicalID#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"嵌套字段\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"formula\",\n" +
                "      \"formula\": \"$devID#+($val#*100+$devID#) /100.0\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"公式字段\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"devID\",\n" +
                "      \"label\": \"设备id（devID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"设备id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataID\",\n" +
                "      \"label\": \"数据id（dataID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"数据id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"logicalID\",\n" +
                "      \"label\": \"回路号id（logicalID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"回路号id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataTypeID\",\n" +
                "      \"label\": \"数据类型id（dataTypeID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"数据类型id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"tm\",\n" +
                "      \"label\": \"时间（tm）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"时间\",\n" +
                "      \"frontedType\": \"date\"\n" +
                "    }, {\n" +
                "      \"field\": \"val\",\n" +
                "      \"label\": \"表码值（val）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"表码值\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"status\",\n" +
                "      \"label\": \"状态（status）\",\n" +
                "      \"fieldType\": \"byte\",\n" +
                "      \"description\": \"状态\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"channelid\",\n" +
                "      \"label\": \"通道id（channelid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"通道id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"stationid\",\n" +
                "      \"label\": \"厂站id（stationid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"厂站id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"quantityobject_id\",\n" +
                "      \"label\": \"物理量id（quantityobject_id）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"物理量id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"energytype\",\n" +
                "      \"label\": \"能源类型（energytype）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"能源类型\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredid\",\n" +
                "      \"label\": \"管网id（monitoredid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"管网id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredlabel\",\n" +
                "      \"label\": \"管网标签（monitoredlabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"管网标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"path\",\n" +
                "      \"label\": \"路径（path）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"路径\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"level\",\n" +
                "      \"label\": \"层级（level）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"层级\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"rate\",\n" +
                "      \"label\": \"比例（rate）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"比例\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orgid\",\n" +
                "      \"label\": \"组织层级id（orgid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"组织层级id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orglabel\",\n" +
                "      \"label\": \"组织层级标签（orglabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"组织层级标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }],\n" +
                "    \"fieldsArray\": {\n" +
                "      \"nest_formula\": \"double\",\n" +
                "      \"_field_1736218734956\": \"double\",\n" +
                "      \"formula\": \"double\",\n" +
                "      \"tm\": \"long\",\n" +
                "      \"bbb\": \"double\",\n" +
                "      \"val\": \"double\",\n" +
                "      \"path\": \"String\",\n" +
                "      \"rate\": \"double\",\n" +
                "      \"devID\": \"int\",\n" +
                "      \"level\": \"int\",\n" +
                "      \"orgid\": \"long\",\n" +
                "      \"dataID\": \"int\",\n" +
                "      \"status\": \"byte\",\n" +
                "      \"orglabel\": \"String\",\n" +
                "      \"channelid\": \"long\",\n" +
                "      \"logicalID\": \"short\",\n" +
                "      \"stationid\": \"long\",\n" +
                "      \"dataTypeID\": \"short\",\n" +
                "      \"energytype\": \"int\",\n" +
                "      \"monitoredid\": \"long\",\n" +
                "      \"monitoredlabel\": \"String\",\n" +
                "      \"quantityobject_id\": \"long\"\n" +
                "    },\n" +
                "    \"allfieldTable\": [{\n" +
                "      \"field\": \"_field_1736218734956\",\n" +
                "      \"formula\": \"$nest_formula#+($formula#+ $val#*100+$logicalID#)/100.0\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"嵌套嵌套公式\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"nest_formula\",\n" +
                "      \"formula\": \"$formula#+$formula#*100 + $channelid#+$logicalID#\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"嵌套字段\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"formula\",\n" +
                "      \"formula\": \"$devID#+($val#*100+$devID#) /100.0\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"公式字段\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"devID\",\n" +
                "      \"label\": \"设备id（devID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"设备id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataID\",\n" +
                "      \"label\": \"数据id（dataID）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"数据id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"logicalID\",\n" +
                "      \"label\": \"回路号id（logicalID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"回路号id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"dataTypeID\",\n" +
                "      \"label\": \"数据类型id（dataTypeID）\",\n" +
                "      \"fieldType\": \"short\",\n" +
                "      \"description\": \"数据类型id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"tm\",\n" +
                "      \"label\": \"时间（tm）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"时间\",\n" +
                "      \"frontedType\": \"date\"\n" +
                "    }, {\n" +
                "      \"field\": \"val\",\n" +
                "      \"label\": \"表码值（val）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"表码值\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"status\",\n" +
                "      \"label\": \"状态（status）\",\n" +
                "      \"fieldType\": \"byte\",\n" +
                "      \"description\": \"状态\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"channelid\",\n" +
                "      \"label\": \"通道id（channelid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"通道id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"stationid\",\n" +
                "      \"label\": \"厂站id（stationid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"厂站id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"quantityobject_id\",\n" +
                "      \"label\": \"物理量id（quantityobject_id）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"物理量id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"energytype\",\n" +
                "      \"label\": \"能源类型（energytype）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"能源类型\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredid\",\n" +
                "      \"label\": \"管网id（monitoredid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"管网id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"monitoredlabel\",\n" +
                "      \"label\": \"管网标签（monitoredlabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"管网标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"path\",\n" +
                "      \"label\": \"路径（path）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"路径\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }, {\n" +
                "      \"field\": \"level\",\n" +
                "      \"label\": \"层级（level）\",\n" +
                "      \"fieldType\": \"int\",\n" +
                "      \"description\": \"层级\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"rate\",\n" +
                "      \"label\": \"比例（rate）\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"description\": \"比例\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orgid\",\n" +
                "      \"label\": \"组织层级id（orgid）\",\n" +
                "      \"fieldType\": \"long\",\n" +
                "      \"description\": \"组织层级id\",\n" +
                "      \"frontedType\": \"number\"\n" +
                "    }, {\n" +
                "      \"field\": \"orglabel\",\n" +
                "      \"label\": \"组织层级标签（orglabel）\",\n" +
                "      \"fieldType\": \"String\",\n" +
                "      \"description\": \"组织层级标签\",\n" +
                "      \"frontedType\": \"string\"\n" +
                "    }]}";
        FieldSetting fieldSetting = JSONObject.parseObject(jsonString, FieldSetting.class);
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(jsonString));
        this.fieldSettingFunction.checkConfig();
        this.fieldSettingFunction.prepare(null);
        RichMapFunction<Row, Row> richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        String dataJson = "{\n" +
                "\t\"devID\": 22,\n" +
                "\t\"val\": 1.637692E7,\n" +
                "\t\"nest_formula\": 1.65407116722E9,\n" +
                "\t\"energytype\": 2,\n" +
                "\t\"level\": 0,\n" +
                "\t\"dataTypeID\": 1,\n" +
                "\t\"monitoredlabel\": \"itcabinet\",\n" +
                "\t\"orgid\": 4,\n" +
                "\t\"quantityobject_id\": 54,\n" +
                "\t\"path\": \"/room[4]\",\n" +
                "\t\"_field_1736218734956\": 1.6706118566522E9,\n" +
                "\t\"dataID\": 4000004,\n" +
                "\t\"logicalID\": 1,\n" +
                "\t\"rate\": 1.0,\n" +
                "\t\"formula\": 1.637694222E7,\n" +
                "\t\"tm\": 1735008300000,\n" +
                "\t\"monitoredid\": 7,\n" +
                "\t\"orglabel\": \"room\",\n" +
                "\t\"channelid\": 2,\n" +
                "\t\"status\": 1,\n" +
                "\t\"stationid\": 1\n" +
                "}";
        Row row = Row.withNames();
        Map<String, Object> data = JSONObject.parseObject(dataJson, Map.class);
        data.remove("illegal_formula");
        data.remove("formula_2");
        data.remove("formula");
        data.forEach(row::setField);
        Row newRow = richMapFunction.map(row);
        Double formula = (Double) newRow.getField("formula");
        Assert.assertEquals(1.637694222E7, formula, 0.01);
        Double _field_1736218734956 = (Double) newRow.getField("_field_1736218734956");
        Assert.assertEquals(1.6706118566522E9, _field_1736218734956, 0.01);
        Double nest_formula = (Double) newRow.getField("nest_formula");
        Assert.assertEquals(1.65407116722E9, nest_formula, 0.01);
    }

    @Test
    public void testCreateRichMapFunction_lack() throws Exception {
        String jsonString = "{\n" +
                "        \"id\": \"T00038\",\n" +
                "        \"to\": \"T00038\",\n" +
                "        \"from\": [\n" +
                "          \"T00040\"\n" +
                "        ],\n" +
                "        \"name\": \"字段设置算子\",\n" +
                "        \"type\": \"FieldSettingFunction\",\n" +
                "        \"nodeType\": \"trans\",\n" +
                "        \"dataStream\": \"stream\",\n" +
                "        \"fieldTable\": [\n" +
                "          {\n" +
                "            \"field\": \"formula\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$devID#+8*7-9+$devID#/0\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula1\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"f1\",\n" +
                "            \"fieldType\": \"string\",\n" +
                "            \"frontedType\": \"string\",\n" +
                "            \"formula\": \"\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"f2\",\n" +
                "            \"fieldType\": \"string\",\n" +
                "            \"frontedType\": \"string\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula2\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"$formula#+$formula1#\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"fieldsArray\": {\n" +
                "          \"devID\": \"int\",\n" +
                "          \"dataID\": \"int\",\n" +
                "          \"vCount\": \"double\",\n" +
                "          \"formula2\": \"double\",\n" +
                "          \"formula1\": \"double\",\n" +
                "          \"formula\": \"double\"\n" +
                "        }\n" +
                "      }";
        FieldSetting fieldSetting = JSONObject.parseObject(jsonString, FieldSetting.class);
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(jsonString));
        this.fieldSettingFunction.checkConfig();
        this.fieldSettingFunction.prepare(null);
        RichMapFunction<Row, Row> richMapFunction = this.fieldSettingFunction.createRichMapFunction(fieldSetting);
        Row row = Row.withNames();
        row.setField("devID", 1);
        row.setField("dataID", 1);
        row.setField("vCount", 1.2);
        Row newRow = richMapFunction.map(row);
        Double formula = (Double) newRow.getField("formula");
        Assert.assertNull(formula);

        Double formula1 = (Double) newRow.getField("formula1");
        Assert.assertNull(formula1);

        Double formula2 = (Double) newRow.getField("formula2");
        Assert.assertNull(formula2);
    }

    @Test
    public void testEvaluateNumericalExpression() {
        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put("a", "1");
        String expression = "a + 10 + 1";
        Object value = this.fieldSettingFunction.evaluateNumericalExpression(variableMap, expression);
        Assert.assertEquals("1101", value);

        variableMap = new HashMap<>();
        variableMap.put("a", 1.0);
        expression = "a + 10 + 1";
        value = this.fieldSettingFunction.evaluateNumericalExpression(variableMap, expression);
        Assert.assertEquals(12.0, (Double) value, 0.01);

        variableMap = new HashMap<>();
        variableMap.put("a", 1);
        expression = "a + 10 + 1";
        value = this.fieldSettingFunction.evaluateNumericalExpression(variableMap, expression);
        Assert.assertNotNull(value);

        variableMap = new HashMap<>();
        variableMap.put("a", 1);
        expression = "a + 10 + 1";
        value = this.fieldSettingFunction.evaluateNumericalExpression(variableMap, expression);
        Assert.assertNotNull(value);
    }

    @Test
    public void testEvaluateNumericalExpression_zero() {
        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put("a", 114455);
        variableMap.put("b", 10);
        variableMap.put("c", 10);
        String expression = "a /(b-c)";
        for (int i = 0; i < 1; i++) {
            Object value = this.fieldSettingFunction.evaluateNumericalExpression(variableMap, expression);
            Assert.assertNull(value);
        }
    }

    @Test
    public void testCheckExpression() {
        CheckResult checkResult = this.fieldSettingFunction.checkExpression("1++1");
        Assert.assertFalse(checkResult.isSuccess());

        checkResult = this.fieldSettingFunction.checkExpression("1+");
        Assert.assertFalse(checkResult.isSuccess());

        checkResult = this.fieldSettingFunction.checkExpression("A+B");
        Assert.assertTrue(checkResult.isSuccess());

        checkResult = this.fieldSettingFunction.checkExpression("A  + b");
        Assert.assertTrue(checkResult.isSuccess());

        checkResult = this.fieldSettingFunction.checkExpression("nest_formula +（formula1 + val*100+logicalID）/100.0");
        Assert.assertFalse(checkResult.isSuccess());
    }

    @Test
    public void testHasAdjacentVariables() {
        boolean has = this.fieldSettingFunction.hasAdjacentVariables("$hello#$f#");
        Assert.assertTrue(has);

        has = this.fieldSettingFunction.hasAdjacentVariables("$hello# $f#");
        Assert.assertFalse(has);
    }

    @Test
    public void testCheckCircularRef() {
        FieldNode fieldNode1 = new FieldNode();
        fieldNode1.setFormulaField(new FormulaField("a", "1", "1", "$b#"));
        fieldNode1.setRefFields(Lists.newArrayList("a"));
        fieldNode1.setExpression("a");

        FieldNode fieldNode2 = new FieldNode();
        fieldNode2.setFormulaField(new FormulaField("b", "1", "1", "$c#+1"));
        fieldNode2.setRefFields(Lists.newArrayList("f1"));
        fieldNode2.setExpression("f1+1");
        fieldNode1.setChildren(Lists.newArrayList(fieldNode2));

        FieldNode fieldNode3 = new FieldNode();
        fieldNode3.setFormulaField(new FormulaField("c", "1", "1", "$a#+1"));
        fieldNode3.setRefFields(Lists.newArrayList("a"));
        fieldNode3.setExpression("a+1");
        fieldNode3.setChildren(Lists.newArrayList(fieldNode1));
        fieldNode2.setChildren(Lists.newArrayList(fieldNode3));

        boolean circularRef = this.fieldSettingFunction.checkCircularRef(fieldNode1, null);
        Assert.assertTrue(circularRef);

        fieldNode3.getFormulaField().setFormula("$f2#+1");
        fieldNode3.setRefFields(Lists.newArrayList("f2"));
        fieldNode3.setExpression("f2+1");
        fieldNode3.setChildren(Lists.newArrayList());
        circularRef = this.fieldSettingFunction.checkCircularRef(fieldNode1, null);
        Assert.assertFalse(circularRef);

        fieldNode3.getFormulaField().setFormula("$b#+1");
        fieldNode3.setRefFields(Lists.newArrayList("b"));
        fieldNode3.setExpression("b+1");
        fieldNode3.setChildren(Lists.newArrayList(fieldNode2));
        circularRef = this.fieldSettingFunction.checkCircularRef(fieldNode1, null);
        Assert.assertTrue(circularRef);

        FieldNode fieldNode4 = new FieldNode();
        fieldNode4.setFormulaField(new FormulaField("d", "1", "1", "$d#+1"));
        fieldNode4.setRefFields(Lists.newArrayList("d"));
        fieldNode4.setExpression("d+1");
        fieldNode4.setChildren(Lists.newArrayList(fieldNode4));
        circularRef = this.fieldSettingFunction.checkCircularRef(fieldNode4, null);
        Assert.assertTrue(circularRef);
    }

    @Test
    public void testCheckConfig() {
        String jsonString = "{\n" +
                "        \"id\": \"T00038\",\n" +
                "        \"to\": \"T00038\",\n" +
                "        \"from\": [\n" +
                "          \"T00040\"\n" +
                "        ],\n" +
                "        \"name\": \"字段设置算子\",\n" +
                "        \"type\": \"FieldSettingFunction\",\n" +
                "        \"nodeType\": \"trans\",\n" +
                "        \"dataStream\": \"stream\",\n" +
                "        \"fieldTable\": [\n" +
                "          {\n" +
                "            \"field\": \"formula\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$devID#+8*7-9\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula1\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula2\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"$formula#+$formula1#\"\n" +
                "          }\n" +
                "        ]}\n" +
                "      }";
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(jsonString));
        this.fieldSettingFunction.checkConfig();
        Map<String, FieldNode> fieldNodeMap = this.fieldSettingFunction.getFieldNodeMap();
        Assert.assertTrue(fieldNodeMap.containsKey("formula1"));
        String[] fields = {"formula", "devID"};
        Assert.assertArrayEquals(fields, fieldNodeMap.get("formula1").getRefFields().toArray());
        Assert.assertTrue(fieldNodeMap.containsKey("formula1"));
        Assert.assertEquals(fieldNodeMap.get("formula1").getChildren().get(0), fieldNodeMap.get("formula"));
        Assert.assertEquals("2+formula+8*7-9+devID", fieldNodeMap.get("formula1").getExpression());

        Assert.assertTrue(fieldNodeMap.containsKey("formula"));
        String[] fields1 = {"devID"};
        Assert.assertArrayEquals(fields1, fieldNodeMap.get("formula").getRefFields().toArray());
        Assert.assertEquals("2+devID+8*7-9", fieldNodeMap.get("formula").getExpression());

        Assert.assertTrue(fieldNodeMap.containsKey("formula2"));
        String[] fields2 = {"formula", "formula1"};
        Assert.assertArrayEquals(fields2, fieldNodeMap.get("formula2").getRefFields().toArray());
        Assert.assertEquals("formula+formula1", fieldNodeMap.get("formula2").getExpression());
        Assert.assertEquals(fieldNodeMap.get("formula2").getChildren().get(0), fieldNodeMap.get("formula"));
        Assert.assertEquals(fieldNodeMap.get("formula2").getChildren().get(1), fieldNodeMap.get("formula1"));
    }

    @Test
    public void testProcessStream() {
        String jsonString = "{\n" +
                "        \"id\": \"T00038\",\n" +
                "        \"to\": \"T00038\",\n" +
                "        \"from\": [\n" +
                "          \"T00040\"\n" +
                "        ],\n" +
                "        \"name\": \"字段设置算子\",\n" +
                "        \"type\": \"FieldSettingFunction\",\n" +
                "        \"nodeType\": \"trans\",\n" +
                "        \"dataStream\": \"stream\",\n" +
                "        \"fieldTable\": [\n" +
                "          {\n" +
                "            \"field\": \"formula\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$devID#+8*7-9\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula1\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula2\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"$formula#+$formula1#\"\n" +
                "          }\n" +
                "        ]}\n" +
                "      }";
        this.fieldSettingFunction.setConfig(JSONObject.parseObject(jsonString));
        this.fieldSettingFunction.checkConfig();
        FlinkEnvironment env = Mockito.mock(FlinkEnvironment.class);
        Map<String, DataStream<Row>> dataStreams = Maps.newHashMap();
        DataStreamSource dataStream = Mockito.mock(DataStreamSource.class);
        OneInputTransformation oneInputTransformation = Mockito.mock(OneInputTransformation.class);
        dataStreams.put("1", dataStream);
        SingleOutputStreamOperator singleOutputStreamOperator = Mockito.mock(SingleOutputStreamOperator.class);
        Mockito.when(dataStream.map(any(MapFunction.class))).thenReturn(singleOutputStreamOperator);
        Mockito.when(singleOutputStreamOperator.map(any(MapFunction.class))).thenReturn(singleOutputStreamOperator);
        Mockito.when(singleOutputStreamOperator.uid(any())).thenReturn(singleOutputStreamOperator);
        Mockito.when(singleOutputStreamOperator.name(any())).thenReturn(singleOutputStreamOperator);
        Mockito.when(singleOutputStreamOperator.returns(any(TypeInformation.class))).thenReturn(singleOutputStreamOperator);
        dataStreams.put("bbbbT00040", dataStream);
        String fieldTypeJson = "{\n" +
                "  \"devID\" : \"int\",\n" +
                "  \"dataID\" : \"int\",\n" +
                "  \"vCount\" : \"double\",\n" +
                "  \"formula\" : \"double\"\n" +
                "}";
        JSONObject schemaInfo = JSONObject.parseObject(fieldTypeJson);
        ReflectionTestUtils.setField(this.fieldSettingFunction, "schemaInfo", schemaInfo);
        ReflectionTestUtils.setField(singleOutputStreamOperator, "transformation", oneInputTransformation);
        DataStream<Row> processStream = this.fieldSettingFunction.processStream(env, dataStreams);
        Assert.assertNotNull(processStream);
    }

    @Test
    public void testParseConfig() throws InvocationTargetException, IllegalAccessException {
        String jsonString = "{\n" +
                "        \"id\": \"T00038\",\n" +
                "        \"to\": \"T00038\",\n" +
                "        \"from\": [\n" +
                "          \"T00040\"\n" +
                "        ],\n" +
                "        \"name\": \"字段设置算子\",\n" +
                "        \"type\": \"FieldSettingFunction\",\n" +
                "        \"nodeType\": \"trans\",\n" +
                "        \"dataStream\": \"stream\",\n" +
                "        \"fieldTable\": [\n" +
                "          {\n" +
                "            \"field\": \"formula\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$devID#+8*7-9\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula1\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"field\": \"formula2\",\n" +
                "            \"fieldType\": \"double\",\n" +
                "            \"frontedType\": \"number\",\n" +
                "            \"formula\": \"$formula#+$formula1#\"\n" +
                "          }\n" +
                "        ]}\n" +
                "      }";
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        Method method = PowerMockito.method(FieldSettingFunction.class, "parseConfig");
        CheckResult checkResult = (CheckResult) method.invoke(this.fieldSettingFunction, jsonObject);
        Assert.assertTrue(checkResult.isSuccess());

        Map<String, FieldNode> fieldNodeMap = this.fieldSettingFunction.getFieldNodeMap();
        FieldSetting fieldSetting = (FieldSetting)ReflectionTestUtils.getField(this.fieldSettingFunction, "fieldSetting");
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        String fieldSettingJson = "{\n" +
                "  \"id\": \"T00038\",\n" +
                "  \"name\": \"字段设置算子\",\n" +
                "  \"from\": [\n" +
                "    \"T00040\"\n" +
                "  ],\n" +
                "  \"fieldTable\": [\n" +
                "    {\n" +
                "      \"field\": \"formula\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"frontedType\": \"number\",\n" +
                "      \"formula\": \"2+$devID#+8*7-9\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"field\": \"formula1\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"frontedType\": \"number\",\n" +
                "      \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"field\": \"formula2\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"frontedType\": \"number\",\n" +
                "      \"formula\": \"$formula#+$formula1#\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        Assert.assertEquals(fieldSettingJson, gson.toJson(fieldSetting));

        fieldSettingJson = "{\n" +
                "  \"formula1\": {\n" +
                "    \"formulaField\": {\n" +
                "      \"field\": \"formula1\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"frontedType\": \"number\",\n" +
                "      \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "    },\n" +
                "    \"refFields\": [\n" +
                "      \"formula\",\n" +
                "      \"devID\"\n" +
                "    ],\n" +
                "    \"expression\": \"2+formula+8*7-9+devID\",\n" +
                "    \"children\": [\n" +
                "      {\n" +
                "        \"formulaField\": {\n" +
                "          \"field\": \"formula\",\n" +
                "          \"fieldType\": \"double\",\n" +
                "          \"frontedType\": \"number\",\n" +
                "          \"formula\": \"2+$devID#+8*7-9\"\n" +
                "        },\n" +
                "        \"refFields\": [\n" +
                "          \"devID\"\n" +
                "        ],\n" +
                "        \"expression\": \"2+devID+8*7-9\",\n" +
                "        \"children\": []\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"formula2\": {\n" +
                "    \"formulaField\": {\n" +
                "      \"field\": \"formula2\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"frontedType\": \"number\",\n" +
                "      \"formula\": \"$formula#+$formula1#\"\n" +
                "    },\n" +
                "    \"refFields\": [\n" +
                "      \"formula\",\n" +
                "      \"formula1\"\n" +
                "    ],\n" +
                "    \"expression\": \"formula+formula1\",\n" +
                "    \"children\": [\n" +
                "      {\n" +
                "        \"formulaField\": {\n" +
                "          \"field\": \"formula\",\n" +
                "          \"fieldType\": \"double\",\n" +
                "          \"frontedType\": \"number\",\n" +
                "          \"formula\": \"2+$devID#+8*7-9\"\n" +
                "        },\n" +
                "        \"refFields\": [\n" +
                "          \"devID\"\n" +
                "        ],\n" +
                "        \"expression\": \"2+devID+8*7-9\",\n" +
                "        \"children\": []\n" +
                "      },\n" +
                "      {\n" +
                "        \"formulaField\": {\n" +
                "          \"field\": \"formula1\",\n" +
                "          \"fieldType\": \"double\",\n" +
                "          \"frontedType\": \"number\",\n" +
                "          \"formula\": \"2+$formula#+8*7-9+$devID#\"\n" +
                "        },\n" +
                "        \"refFields\": [\n" +
                "          \"formula\",\n" +
                "          \"devID\"\n" +
                "        ],\n" +
                "        \"expression\": \"2+formula+8*7-9+devID\",\n" +
                "        \"children\": [\n" +
                "          {\n" +
                "            \"formulaField\": {\n" +
                "              \"field\": \"formula\",\n" +
                "              \"fieldType\": \"double\",\n" +
                "              \"frontedType\": \"number\",\n" +
                "              \"formula\": \"2+$devID#+8*7-9\"\n" +
                "            },\n" +
                "            \"refFields\": [\n" +
                "              \"devID\"\n" +
                "            ],\n" +
                "            \"expression\": \"2+devID+8*7-9\",\n" +
                "            \"children\": []\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"formula\": {\n" +
                "    \"formulaField\": {\n" +
                "      \"field\": \"formula\",\n" +
                "      \"fieldType\": \"double\",\n" +
                "      \"frontedType\": \"number\",\n" +
                "      \"formula\": \"2+$devID#+8*7-9\"\n" +
                "    },\n" +
                "    \"refFields\": [\n" +
                "      \"devID\"\n" +
                "    ],\n" +
                "    \"expression\": \"2+devID+8*7-9\",\n" +
                "    \"children\": []\n" +
                "  }\n" +
                "}";
        Assert.assertEquals(fieldSettingJson, gson.toJson(fieldNodeMap));
    }
}
