package com.cet.engine.plugin.base.flink.transform;

import com.cet.engine.model.AggregateTypeEnum;
import com.cet.engine.model.AggregationCycleEnum;
import com.cet.engine.model.KeyedRowProcessConfig;
import com.cet.engine.model.PeriodField;
import com.cet.engine.test.*;
import com.cet.engine.utils.DateUtil;
import com.cet.engine.utils.UnnaturalSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.flink.streaming.api.TimerService;
import org.apache.flink.types.Row;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class KeyedRowProcessFunctionTest_year {
    @Test
    public void getDataIndex_test(){
        AggregationCycleEnum aggregationCycle = AggregationCycleEnum.YEAR;
        UnnaturalSet unnaturalSet = new UnnaturalSet();
        long windowStartTime_2024_0101 = 1704038400000L;

        // 5分钟间隔
        int interval = 5;
        long logTime_2024_1231_2355 = 1735660500000L;
        Assertions.assertEquals(366 * 12 * 24 - 1  , KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2024_1231_2355));
        long logTime_2025_0101 = 1735660800000L;
        Assertions.assertEquals(366 * 12 * 24, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0101));
        long logTime_2025_0201 = 1738339200000L;
        Assertions.assertEquals(397 * 12 * 24, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0201));
        long logTime_2025_0301 = 1740758400000L;
        Assertions.assertEquals(425 * 12 * 24, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0301));

        // 1天间隔
        interval = 1440;
        Assertions.assertEquals(365 , KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2024_1231_2355));
        Assertions.assertEquals(366, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0101));
        Assertions.assertEquals(397, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0201));
        Assertions.assertEquals(425, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0301));
        long logTime_2025_0301_235959 = 1740844799000L;
        Assertions.assertEquals(425, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0301_235959));

        // 月间隔
        interval = 1440*28;
        Assertions.assertEquals(11 , KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2024_1231_2355));
        Assertions.assertEquals(12, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0101));
        Assertions.assertEquals(13, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0201));
        Assertions.assertEquals(14, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0301));
        Assertions.assertEquals(14, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0301_235959));
        long logTime_2028_0101 = 1830268800000L;
        Assertions.assertEquals(48, KeyedRowProcessFunction.getDataIndex(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2028_0101));
    }

    @Test
    public void getAddCount_year(){
        AggregationCycleEnum aggregationCycle = AggregationCycleEnum.YEAR;
        UnnaturalSet unnaturalSet = new UnnaturalSet();
        long windowStartTime_2024_0101 = 1704038400000L;

        // 5分钟间隔
        int interval = 5;
        Assertions.assertEquals(366*24*12,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, windowStartTime_2024_0101, 0));
        long logTime_2024_1231 = 1735660500000L;
        Assertions.assertEquals(366*24*12,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2024_1231, 0));
        long logTime_2025_0101 = 1735660800000L;
        Assertions.assertEquals(731 * 12 * 24,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0101, 0));
        long logTime_2025_0201 = 1738339200000L;
        Assertions.assertEquals(731* 12 * 24,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0201, 0));
        long logTime_2025_0301 = 1740758400000L;
        Assertions.assertEquals(731 * 12 * 24,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0301, 0));
        long logTime_2025_0331_2355 = 1743436500000L;
        Assertions.assertEquals(731 * 12 * 24,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0331_2355, 0));
        long logTime_2028_0101 = 1830268800000L;
        Assertions.assertEquals(1827 * 12 * 24,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2028_0101, 0));

        // 1天间隔
        interval = 1440;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, windowStartTime_2024_0101, 366));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2024_1231, 366));
        Assertions.assertEquals(365,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0101, 366));
        Assertions.assertEquals(365,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0201, 366));
        Assertions.assertEquals(365,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0301, 366));
        Assertions.assertEquals(365,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0331_2355, 366));
        Assertions.assertEquals(1461,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2028_0101, 366));

        // 月间隔
        interval = 1440 * 28;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, windowStartTime_2024_0101, 12));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2024_1231, 12));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0101, 12));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0201, 12));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0301, 12));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2025_0331_2355, 12));
        Assertions.assertEquals(48,KeyedRowProcessFunction.getAddCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, logTime_2028_0101, 12));
    }

    @Test
    public void getComputeCount_year(){
        AggregationCycleEnum aggregationCycle = AggregationCycleEnum.YEAR;
        UnnaturalSet unnaturalSet = new UnnaturalSet();
        long windowStartTime_2024_0101 = 1704038400000L;
        // 5分钟间隔
        int interval = 5;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 0));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 1));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 31*24*12 -1));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 31*24*12));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 366*24*12-1));
        Assertions.assertEquals(366*24*12,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 366*24*12));
        Assertions.assertEquals(366*24*12,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 731*24*12-1));
        Assertions.assertEquals(731*24*12,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 731*24*12));
        Assertions.assertEquals(731*24*12,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 1096*24*12-1));
        Assertions.assertEquals(1096*24*12,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 1096*24*12));
        // 1天间隔
        interval = 1440;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 0));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 1));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 30));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 31));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 365));
        Assertions.assertEquals(366,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 366));
        Assertions.assertEquals(366,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 730));
        Assertions.assertEquals(731,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 731));
        Assertions.assertEquals(731,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 1095));
        Assertions.assertEquals(1096,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 1096));
        // 1月间隔
        interval = 1440 * 28;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 0));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 1));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 11));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 12));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 23));
        Assertions.assertEquals(24,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 24));
        Assertions.assertEquals(24,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 35));
        Assertions.assertEquals(36,KeyedRowProcessFunction.getComputeCount(aggregationCycle, unnaturalSet, windowStartTime_2024_0101, interval, 36));
    }

    @Test
    public void getStartTimeAfterClean_year(){
        AggregationCycleEnum aggregationCycle = AggregationCycleEnum.YEAR;
        int interval = 5;
        long windowStartTime_2024_0101 = 1704038400000L;
        long logTime_2025_0101 = 1735660800000L;
        Assertions.assertEquals(logTime_2025_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,366*12*24));
        long logTime_2026_0101 = 1767196800000L;
        Assertions.assertEquals(logTime_2026_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,731*12*24));
        long logTime_2027_0101 = 1798732800000L;
        Assertions.assertEquals(logTime_2027_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,1096*12*24));

        interval = 1440;
        Assertions.assertEquals(logTime_2025_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,366));
        Assertions.assertEquals(logTime_2026_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,731));
        Assertions.assertEquals(logTime_2027_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,1096));

        interval = 1440 * 28;
        Assertions.assertEquals(windowStartTime_2024_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,0));
        Assertions.assertEquals(logTime_2025_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,12));
        Assertions.assertEquals(logTime_2026_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,24));
        Assertions.assertEquals(logTime_2027_0101,KeyedRowProcessFunction.getStartTimeAfterClean(aggregationCycle,interval,windowStartTime_2024_0101,36));
    }

    @Test
    public void getDelayCount_year(){
        AggregationCycleEnum aggregationCycle = AggregationCycleEnum.YEAR;
        UnnaturalSet unnaturalSet = new UnnaturalSet();
        long windowStartTime_2024_0101 = 1704038400000L;
        // 5分钟间隔
        int interval = 5;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, windowStartTime_2024_0101));
        long logTime_2024_1231 = 1735660500000L;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2024_1231));
        long logTime_2025_0101 = 1735660800000L;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0101));
        long logTime_2025_0102 = 1735747200000L;
        Assertions.assertEquals(366*24*12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0102));
        long logTime_2025_0103 = 1735833600000L;
        Assertions.assertEquals(366*24*12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0103));
        long logTime_2025_1231 = 1767110400000L;
        Assertions.assertEquals(366*24*12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_1231));
        long logTime_2026_0101 = 1767196800000L;
        Assertions.assertEquals(366*24*12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2026_0101));
        long logTime_2026_0102 = 1767283200000L;
        Assertions.assertEquals(731*24*12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2026_0102));

        // 天间隔
        interval = 1440;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, windowStartTime_2024_0101));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2024_1231));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0101));
        Assertions.assertEquals(366,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0102));
        Assertions.assertEquals(366,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0103));
        Assertions.assertEquals(366,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_1231));
        Assertions.assertEquals(366,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2026_0101));
        Assertions.assertEquals(731,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2026_0102));

        // 月间隔
        interval = 1440*28;
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, windowStartTime_2024_0101));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2024_1231));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0101));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0102));
        Assertions.assertEquals(0,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0103));
        long logTime_2025_0201 = 1738339200000L;
        Assertions.assertEquals(12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_0201));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2025_1231));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2026_0101));
        Assertions.assertEquals(12,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2026_0102));
        long logTime_2026_0201 = 1769875200000L;
        Assertions.assertEquals(24,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2026_0201));
        long logTime_2026_0301 = 1772294400000L;
        Assertions.assertEquals(24,KeyedRowProcessFunction.getDelayCount(aggregationCycle,unnaturalSet, interval, 48, windowStartTime_2024_0101, logTime_2026_0301));

    }

    @Test
    public void testProcessElement_sum_sum() throws Exception {
        KeyedRowProcessFunction keyedRowProcessFunction = new KeyedRowProcessFunction();
        KeyedRowProcessConfig config = new KeyedRowProcessConfig();
        config.setAggregationCycle(AggregationCycleEnum.YEAR);
        config.setKeyFields(Lists.newArrayList("roomID", "dataID"));
        config.setFuncField("val");
        config.setGroupBy(Lists.newArrayList("roomID", "dataID", "tm"));
        config.setLatenessHour(24);
        config.setSubFields(Lists.newArrayList("devID"));
        config.setAggregators(Lists.newArrayList(new GeneralPeriodAggregator("sum", true,
                AggregateTypeEnum.ARITHMETIC_SUM, AggregateTypeEnum.ARITHMETIC_SUM)));
        config.setPeriodField(new PeriodField("tm", "tm", "tm", AggregationCycleEnum.YEAR.getCode()));
        Map<String, String> dataTypeMap = Maps.newHashMap();
        dataTypeMap.put("roomID", "long");
        dataTypeMap.put("devID", "long");
        dataTypeMap.put("dataID", "int");
        dataTypeMap.put("logicalID", "int");
        dataTypeMap.put("dataTypeID", "int");
        dataTypeMap.put("tm", "date");
        dataTypeMap.put("val", "double");
        dataTypeMap.put("status", "byte");
        List<Row> dataSet = UnitTestUtils.readDataRowsFromCsvFile("data/room_datalog_year.csv", dataTypeMap);
        ReflectionTestUtils.setField(keyedRowProcessFunction, "config", config);
        KeyedRowProcessProperties keyedRowProcessProperties = UnitTestUtils.initializeKeyedRowProcessFunction(keyedRowProcessFunction);
        TimerService timerService = new TestTimerService();
        Row keyRow = Row.withNames();
        keyRow.setField("roomID", 1);
        keyRow.setField("dataID", 4000012);
        KeyedProcessFunctionContext context = new KeyedProcessFunctionContext(keyedRowProcessFunction, timerService, 0L, keyRow);
        TestCollector<Row> collector = new TestCollector<>();
        for (int index = 0; index < dataSet.size(); index++) {
            System.out.println("定时记录时间：" + DateUtil.longToString((Long) dataSet.get(index).getField("tm"), "yyyy-MM-dd HH:mm:ss"));
            keyedRowProcessFunction.processElement(dataSet.get(index), context, collector);
        }
        TestOnTimerContext testOnTimerContext = new TestOnTimerContext(keyedRowProcessFunction, timerService, System.currentTimeMillis(), keyRow);
        keyedRowProcessFunction.onTimer(System.currentTimeMillis(), testOnTimerContext, collector);
        Long minTime = keyedRowProcessProperties.getMinTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2023-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), minTime.longValue());
        Long maxTime = keyedRowProcessProperties.getMaxTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2025-02-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), maxTime.longValue());
//        Long startTime = keyedRowProcessProperties.getStartTimeValueState().value();
//        Assert.assertEquals(DateUtil.stringToLong("2025-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), startTime.longValue());
        Integer interval = keyedRowProcessProperties.getIntervalValueState().value();
        Assert.assertEquals(40320, interval.intValue());
        Map<Row, Integer> expectedSubKeyIndex = Maps.newHashMap();
        Row device1 = Row.withNames();
        device1.setField("devID", 1L);
        expectedSubKeyIndex.put(device1, 0);
        Row device2 = Row.withNames();
        device2.setField("devID", 2L);
        expectedSubKeyIndex.put(device2, 1);
        Row device3 = Row.withNames();
        device3.setField("devID", 3L);
        expectedSubKeyIndex.put(device3, 2);
        Row device4 = Row.withNames();
        device4.setField("devID", 4L);
        expectedSubKeyIndex.put(device4, 3);
        Row device5 = Row.withNames();
        device5.setField("devID", 5L);
        expectedSubKeyIndex.put(device5, 4);
        Assert.assertEquals(expectedSubKeyIndex, keyedRowProcessProperties.getSubKeyIndexMapState().getMap());

        List<Row> records = collector.getRecords();
        equalDayRow(1, 4000012, DateUtil.stringToLong("2023-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                new Double("101.0"), records.get(0));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2024-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                new Double("852.6"), records.get(1));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2025-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                new Double("131.6"), records.get(2));
        Assert.assertEquals(3, records.size());
    }

    private void equalDayRow(Integer devId, Integer dataId, Long tm, Double value, Row row) {
        Assert.assertEquals(devId, row.getField("roomID"));
        Assert.assertEquals(dataId, row.getField("dataID"));
        Assert.assertEquals(tm, row.getField("tm"));
        Assert.assertEquals(value, row.getFieldAs("sum"), 0.000001);
    }

    @Test
    public void testProcessElement_sum_min() throws Exception {
        KeyedRowProcessFunction keyedRowProcessFunction = new KeyedRowProcessFunction();
        KeyedRowProcessConfig config = new KeyedRowProcessConfig();
        config.setAggregationCycle(AggregationCycleEnum.YEAR);
        config.setKeyFields(Lists.newArrayList("roomID", "dataID"));
        config.setFuncField("val");
        config.setGroupBy(Lists.newArrayList("roomID", "dataID", "tm"));
        config.setLatenessHour(24);
        config.setSubFields(Lists.newArrayList("devID"));
        config.setAggregators(Lists.newArrayList(new GeneralPeriodAggregator("sum", true,
                AggregateTypeEnum.ARITHMETIC_SUM, AggregateTypeEnum.MIN)));
        config.setPeriodField(new PeriodField("tm", "tm", "tm", AggregationCycleEnum.YEAR.getCode()));
        Map<String, String> dataTypeMap = Maps.newHashMap();
        dataTypeMap.put("roomID", "long");
        dataTypeMap.put("devID", "long");
        dataTypeMap.put("dataID", "int");
        dataTypeMap.put("logicalID", "int");
        dataTypeMap.put("dataTypeID", "int");
        dataTypeMap.put("tm", "date");
        dataTypeMap.put("val", "double");
        dataTypeMap.put("status", "byte");
        List<Row> dataSet = UnitTestUtils.readDataRowsFromCsvFile("data/room_datalog_year.csv", dataTypeMap);
        ReflectionTestUtils.setField(keyedRowProcessFunction, "config", config);
        KeyedRowProcessProperties keyedRowProcessProperties = UnitTestUtils.initializeKeyedRowProcessFunction(keyedRowProcessFunction);
        TimerService timerService = new TestTimerService();
        Row keyRow = Row.withNames();
        keyRow.setField("roomID", 1);
        keyRow.setField("dataID", 4000012);
        KeyedProcessFunctionContext context = new KeyedProcessFunctionContext(keyedRowProcessFunction, timerService, 0L, keyRow);
        TestCollector<Row> collector = new TestCollector<>();
        for (Row row : dataSet) {
            System.out.println("定时记录时间：" + DateUtil.longToString((Long) row.getField("tm"), "yyyy-MM-dd HH:mm:ss"));
            keyedRowProcessFunction.processElement(row, context, collector);
        }
        TestOnTimerContext testOnTimerContext = new TestOnTimerContext(keyedRowProcessFunction, timerService, System.currentTimeMillis(), keyRow);
        keyedRowProcessFunction.onTimer(System.currentTimeMillis(), testOnTimerContext, collector);
        Long minTime = keyedRowProcessProperties.getMinTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2023-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), minTime.longValue());
        Long maxTime = keyedRowProcessProperties.getMaxTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2025-02-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), maxTime.longValue());
        Long startTime = keyedRowProcessProperties.getStartTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2024-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), startTime.longValue());
        Integer interval = keyedRowProcessProperties.getIntervalValueState().value();
        Assert.assertEquals(40320, interval.intValue());
        Map<Row, Integer> expectedSubKeyIndex = Maps.newHashMap();
        Row device1 = Row.withNames();
        device1.setField("devID", 1L);
        expectedSubKeyIndex.put(device1, 0);
        Row device2 = Row.withNames();
        device2.setField("devID", 2L);
        expectedSubKeyIndex.put(device2, 1);
        Row device3 = Row.withNames();
        device3.setField("devID", 3L);
        expectedSubKeyIndex.put(device3, 2);
        Row device4 = Row.withNames();
        device4.setField("devID", 4L);
        expectedSubKeyIndex.put(device4, 3);
        Row device5 = Row.withNames();
        device5.setField("devID", 5L);
        expectedSubKeyIndex.put(device5, 4);
        Assert.assertEquals(expectedSubKeyIndex, keyedRowProcessProperties.getSubKeyIndexMapState().getMap());

        Map<Long, Double> timeGroup = dataSet.stream().collect(
                Collectors.groupingBy(row -> row.getFieldAs("tm"), Collectors.summingDouble(row -> row.getFieldAs("val"))));
        Map<String, Double> dayGroup = Maps.newHashMap();
        timeGroup.forEach((tm, val) -> {
            String dayString = DateUtil.longToString(tm, "yyyy-01-01");
            if (dayGroup.containsKey(dayString)) {
                if (dayGroup.get(dayString) > val) {
                    dayGroup.put(dayString, val);
                }
            } else {
                dayGroup.put(dayString, val);
            }
        });
        List<Row> records = collector.getRecords();
        equalDayRow(1, 4000012, DateUtil.stringToLong("2023-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2023-01-01"), records.get(0));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2024-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2024-01-01"), records.get(1));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2025-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2025-01-01"), records.get(2));
        Assert.assertEquals(3, records.size());
    }

    @Test
    public void testProcessElement_sum_max() throws Exception {
        KeyedRowProcessFunction keyedRowProcessFunction = new KeyedRowProcessFunction();
        KeyedRowProcessConfig config = new KeyedRowProcessConfig();
        config.setAggregationCycle(AggregationCycleEnum.YEAR);
        config.setKeyFields(Lists.newArrayList("roomID", "dataID"));
        config.setFuncField("val");
        config.setGroupBy(Lists.newArrayList("roomID", "dataID", "tm"));
        config.setLatenessHour(24);
        config.setSubFields(Lists.newArrayList("devID"));
        config.setAggregators(Lists.newArrayList(new GeneralPeriodAggregator("sum", true,
                AggregateTypeEnum.ARITHMETIC_SUM, AggregateTypeEnum.MAX)));
        config.setPeriodField(new PeriodField("tm", "tm", "tm", AggregationCycleEnum.YEAR.getCode()));
        Map<String, String> dataTypeMap = Maps.newHashMap();
        dataTypeMap.put("roomID", "long");
        dataTypeMap.put("devID", "long");
        dataTypeMap.put("dataID", "int");
        dataTypeMap.put("logicalID", "int");
        dataTypeMap.put("dataTypeID", "int");
        dataTypeMap.put("tm", "date");
        dataTypeMap.put("val", "double");
        dataTypeMap.put("status", "byte");
        List<Row> dataSet = UnitTestUtils.readDataRowsFromCsvFile("data/room_datalog_year.csv", dataTypeMap);
        ReflectionTestUtils.setField(keyedRowProcessFunction, "config", config);
        KeyedRowProcessProperties keyedRowProcessProperties = UnitTestUtils.initializeKeyedRowProcessFunction(keyedRowProcessFunction);
        TimerService timerService = new TestTimerService();
        Row keyRow = Row.withNames();
        keyRow.setField("roomID", 1);
        keyRow.setField("dataID", 4000012);
        KeyedProcessFunctionContext context = new KeyedProcessFunctionContext(keyedRowProcessFunction, timerService, 0L, keyRow);
        TestCollector<Row> collector = new TestCollector<>();
        for (Row row : dataSet) {
            System.out.println("定时记录时间：" + DateUtil.longToString((Long) row.getField("tm"), "yyyy-MM-dd HH:mm:ss"));
            keyedRowProcessFunction.processElement(row, context, collector);
        }
        TestOnTimerContext testOnTimerContext = new TestOnTimerContext(keyedRowProcessFunction, timerService, System.currentTimeMillis(), keyRow);
        keyedRowProcessFunction.onTimer(System.currentTimeMillis(), testOnTimerContext, collector);
        Long minTime = keyedRowProcessProperties.getMinTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2023-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), minTime.longValue());
        Long maxTime = keyedRowProcessProperties.getMaxTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2025-02-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), maxTime.longValue());
        Long startTime = keyedRowProcessProperties.getStartTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2024-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), startTime.longValue());
        Integer interval = keyedRowProcessProperties.getIntervalValueState().value();
        Assert.assertEquals(40320, interval.intValue());
        Map<Row, Integer> expectedSubKeyIndex = Maps.newHashMap();
        Row device1 = Row.withNames();
        device1.setField("devID", 1L);
        expectedSubKeyIndex.put(device1, 0);
        Row device2 = Row.withNames();
        device2.setField("devID", 2L);
        expectedSubKeyIndex.put(device2, 1);
        Row device3 = Row.withNames();
        device3.setField("devID", 3L);
        expectedSubKeyIndex.put(device3, 2);
        Row device4 = Row.withNames();
        device4.setField("devID", 4L);
        expectedSubKeyIndex.put(device4, 3);
        Row device5 = Row.withNames();
        device5.setField("devID", 5L);
        expectedSubKeyIndex.put(device5, 4);
        Assert.assertEquals(expectedSubKeyIndex, keyedRowProcessProperties.getSubKeyIndexMapState().getMap());

        Map<Long, Double> timeGroup = dataSet.stream().collect(
                Collectors.groupingBy(row -> row.getFieldAs("tm"), Collectors.summingDouble(row -> row.getFieldAs("val"))));
        Map<String, Double> dayGroup = Maps.newHashMap();
        timeGroup.forEach((tm, val) -> {
            String dayString = DateUtil.longToString(tm, "yyyy-01-01");
            if (dayGroup.containsKey(dayString)) {
                if (dayGroup.get(dayString) < val) {
                    dayGroup.put(dayString, val);
                }
            } else {
                dayGroup.put(dayString, val);
            }
        });
        List<Row> records = collector.getRecords();
        equalDayRow(1, 4000012, DateUtil.stringToLong("2023-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2023-01-01"), records.get(0));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2024-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2024-01-01"), records.get(1));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2025-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2025-01-01"), records.get(2));
        Assert.assertEquals(3, records.size());
    }

    @Test
    public void testProcessElement_sum_avg() throws Exception {
        KeyedRowProcessFunction keyedRowProcessFunction = new KeyedRowProcessFunction();
        KeyedRowProcessConfig config = new KeyedRowProcessConfig();
        config.setAggregationCycle(AggregationCycleEnum.YEAR);
        config.setKeyFields(Lists.newArrayList("roomID", "dataID"));
        config.setFuncField("val");
        config.setGroupBy(Lists.newArrayList("roomID", "dataID", "tm"));
        config.setLatenessHour(24);
        config.setSubFields(Lists.newArrayList("devID"));
        config.setAggregators(Lists.newArrayList(new GeneralPeriodAggregator("sum", true,
                AggregateTypeEnum.ARITHMETIC_SUM, AggregateTypeEnum.AVG)));
        config.setPeriodField(new PeriodField("tm", "tm", "tm", AggregationCycleEnum.YEAR.getCode()));
        Map<String, String> dataTypeMap = Maps.newHashMap();
        dataTypeMap.put("roomID", "long");
        dataTypeMap.put("devID", "long");
        dataTypeMap.put("dataID", "int");
        dataTypeMap.put("logicalID", "int");
        dataTypeMap.put("dataTypeID", "int");
        dataTypeMap.put("tm", "date");
        dataTypeMap.put("val", "double");
        dataTypeMap.put("status", "byte");
        List<Row> dataSet = UnitTestUtils.readDataRowsFromCsvFile("data/room_datalog_year.csv", dataTypeMap);
        ReflectionTestUtils.setField(keyedRowProcessFunction, "config", config);
        KeyedRowProcessProperties keyedRowProcessProperties = UnitTestUtils.initializeKeyedRowProcessFunction(keyedRowProcessFunction);
        TimerService timerService = new TestTimerService();
        Row keyRow = Row.withNames();
        keyRow.setField("roomID", 1);
        keyRow.setField("dataID", 4000012);
        KeyedProcessFunctionContext context = new KeyedProcessFunctionContext(keyedRowProcessFunction, timerService, 0L, keyRow);
        TestCollector<Row> collector = new TestCollector<>();
        for (Row row : dataSet) {
            System.out.println("定时记录时间：" + DateUtil.longToString((Long) row.getField("tm"), "yyyy-MM-dd HH:mm:ss"));
            keyedRowProcessFunction.processElement(row, context, collector);
        }
        TestOnTimerContext testOnTimerContext = new TestOnTimerContext(keyedRowProcessFunction, timerService, System.currentTimeMillis(), keyRow);
        keyedRowProcessFunction.onTimer(System.currentTimeMillis(), testOnTimerContext, collector);
        Long minTime = keyedRowProcessProperties.getMinTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2023-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), minTime.longValue());
        Long maxTime = keyedRowProcessProperties.getMaxTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2025-02-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), maxTime.longValue());
        Long startTime = keyedRowProcessProperties.getStartTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2024-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), startTime.longValue());
        Integer interval = keyedRowProcessProperties.getIntervalValueState().value();
        Assert.assertEquals(40320, interval.intValue());
        Map<Row, Integer> expectedSubKeyIndex = Maps.newHashMap();
        Row device1 = Row.withNames();
        device1.setField("devID", 1L);
        expectedSubKeyIndex.put(device1, 0);
        Row device2 = Row.withNames();
        device2.setField("devID", 2L);
        expectedSubKeyIndex.put(device2, 1);
        Row device3 = Row.withNames();
        device3.setField("devID", 3L);
        expectedSubKeyIndex.put(device3, 2);
        Row device4 = Row.withNames();
        device4.setField("devID", 4L);
        expectedSubKeyIndex.put(device4, 3);
        Row device5 = Row.withNames();
        device5.setField("devID", 5L);
        expectedSubKeyIndex.put(device5, 4);
        Assert.assertEquals(expectedSubKeyIndex, keyedRowProcessProperties.getSubKeyIndexMapState().getMap());

        Map<Long, Double> timeGroup = dataSet.stream().collect(
                Collectors.groupingBy(row -> row.getFieldAs("tm"), Collectors.summingDouble(row -> row.getFieldAs("val"))));
        Map<String, Double> dayGroup = Maps.newHashMap();
        Map<String, Integer> countGroup = Maps.newHashMap();
        timeGroup.forEach((tm, val) -> {
            String dayString = DateUtil.longToString(tm, "yyyy-01-01");
            if (dayGroup.containsKey(dayString)) {
                dayGroup.put(dayString, val + dayGroup.get(dayString));
                countGroup.put(dayString, countGroup.get(dayString) + 1);
            } else {
                dayGroup.put(dayString, val);
                countGroup.put(dayString, 1);
            }
        });
        List<Row> records = collector.getRecords();
        equalDayRow(1, 4000012, DateUtil.stringToLong("2023-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2023-01-01")/countGroup.get("2023-01-01"), records.get(0));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2024-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2024-01-01")/countGroup.get("2024-01-01"), records.get(1));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2025-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                dayGroup.get("2025-01-01")/countGroup.get("2025-01-01"), records.get(2));
        Assert.assertEquals(3, records.size());
    }

    @Test
    public void testProcessElement_sum_count() throws Exception {
        KeyedRowProcessFunction keyedRowProcessFunction = new KeyedRowProcessFunction();
        KeyedRowProcessConfig config = new KeyedRowProcessConfig();
        config.setAggregationCycle(AggregationCycleEnum.YEAR);
        config.setKeyFields(Lists.newArrayList("roomID", "dataID"));
        config.setFuncField("val");
        config.setGroupBy(Lists.newArrayList("roomID", "dataID", "tm"));
        config.setLatenessHour(24);
        config.setSubFields(Lists.newArrayList("devID"));
        config.setAggregators(Lists.newArrayList(new GeneralPeriodAggregator("sum", true,
                AggregateTypeEnum.ARITHMETIC_SUM, AggregateTypeEnum.COUNT)));
        config.setPeriodField(new PeriodField("tm", "tm", "tm", AggregationCycleEnum.YEAR.getCode()));
        Map<String, String> dataTypeMap = Maps.newHashMap();
        dataTypeMap.put("roomID", "long");
        dataTypeMap.put("devID", "long");
        dataTypeMap.put("dataID", "int");
        dataTypeMap.put("logicalID", "int");
        dataTypeMap.put("dataTypeID", "int");
        dataTypeMap.put("tm", "date");
        dataTypeMap.put("val", "double");
        dataTypeMap.put("status", "byte");
        List<Row> dataSet = UnitTestUtils.readDataRowsFromCsvFile("data/room_datalog_year.csv", dataTypeMap);
        ReflectionTestUtils.setField(keyedRowProcessFunction, "config", config);
        KeyedRowProcessProperties keyedRowProcessProperties = UnitTestUtils.initializeKeyedRowProcessFunction(keyedRowProcessFunction);
        TimerService timerService = new TestTimerService();
        Row keyRow = Row.withNames();
        keyRow.setField("roomID", 1);
        keyRow.setField("dataID", 4000012);
        KeyedProcessFunctionContext context = new KeyedProcessFunctionContext(keyedRowProcessFunction, timerService, 0L, keyRow);
        TestCollector<Row> collector = new TestCollector<>();
        for (Row row : dataSet) {
            System.out.println("定时记录时间：" + DateUtil.longToString((Long) row.getField("tm"), "yyyy-MM-dd HH:mm:ss"));
            keyedRowProcessFunction.processElement(row, context, collector);
        }
        TestOnTimerContext testOnTimerContext = new TestOnTimerContext(keyedRowProcessFunction, timerService, System.currentTimeMillis(), keyRow);
        keyedRowProcessFunction.onTimer(System.currentTimeMillis(), testOnTimerContext, collector);
        Long minTime = keyedRowProcessProperties.getMinTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2023-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), minTime.longValue());
        Long maxTime = keyedRowProcessProperties.getMaxTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2025-02-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), maxTime.longValue());
        Long startTime = keyedRowProcessProperties.getStartTimeValueState().value();
        Assert.assertEquals(DateUtil.stringToLong("2024-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"), startTime.longValue());
        Integer interval = keyedRowProcessProperties.getIntervalValueState().value();
        Assert.assertEquals(40320, interval.intValue());
        Map<Row, Integer> expectedSubKeyIndex = Maps.newHashMap();
        Row device1 = Row.withNames();
        device1.setField("devID", 1L);
        expectedSubKeyIndex.put(device1, 0);
        Row device2 = Row.withNames();
        device2.setField("devID", 2L);
        expectedSubKeyIndex.put(device2, 1);
        Row device3 = Row.withNames();
        device3.setField("devID", 3L);
        expectedSubKeyIndex.put(device3, 2);
        Row device4 = Row.withNames();
        device4.setField("devID", 4L);
        expectedSubKeyIndex.put(device4, 3);
        Row device5 = Row.withNames();
        device5.setField("devID", 5L);
        expectedSubKeyIndex.put(device5, 4);
        Assert.assertEquals(expectedSubKeyIndex, keyedRowProcessProperties.getSubKeyIndexMapState().getMap());

        Map<Long, Double> timeGroup = dataSet.stream().collect(
                Collectors.groupingBy(row -> row.getFieldAs("tm"), Collectors.summingDouble(row -> row.getFieldAs("val"))));
        Map<String, Integer> countGroup = Maps.newHashMap();
        timeGroup.forEach((tm, val) -> {
            String dayString = DateUtil.longToString(tm, "yyyy-01-01");
            if (countGroup.containsKey(dayString)) {
                countGroup.put(dayString, countGroup.get(dayString) + 1);
            } else {
                countGroup.put(dayString, 1);
            }
        });
        List<Row> records = collector.getRecords();
        equalDayRow(1, 4000012, DateUtil.stringToLong("2023-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                countGroup.get("2023-01-01").doubleValue(), records.get(0));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2024-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                countGroup.get("2024-01-01").doubleValue(), records.get(1));
        equalDayRow(1, 4000012, DateUtil.stringToLong("2025-01-01 00:00:00","yyyy-MM-dd HH:mm:ss"),
                countGroup.get("2025-01-01").doubleValue(), records.get(2));
        Assert.assertEquals(3, records.size());
    }
}