package com.cet.fbp.common.component.source;

import com.cet.fbp.common.bean.*;
import com.cet.fbp.common.bean.datacachelibsrv.*;
import com.cet.fbp.common.define.ConstDef;
import com.cet.fbp.common.frame.FbpProperties;
import com.cet.fbp.common.thirdpart.JsonTransferUtils;
import com.cet.fbp.common.util.HttpUtils;
import com.cet.fbp.common.util.LibSerializeHelper;
import com.cet.fbp.common.util.PublicFunc;
import com.cet.fbp.common.util.TimeFunc;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.util.IOUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时记录重算数据查询类（优先从缓存中查询，缓存中缺失的，从数据库中补充完整
 * 重算时，是否需要考虑新增了设备（暂未考虑）
 * 实时计算时，可以自动更新设备范围（每次读取缓存索引时，同步更新下设备范围）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class RecalcDatalogReader implements Serializable {


    //region 常量&静态成员
    /**
     * 日志写入对象
     */
    private static final Logger log = LoggerFactory.getLogger(RecalcDatalogReader.class);

    /**
     * 没找到
     */
    public static final int NOT_FOUND = -1;
    /**
     * 单次查询最大序号数（块数），基本保持不变
     */
    public int MAX_COUNT_PER_QUERY = 5000;
    /**
     * DataCacheService查询url
     */
    protected String getQuerySequenceUrl(int stationId){
        return stationQueryUrlMap.get(stationId) + ConstDef.DATALOG_SEQUENCE_URL;
    }

    /**
     *
     */
    private List<String> dataCacheServiceUrls;

    /**
     * 厂站和url映射
     */
    private Map<Integer, String> stationQueryUrlMap = Maps.newConcurrentMap();

    //endregion

    //region 成员变量

    /**
     * 厂站-通道映射
     */
    private HashMap<Integer,HashSet<Integer>> stationChannelsMap = new HashMap<>();
    /**
     * 通道集合
     */
    private HashSet<Integer> channelIdSet = new HashSet<>();
    /**
     * 重算的通道集合，按id从小到大排序
     */
    private List<Integer> channelIdList = new ArrayList<>();
    /**
     * 重算设备集合
     */
    private HashSet<DeviceLogicalId> deviceIdSet = new HashSet<>();
    /**
     * 重算设备列表，从小到大排序
     */
    private List<DeviceLogicalId> deviceIdList = new ArrayList<>();

    private List<Integer> dataIdList = new ArrayList<>();

    private List<Short> dataTypeIdList = new ArrayList<>();

    /**
     * 通道对应设备id的映射
     */
    private HashMap<Integer,List<DeviceLogicalId>> channelIdDeviceIdsMap = new HashMap<>();
    /**
     * 设备及其定时记录查询参数映射
     */
    private HashMap<DeviceLogicalId,HashSet<DatalogKey>> deviceDatalogKeyMap = new HashMap<>();
    /**
     * 通道缓存文件信息，按 天-通道id-文件id 排序
     */
    protected ArrayList<ChnIDDayTmFileEndID> dataCacheIndexes = new ArrayList<>();

    /**
     * 通信配置更新时间，每30分钟更新一次
     */
    private long lastUpdateTime = 0;

    /**
     * 定时记录类型
     */
    protected CacheDataType cacheDataType;

    public StreamDatalogReaderSource.StreamDatalogPoi getDatalogPoi() {
        return this.datalogPoi;
    }

    public void setDatalogPoi(StreamDatalogReaderSource.StreamDatalogPoi datalogPoi) {
        this.datalogPoi = datalogPoi;
    }

    protected StreamDatalogReaderSource.StreamDatalogPoi datalogPoi;

    protected boolean running = true;

    //endregion

    //region 构造函数&初始化方法

    /**
     * 构造函数，logicalid默认为1
     * @param dataIdList dataid列表
     * @param dataTypeIdList datatypeid列表
     */
    public RecalcDatalogReader(String urls, List<Integer> dataIdList, List<Short> dataTypeIdList, CacheDataType cacheDataType) {
        this.cacheDataType = cacheDataType;
        this.dataIdList = dataIdList;
        this.dataTypeIdList = dataTypeIdList;
        this.running = true;
        initializeByDevDataIdDataType(urls);
    }

//    /**
//     * 构造函数，不指定设备（使用所有设备）
//     */
//    public RecalcDatalogReader(String urls, List<Integer> dataIdList, List<Short> dataTypeIdList, CacheDataType cacheDataType) {
//        this.cacheDataType = cacheDataType;
//        this.dataIdList = dataIdList;
//        this.dataTypeIdList = dataTypeIdList;
//        List<Integer> allDeviceIdList = StationChannelDeviceCfg.instance().getAllDeviceIdList();
//        initializeByDevDataIdDataType(urls, allDeviceIdList);
//    }

    /**
     * 获取设备id的抽象方法
     */
    public abstract List<DeviceLogicalId> getDeviceIds();

    /**
     * 根据设备、参数、数据类型进行成员初始化
     */
    private void initializeByDevDataIdDataType(String urls){
        //设置url
        String[] urlArray = urls.split(",");
        dataCacheServiceUrls = new ArrayList<>(urlArray.length);
        Collections.addAll(dataCacheServiceUrls, urlArray);
        refreshDeviceIds();
    }

    public void refreshDeviceIds() {
        long now = System.currentTimeMillis();
        if(now - lastUpdateTime < 30 * 60 * 1000) //30分钟更新一次
        {
            return;
        }

        List<DeviceLogicalId> deviceLogicalIdList = getDeviceIds();
        Collections.sort(deviceLogicalIdList);
        for (DeviceLogicalId deviceLogicalId: deviceLogicalIdList) {
            if(deviceIdSet.contains(deviceLogicalId)) //避免重复添加
            {
                continue;
            }
            Integer channelId = StationChannelDeviceCfg.instance().getChannelId(deviceLogicalId.getDeviceId());
            if(channelId == null) {
                continue;
            }
            Integer stationId = StationChannelDeviceCfg.instance().getStationId(channelId);
            if(stationId == null) {
                continue;
            }
            channelIdSet.add(channelId);
            HashSet<Integer> channelIdSetOfStation = this.stationChannelsMap.computeIfAbsent(stationId, station -> new HashSet<>());
            channelIdSetOfStation.add(channelId);

            this.deviceIdSet.add(deviceLogicalId);
            this.deviceIdList.add(deviceLogicalId);
            List<DeviceLogicalId> deviceIds = this.channelIdDeviceIdsMap.computeIfAbsent(channelId, integer -> new ArrayList<>());
            deviceIds.add(deviceLogicalId);

            addToDeviceDatalogKeyMap(deviceLogicalId, dataIdList, dataTypeIdList);
        }
        this.channelIdList = new ArrayList<>(channelIdSet);
        Collections.sort(this.channelIdList);
        lastUpdateTime = now;
    }

    /**
     * 将定时记录参数添加到deviceDatalogKeyMap中
     * @param deviceLogicalId 设备id
     * @param dataIdList dataid列表
     * @param dataTypeIdList datatypeid列表
     */
    private void addToDeviceDatalogKeyMap(DeviceLogicalId deviceLogicalId, List<Integer> dataIdList, List<Short> dataTypeIdList) {
        HashSet<DatalogKey> datalogKeySet = this.deviceDatalogKeyMap.computeIfAbsent(deviceLogicalId, dev -> new HashSet<>());
        for (Integer dataId : dataIdList) {
            for(Short dataTypeId : dataTypeIdList){
                datalogKeySet.add(new DatalogKey(deviceLogicalId.getDeviceId(),dataId,  deviceLogicalId.getLogicalId() ,dataTypeId));
            }
        }
    }

    //endregion



    //region 读取缓存索引信息

    /**
     * 读取缓存索引信息
     */
    public void readDataCacheIndexes()
    {
        this.dataCacheIndexes.clear();
        if (this.channelIdList.isEmpty()){
            return;
        }
        for (Map.Entry<Integer, HashSet<Integer>> entry:this.stationChannelsMap.entrySet()) {
            //需要判断出来station对应的url是哪个
            DataLogIdxParam dataLogIdxQueryParam = getDataLogIdxQueryParam(entry.getKey(),entry.getValue());
            if(this.dataCacheServiceUrls.size() > 1 && this.stationQueryUrlMap.get(entry.getKey()) == null){
                for (String url: dataCacheServiceUrls) {
                    String queryDataLogIdxUrl = url + ConstDef.DATALOG_IDX_COUNT_URL;
                    String idxResult = HttpUtils.post(queryDataLogIdxUrl, "application/json", JsonTransferUtils.toJSONString(dataLogIdxQueryParam), "utf-8");
                    if(parse2DataCacheIndexes(idxResult) > 0){
                        this.stationQueryUrlMap.put(entry.getKey(), url);
                        break;
                    }
                }
            }
            else{
                if(this.dataCacheServiceUrls.size() == 1 && this.stationQueryUrlMap.get(entry.getKey()) == null){
                    this.stationQueryUrlMap.put(entry.getKey(), this.dataCacheServiceUrls.get(0));
                }
                String queryDataLogIdxUrl = this.stationQueryUrlMap.get(entry.getKey()) + ConstDef.DATALOG_IDX_COUNT_URL;
                String body = JsonTransferUtils.toJSONString(dataLogIdxQueryParam);
                String idxResult = HttpUtils.post(queryDataLogIdxUrl, "application/json", body, "utf-8");
                parse2DataCacheIndexes(idxResult);
            }
        }
        Collections.sort(this.dataCacheIndexes);
        cleanExpiredDataCachePoi();
        warnIfDataCacheIsEmpty();
    }

    /**
     * 获取一个厂站的最新datacache索引查询参数
     * @param stationId 厂站id
     * @param channelIdSet 厂站下需要查询索引的通道id集合
     * @return 返回该厂站的datacache索引查询参数
     */
    @NotNull
    private DataLogIdxParam getDataLogIdxQueryParam(Integer stationId, HashSet<Integer> channelIdSet) {
        //组装查询参数
        //TimeFunc.utctoDoubleDay(this.datalogPoi.getBeginTime())
        double endTime = TimeFunc.utctoDoubleDay(TimeFunc.Today()) + 1; //结束日期：明日0点（不含）
        double beginTime = endTime - 366; //开始日期：1年前
        //开始时间不大于全局设置的开始时标
        double beginTimeOfPoi = TimeFunc.utctoDoubleDay(datalogPoi.getBeginTime());
        if(beginTime < beginTimeOfPoi) {
            beginTime = beginTimeOfPoi;
        }

        List<Integer> channelIds = new ArrayList<>(channelIdSet);
        Collections.sort(channelIds);//排序

        return new DataLogIdxParam(ConstDef.DATACACHE_JSON_RESULT,  cacheDataType.getCacheType(), cacheDataType.getHistoryDataType(), cacheDataType.getGlobalFileType(),stationId, beginTime,endTime, channelIds.size(), channelIds);
    }

    /**
     * 解析datacache索引信息，添加到dataCacheIndexes中
     * @param idxResult 索引信息json字符串
     * @return 返回查到的数量
     */
    private int parse2DataCacheIndexes(String idxResult) {
        List<DataLogIdxResult> stationDataLogIndexes = deserializeDatalogIndexes(idxResult); //一个厂站的datacache索引结构
        //解析索引到dataCacheIndexes
        if (CollectionUtils.isEmpty(stationDataLogIndexes)) {
            return 0;
        }
        int validCount = 0; //有效缓存的个数
        for (DataLogIdxResult channelDatalogIdxList:stationDataLogIndexes) {
            if(channelDatalogIdxList != null && channelDatalogIdxList.getChnID() != null){
                addChannelIdx2DataCacheIndexes(channelDatalogIdxList);
                validCount++;
            }
        }
        return validCount;
    }
    /**
     * 反序列化索引json字符串为索引结构列表
     * @param idxResult 索引查询结果
     * @return 索引查询结果
     */
    @Nullable
    private List<DataLogIdxResult> deserializeDatalogIndexes(String idxResult) {
        List<DataLogIdxResult> dataLogIdxResults = null;
        if (StringUtils.isBlank(idxResult)) {
            return dataLogIdxResults;
        }
        try{
            PecResult dataLogIdxResult = JsonTransferUtils.parseObject(idxResult, PecResult.class);
            if(dataLogIdxResult == null || dataLogIdxResult.getResult() == null){
                log.error("解析datacache索引信息失败: {}",idxResult);
                return dataLogIdxResults;
            }
            dataLogIdxResults = JsonTransferUtils.transferJsonString(JsonTransferUtils.toJSONString(dataLogIdxResult.getResult()),
                    DataLogIdxResult.class);
        }
        catch (RuntimeException ex){
            log.error("解析datacache索引信息失败: {}",idxResult);
        }
        return dataLogIdxResults;
    }

    /**
     * 添加一个通道的索引到dataCacheIndexes中
     * @param channelDatalogIdxList 通道多天datacache索引信息
     */
    private void addChannelIdx2DataCacheIndexes(DataLogIdxResult channelDatalogIdxList) {
        if(channelDatalogIdxList == null || channelDatalogIdxList.getDatas() == null) {
            return;
        }
        //一个通道多天的datacache索引结构
        List<DataLogIdx> channelDailyDatalogIdxList = channelDatalogIdxList.getDatas();
        for (DataLogIdx channelOneDayDatalogIdx:channelDailyDatalogIdxList) {
            addChannelDayIdx2DataCacheIndexes(channelDatalogIdxList.getChnID(), channelOneDayDatalogIdx);
        }
    }

    /**
     * 添加一个通道一天的索引到dataCacheIndexes中
     * @param channelId 通道id
     * @param channelOneDayDatalogIdx 一个通道1天的datacache定时记录索引结构
     */
    private void addChannelDayIdx2DataCacheIndexes(Integer channelId, DataLogIdx channelOneDayDatalogIdx) {
        if(channelOneDayDatalogIdx == null || channelOneDayDatalogIdx.getDatas() == null) {
            return;
        }
        ChnIDDayTm chnIDDayTm = new ChnIDDayTm(channelId, channelOneDayDatalogIdx.getTime());
        List<DataLogIdx.DataLogIdxData> fileDatalogIdxList = channelOneDayDatalogIdx.getDatas();
        for(DataLogIdx.DataLogIdxData fileDatalogIdx: fileDatalogIdxList){
            addFileIdx2DataCacheIndexes(chnIDDayTm, fileDatalogIdx);
        }
    }

    /**
     * 将一个datacache文件索引信息添加到dataCacheIndexes中
     * @param chnIDDayTm 通道日期结构
     * @param fileDatalogIdx 一个datacache文件块索引信息
     */
    private void addFileIdx2DataCacheIndexes(ChnIDDayTm chnIDDayTm, DataLogIdx.DataLogIdxData fileDatalogIdx) {
        if(fileDatalogIdx == null) {
            return;
        }
        FileEndID fileEndID = new FileEndID(fileDatalogIdx.getFileID(), fileDatalogIdx.getCount());
        ChnIDDayTmFileEndID dataLogIdxSequence = new ChnIDDayTmFileEndID(chnIDDayTm,fileEndID);
        this.dataCacheIndexes.add(dataLogIdxSequence);
    }

    /**
     * 清理过期datacache-poi
     */
    private void cleanExpiredDataCachePoi(){
        if(this.dataCacheIndexes.isEmpty()) {
            return;
        }
        double minDayInCache = this.dataCacheIndexes.get(0).getChnIDDayTm().getDayTm();
        Iterator<Map.Entry<ChnIDDayTm, FileEndID>> itChannlDayFileIndexMap = this.datalogPoi.getDataCachePoi().entrySet().iterator();
        while (itChannlDayFileIndexMap.hasNext()) {
            Map.Entry<ChnIDDayTm, FileEndID> chnIDDayTmFileEndIDEntry = itChannlDayFileIndexMap.next();
            //清理掉小于最小缓存时间的poi，避免积压过多，这种方式没有清理掉所有已不存在的通道日期缓存poi，但是可以避免poi的数量一直增加
            if(chnIDDayTmFileEndIDEntry.getKey().getDayTm() < minDayInCache) {
                itChannlDayFileIndexMap.remove();
            }
        }
    }

    /**
     * 如果缓存索引信息为空，输出告警信息
     */
    private void warnIfDataCacheIsEmpty() {
        if (this.dataCacheIndexes.isEmpty()){
            log.warn("ReadCahFileInfoAndSortByTime GetNormalDataLogIdxCntPtr return size<1 failed");
        }
    }
    //endregion

    //region 查询定时记录数据

    /**
     * 读取定时记录数据并收集到流中
     * 读取每个通道1天的datacache数据，如有不足，查询设备数据服务进行补充
     * @param ctx 流数据收集器
     */
    public boolean readDatalog(SourceFunction.SourceContext<OneDatalog> ctx) throws InterruptedException {
        int datacacheIndexOffset = 0; // dataCacheIndexes的偏移索引，加快处理效率
        if(this.datalogPoi.getReadPoi() < this.datalogPoi.getEndTime()){
            double day = TimeFunc.utctoDoubleDay(this.datalogPoi.getReadPoi());
            for (Integer channelId:this.channelIdList) {
                //获取通道当日datacache数据文件所在序号范围
                IndexRange dataCacheIndexRange = findDataCacheIndex(day, channelId, datacacheIndexOffset);
                //获取datacache定时记录查询参数
                List<DataLogSequenceParam> queryParaList = getDatalogQueryParaList(day, channelId, dataCacheIndexRange);
                //从datacache中查询定时记录数据
                List<OneDatalog> channelDatalogList = readDatalogsFromCache(queryParaList);
                //从数据库中补全datacache中缺失的数据
                List<OneDatalog> dbChannelDatalogList = appendDatalogsFromDB(channelId, day, channelDatalogList);
                //合并排序去重
                channelDatalogList.addAll(dbChannelDatalogList);
                long beforeSortTime = System.currentTimeMillis();
                if(channelDatalogList.size() > 20000) {
                    Collections.sort(channelDatalogList.parallelStream().collect(Collectors.toList()));
                } else {
                    Collections.sort(channelDatalogList);
                }
                long afterSortTime = System.currentTimeMillis();
                log.info("数据排序耗时:{}秒，数据条数:{}", (afterSortTime - beforeSortTime) / 1000.0,channelDatalogList.size());
                removeDuplicateRecords(channelDatalogList, OneDatalog::compareTo);
                long afterRemoveDuplicateTime = System.currentTimeMillis();
                log.info("数据去重耗时:{}秒，数据条数:{}", (afterRemoveDuplicateTime - afterSortTime) / 1000.0, channelDatalogList.size());
                //收集数据到数据源
                try{
                    channelDatalogList.forEach(ctx::collect);
                }
                catch (Exception e){
                    log.error("RecalcDatalogReader收集数据到数据源失败",e);
                }
                if(!running) //如果停止了，退出
                {
                    return false;
                }
                //更新poi
                if(dataCacheIndexRange.getEndIndex() > 0){
                    ChnIDDayTmFileEndID endDataCacheFile = this.dataCacheIndexes.get(dataCacheIndexRange.getEndIndex() - 1);
                    FileEndID fileEndID = endDataCacheFile.getFileEndID();
                    this.datalogPoi.getDataCachePoi().put(new ChnIDDayTm(channelId,day), new FileEndID(fileEndID.getFileID(),fileEndID.getEndID()));
                    datacacheIndexOffset = dataCacheIndexRange.getEndIndex();
                }
            }
            this.getDatalogPoi().setReadPoi(this.getDatalogPoi().getReadPoi() + 86400000L); //日期更新一天
        }
        return this.datalogPoi.getReadPoi() >= this.datalogPoi.getEndTime();
    }

    /**
     * 根据日期和通道id在dataCacheIndexes中查找数据所在开始-结束的序号
     * @param day 日期
     * @param channelId 通道id
     * @param datacacheIndexOffset 查找的起始位置（含）
     * @return 返回开始和结束序号（含开始，不含结束序号）
     */
    private IndexRange findDataCacheIndex(double day, int channelId, int datacacheIndexOffset){
        IndexRange dataCacheIndexRange = new IndexRange(NOT_FOUND,NOT_FOUND);
        //查找开始序号
        ChnIDDayTm channnelIdDay = new ChnIDDayTm(channelId,day);
        Comparable<ChnIDDayTm, ChnIDDayTmFileEndID> compareOperator = (target, data) -> {
            if(target.getDayTm() != data.getChnIDDayTm().getDayTm()) {
                return (int)(target.getDayTm() - data.getChnIDDayTm().getDayTm());
            }
            if(target.getChnID() != data.getChnIDDayTm().getChnID()) {
                return target.getChnID() - data.getChnIDDayTm().getChnID();
            }
            return 0;
        };
        dataCacheIndexRange.setBeginIndex(findStartIndex(channnelIdDay, this.dataCacheIndexes, compareOperator, datacacheIndexOffset));
        dataCacheIndexRange.setEndIndex(findEndIndex(channnelIdDay, this.dataCacheIndexes, compareOperator, dataCacheIndexRange.getBeginIndex()));
        return dataCacheIndexRange;
    }

    /**
     * 构造一个通道一天datacache定时记录数据的查询参数列表
     * @param day 日期
     * @param channelId 通道id
     * @param dataCacheIndexRange  通道日数据对应dataCacheIndexes的开始结束序号
     * @return 返回查询参数列表，每个查询参数最多MAX_COUNT个索引
     */
    private List<DataLogSequenceParam> getDatalogQueryParaList(double day, int channelId, IndexRange dataCacheIndexRange){
        List<DataLogSequenceParam> queryParaList = new ArrayList<>();
        int sequenceCount = 0;//一个DataLogSequenceParam中查询的文件块数
        Integer stationId = StationChannelDeviceCfg.instance().getStationId(channelId);
        if(stationId == null)
            return queryParaList;
        DataLogSequenceParam queryPara = createDataLogSequenceParam(day, stationId, channelId); //一次定时记录查询请求的参数
        List<DataLogSequence> queryFileSequenceList = new ArrayList<>(); //文件块号查询信息集合
        for(int i = dataCacheIndexRange.getBeginIndex(); i< dataCacheIndexRange.getEndIndex(); i++){
            ChnIDDayTmFileEndID datacacheFile = this.dataCacheIndexes.get(i);
            FileEndID fileEndIdPoi = this.datalogPoi.getDataCachePoi().get(datacacheFile.getChnIDDayTm());
            int startSequenceIndex = getStartSequenceIndex(fileEndIdPoi, datacacheFile);
            while(startSequenceIndex <= datacacheFile.getFileEndID().getEndID()){ //读取到文件最新索引块
                int needRead = Math.min(datacacheFile.getFileEndID().getEndID() - startSequenceIndex + 1, MAX_COUNT_PER_QUERY);
                DataLogSequence queryFileSequence = new DataLogSequence(channelId, datacacheFile.getFileEndID().getFileID(), startSequenceIndex, needRead); //文件块查询信息
                if(sequenceCount + needRead > MAX_COUNT_PER_QUERY){ //单次查询索引数量超限，将此前的参数输出到参数列表，新增查询参数
                    addToQueryParamList(queryParaList, queryPara, queryFileSequenceList);
                    queryPara = createDataLogSequenceParam(day, stationId, channelId);
                    queryFileSequenceList = new ArrayList<>();
                    sequenceCount = 0;
                }
                queryFileSequenceList.add(queryFileSequence);
                sequenceCount += needRead;
                startSequenceIndex += needRead;
            }
        }
        addToQueryParamList(queryParaList, queryPara, queryFileSequenceList); //添加最后一个查询参数
        return queryParaList;
    }

    /**
     * 创建一个datacache-datalog一个通道一天的查询入参
     * @param day 日期
     * @param channelId 通道id
     * @return datacache-datalog查询参数
     */
    @NotNull
    protected DataLogSequenceParam createDataLogSequenceParam(double day, int stationId, int channelId) {
        return new DataLogSequenceParam(ConstDef.DATACACHE_BINARY_RESULT,cacheDataType.getCacheType(),cacheDataType.getHistoryDataType(), cacheDataType.getGlobalFileType(), stationId, day, day + 1);
    }

    /**
     * 根据poi和datacache文件信息获取查询开始索引号
     * @param fileEndIdPoi datacache读取poi
     * @param datacacheFile datacache缓存文件索引信息
     * @return 返回datacacheFile的开始查询索引，如果大于datacacheFile的endid，说明已处理完成
     */
    protected int getStartSequenceIndex(FileEndID fileEndIdPoi, ChnIDDayTmFileEndID datacacheFile) {
        int startSequenceIndex;
        if(fileEndIdPoi == null || fileEndIdPoi.fileID < datacacheFile.getFileEndID().getFileID())
            startSequenceIndex = 1; //poi不存在或poi中的标记的file已不存在，从文件开头查询数据
        else if(fileEndIdPoi.fileID == datacacheFile.getFileEndID().getFileID()){ //poi中fileid与当前datacachefile相同，从poi的下一个序号读起
            if(datacacheFile.getFileEndID().getEndID() >= fileEndIdPoi.getEndID()) {
                startSequenceIndex = fileEndIdPoi.getEndID() + 1; //poi = datacach，表示poi在缓存文件中，从当前poi向前查询定时记录
            } else {
                startSequenceIndex = 1; //主备切换之后，如果datacache中的endid还小于poi中的endid，则从文件开头查询，避免数据遗漏
            }
        }
        else {
            startSequenceIndex = datacacheFile.getFileEndID().getEndID() + 1; // poi > datacache最大文件id，可能是该文件已完成查询，也可能是poi标记的缓存文件块已被清理，数据在datacache缓存中无法找到，例如flink在重算期间退出，然后当日缓存文件被清理，此时存在数据缺失，需从数据库中补充完整当日数据，此处继续处理下一个缓存文件即可
        }
        return startSequenceIndex;
    }

    /**
     * 将dataLogSequenceParam信息添加到参数集合queryParams中去
     * @param queryParams 定时记录查询参数集合
     * @param dataLogSequenceParam 定时记录查询参数，对应一次http请求
     * @param dataLogSequences 定时记录查询参数中的文件块查询列表
     */
    private void addToQueryParamList(List<DataLogSequenceParam> queryParams, DataLogSequenceParam dataLogSequenceParam, List<DataLogSequence> dataLogSequences) {
        if(CollectionUtils.isEmpty(dataLogSequences)) {
            return;
        }
        dataLogSequenceParam.setParams(dataLogSequences);
        dataLogSequenceParam.setParamsCnt(dataLogSequences.size());
        queryParams.add(dataLogSequenceParam);
    }

    /**
     * 从datachache中读取一个通道一天的数据
     * @param queryParaList datacache定时记录查询参数里欸包
     * @return 定时记录查询结果
     */
    @NotNull
    private List<OneDatalog> readDatalogsFromCache(List<DataLogSequenceParam> queryParaList) {
        List<OneDatalog> channelDatalogList = new ArrayList<>(); //一个通道的数据
        for (DataLogSequenceParam queryPara: queryParaList) {
            if(!running) {
                break;
            }
            List<OneDatalog> datalogList = queryDatalogFromDataCacheService(queryPara);
            channelDatalogList.addAll(datalogList);
        }
        if(channelDatalogList.size() > 20000) {
            Collections.sort(channelDatalogList.parallelStream().collect(Collectors.toList()));
        } else {
            Collections.sort(channelDatalogList);
        }
        return channelDatalogList;
    }

    @NotNull
    protected List<OneDatalog> queryDatalogFromDataCacheService(DataLogSequenceParam queryPara) {
        String body = JsonTransferUtils.toJSONString(queryPara);
        while(true){
            try {
                if(!running)
                    break;
                Date queryBeginTime = new Date();
                byte[] dataCacheBuffer = HttpUtils.binaryPost(getQuerySequenceUrl(queryPara.getStaID()), "application/json", body, "utf-8");
                Date queryEndTime = new Date();
                // 二进制数据解析到map中
                Map<Long, List<DataBinary>> map = new HashMap<>();
                int code = parseDataCacheBuffer(dataCacheBuffer, map);
                if(code != 0 && !FbpProperties.instance().getDataCacheSkipCodeSet().contains(code)){
                    log.error("从datacache读取定时记录失败，重试中，queryPara={},code:{}", body, code);
                    Thread.sleep(1000);
                    continue;
                }
                List<OneDatalog> datalogList = parseToDatalogList(map);
                Date parseEndTime = new Date();
                log.info("缓存查到 {} 条定时记录，读耗时 {} ms，解析 {} ms, {}\n",datalogList.size(),queryEndTime.getTime() - queryBeginTime.getTime(), parseEndTime.getTime() - queryEndTime.getTime(), getQueryInfo(queryPara));
                return datalogList;
            } catch (Exception e) {
                log.error("从datacache读取定时记录失败，重试中，queryPara={}", body, e);
            }
        }
        return new ArrayList<>();
    }

    @NotNull
    private StringBuilder getQueryInfo(DataLogSequenceParam queryPara) {
        StringBuilder sbQueryInfo = new StringBuilder();
        queryPara.getParams().forEach(sequence -> sbQueryInfo.append(" channel: " + sequence.getChnID())
                .append(" file: " + sequence.getFileID())
                .append(" start: " + sequence.getStartID())
                .append(" count: " + sequence.getNeedRead()));
        String cacheType;
        if(queryPara.getCacheType() == 1){
            cacheType = "datacache";
        }
        else if(queryPara.getCacheType() == 2){
            cacheType = "flink";
        }
        else{
            cacheType = queryPara.getCacheType().toString();
        }
        sbQueryInfo.append(" cachetype: ").append(cacheType).append(" date: ").append(TimeFunc.utcToTimeDescription(TimeFunc.doubleDayToUtc(queryPara.getBeginTime())));
        return sbQueryInfo;
    }

    /**
     * 将二进制流转换成通道-二进制定时记录数据映射
     * @param buffer datacache服务返回的定时记录二进制流数据
     * @return 返回查询code
     */
    private int parseDataCacheBuffer(byte[] buffer, Map<Long, List<DataBinary>> map) {
        int code = -1;
        if (buffer == null) {
            return code;
        }
        DataInputStream dis = null;
        try {
            dis = new DataInputStream(new ByteArrayInputStream(buffer));
            code = LibSerializeHelper.readInt(dis);
            long msgLen = LibSerializeHelper.readUnsignedIntOrLong(dis);
            String msg = LibSerializeHelper.readString(dis, (int) msgLen);
            if (code != 0) {
                log.error("read {} failed code: {} msg: {}", ConstDef.DATALOG_SEQUENCE_URL, code , msg);
                if(!FbpProperties.instance().getDataCacheSkipCodeSet().contains(code)) //持续重试
                {
                    return code;
                }
            }
            int chnCount = (int) LibSerializeHelper.readUnsignedIntOrLong(dis); //解析chn count：这里是1
            for (int i = 0; i < chnCount; i++) {
                long chnId = LibSerializeHelper.readUnsignedIntOrLong(dis);
                long deviceDataCount = LibSerializeHelper.readUnsignedIntOrLong(dis);
                List<DataBinary> dataBinaries = new ArrayList<>();
                map.put(chnId, dataBinaries);
                for (int j = 0; j < deviceDataCount; ++j) {
                    long dataSize = LibSerializeHelper.readUnsignedIntOrLong(dis);
                    byte[] bytes = LibSerializeHelper.readByteArray((int) dataSize, dis);
                    dataBinaries.add(new DataBinary(dataSize, bytes));
                }
            }
        } catch (Exception e) {
            log.error("convert binary error", e);
        } finally {
            IOUtils.closeQuietly(dis);
        }
        return code;
    }

    /**
     * 将二进制数据转换成定时记录数据
     * @param map 通道及其二进制定时记录数据映射
     * @return 返回通道定时记录数据列表
     */
    @NotNull
    private List<OneDatalog> parseToDatalogList(Map<Long, List<DataBinary>> map) {
        List<OneDatalog> datalogList = new ArrayList<>();
        map.forEach((chnId, dataBinaries) -> {
            dataBinaries.forEach(dataBinary -> {
                ByteArrayInputStream bis2 = null;
                DataInputStream dis2 = null;
                try {
                    bis2 = new ByteArrayInputStream(dataBinary.getDatas());
                    dis2 = new DataInputStream(bis2);
                    for (int k = 0; k < dataBinary.getDataSize(); k++) {
                        short wYear = LibSerializeHelper.readUShort(dis2);
                        short wMonth = LibSerializeHelper.readUShort(dis2);
                        short wDayOfWeek = LibSerializeHelper.readUShort(dis2);
                        short wDay = LibSerializeHelper.readUShort(dis2);
                        short wHour = LibSerializeHelper.readUShort(dis2);
                        short wMinute = LibSerializeHelper.readUShort(dis2);
                        short wSecond = LibSerializeHelper.readUShort(dis2);
                        short wMilliseconds = LibSerializeHelper.readUShort(dis2);
                        LibSerializeHelper.readUnsignedIntOrLong(dis2);
                        LibSerializeHelper.readUnsignedIntOrLong(dis2);
                        long devId = LibSerializeHelper.readUnsignedIntOrLong(dis2);
                        long dataCount = (int) LibSerializeHelper.readUnsignedIntOrLong(dis2);
                        SYSTEMTIME sysTm = new SYSTEMTIME(wYear, wMonth, wDayOfWeek, wDay, wHour, wMinute, wSecond, wMilliseconds);
                        k += 32;
                        int dataID;
                        int logicalID;
                        int dataTypeID;
                        double value;
                        byte status;
                        for (int d = 0; d < dataCount; d++) {
                            dataID = LibSerializeHelper.readInt(dis2);
                            logicalID = LibSerializeHelper.readUnsigdedShort(dis2);
                            dataTypeID = LibSerializeHelper.readUnsigdedShort(dis2);
                            value = LibSerializeHelper.readDouble(dis2);
                            status = LibSerializeHelper.readByte(dis2);
                            DatalogKey dtKey = new DatalogKey((int)devId, dataID, (short)logicalID, (short)dataTypeID);
                            if (isValidData(value,status, dtKey)){ //只添加需要的数据
                                double formattedValue = PublicFunc.KeepDecimalPlaces(value,6);
                                datalogList.add(new OneDatalog(dtKey, new DatalogVal(sysTm, formattedValue, status)));
                            }

                            k += 17;
                        }
                    }
                    IOUtils.closeQuietly(bis2);
                    IOUtils.closeQuietly(dis2);
                } catch (Exception e) {
                    log.error("binary convert to data error.", e);
                } finally {
                    IOUtils.closeQuietly(bis2);
                    IOUtils.closeQuietly(dis2);
                }
            });
        });
        return datalogList;
    }

    /**
     * 从数据库中补全datacache中缺失的定时记录数据
     * @param day 日期
     * @param channelDatalogList datacache查询到的通道定时记录
     * @return 数据库中补充的定时记录列表
     */
    @NotNull
    private List<OneDatalog> appendDatalogsFromDB(Integer channelId, double day, List<OneDatalog> channelDatalogList) throws InterruptedException {
        List<OneDatalog> dbChannelDatalogList = new ArrayList<>(); //从数据库中查询到的通道定时记录数据
        //分析datacache结果：遍历每个设备的数据，判断是否有缺失，如有缺失，则从数据库中查询当日数据，合并datacache缓存数据
        int offset = 0; //搜索偏移量
        List<DeviceLogicalId> deviceLogicalIds = this.channelIdDeviceIdsMap.computeIfAbsent(channelId, integer -> new ArrayList<>());
        List<DatalogKey> datalogKeyList = new ArrayList<>();
        for (DeviceLogicalId deviceLogicalId: deviceLogicalIds) {
            if(!running) //收到停止命令，立即返回
            {
                return dbChannelDatalogList;
            }
            int startIndex = findStartIndex(deviceLogicalId, channelDatalogList, (target, data) -> {
                if(target.getDeviceId() == data.getDtKey().getDevID()) {
                    return target.getLogicalId() - data.getDtKey().getLogicalID();
                }
                return target.getDeviceId() - data.getDtKey().getDevID();
            },offset);//根据deviceid找到channelDatalogList中对应的第一个数据，如果大于0点，则需要补全数据
            int endIndex = findEndIndex(deviceLogicalId, channelDatalogList, (target, data) -> {
                if(target.getDeviceId() == data.getDtKey().getDevID()) {
                    return target.getLogicalId() - data.getDtKey().getLogicalID();
                }
                return target.getDeviceId() - data.getDtKey().getDevID();
            }, startIndex);//找到最后一个数据，如果小于23点，则需要从数据库中补全数据
            if(!isDeviceDataComplete(channelDatalogList, startIndex, endIndex)){
                //补全设备定时记录，添加到 dbChannelDatalogList 中
                datalogKeyList.addAll(getDatalogKeyList(deviceLogicalId));
                if(datalogKeyList.size() >= 100){
                    List<OneDatalog> dbDeviceDatalogList = PublicFunc.readOneDayDtlogFromDevDataSvr0(datalogKeyList, day);
                    dbChannelDatalogList.addAll(dbDeviceDatalogList);
                    datalogKeyList.clear();
                }
            }
            offset += (endIndex - startIndex);
        }
        if(datalogKeyList.isEmpty()) {
            return dbChannelDatalogList;
        }
        List<OneDatalog> dbDeviceDatalogList = PublicFunc.readOneDayDtlogFromDevDataSvr0(datalogKeyList, day);
        dbChannelDatalogList.addAll(dbDeviceDatalogList);
        return dbChannelDatalogList;
    }

    /**
     * 验证设备的数据是否已从datacache中查询完整
     * @param channelDatalogList 设备所属通道下所有当日定时记录集合
     * @param startIndex 设备定时记录开始序号
     * @param endIndex 设备定时记录结束序号（不含）
     * @return true-数据完整，false-数据不完整
     */
    private boolean isDeviceDataComplete(List<OneDatalog> channelDatalogList, int startIndex, int endIndex) {
        return startIndex != NOT_FOUND && channelDatalogList.get(startIndex).getDtVal().getTm().wHour == 0 && channelDatalogList.get(endIndex - 1).getDtVal().getTm().wHour == 23;
    }

    /**
     * 获取设备定时记录查询参数
     * @param deviceLogicalId 设备id
     * @return 定时记录查询参数
     */
    private List<DatalogKey> getDatalogKeyList(DeviceLogicalId deviceLogicalId){
        List<DatalogKey> datalogKeyList = new ArrayList<>();
        HashSet<DatalogKey> datalogKeys = this.deviceDatalogKeyMap.get(deviceLogicalId);
        if(datalogKeys != null) {
            datalogKeyList = new ArrayList<>(datalogKeys);
        }
        return datalogKeyList;
    }

    /**
     * 判断是否有效数据
     * @param value 数值
     * @param status 状态，大于1表示无效数据？
     * @param dtKey 定时记录参数
     * @return true-有效，false-无效
     */
    private boolean isValidData(double value, byte status, DatalogKey dtKey) {
        if(value == ConstDef.NA || status > 1) {
            return false;
        }

        HashSet<DatalogKey> datalogKeys = this.deviceDatalogKeyMap.get(new DeviceLogicalId(dtKey.getDevID(), dtKey.getLogicalID()));

        if(datalogKeys == null) //说明该设备不在查询范围内
        {
            return false;
        }

        if(datalogKeys.isEmpty()) //不限制参数查询范围，返回true
        {
            return true;
        }

        return datalogKeys.contains(dtKey); //限制了参数查询范围，看是否在范围内
    }

    //endregion

    //region 通用方法和类定义

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexRange {
        /**
         * 开始索引，包含
         */
        private int beginIndex;
        /**
         * 结束索引，不包含
         */
        private int endIndex;
    }

    /**
     * 查找target在dataList中的第一个序号
     * @param target 查找目标
     * @param dataList 排序数据列表
     * @param compareOperator 比较器
     * @param offset 序号偏移量（含）
     * @param <T> 查找目标类型
     * @param <D> 数据类型
     * @return 第一个与target相等的序号，未找到返回NOT_FOUND(-1)
     */
    private static <T,D extends java.lang.Comparable<D>> int findStartIndex(T target, List<D> dataList, Comparable<T,D> compareOperator, int offset){
        for(int i = offset;i<dataList.size();i++){
            int compareResult = compareOperator.compareTo(target, dataList.get(i));
            if(compareResult == 0) //找到第一个相等的元素
                return i;
            if(compareResult < 0)
                return NOT_FOUND;
        }
        return NOT_FOUND;
    }

    /**
     * 查找target在dataList中的最后一个序号（不含）
     * @param target 查找目标
     * @param dataList 排序数据列表
     * @param findOperator 查找的操作符
     * @param startIndex 开始序号
     * @param <T> 查找目标类型
     * @param <D> 数据类型
     * @return 当startIndex有效时返回第一个大于target的序号，未找到返回dataList的长度
     */
    private static <T,D extends java.lang.Comparable<D>> int findEndIndex(T target, List<D> dataList, Comparable<T,D> findOperator, int startIndex){
        if(startIndex == NOT_FOUND) //开始索引无效，结束索引也返回无效
            return NOT_FOUND;

        for(int i = startIndex + 1; i<dataList.size(); i++){
            if(findOperator.compareTo(target,dataList.get(i)) < 0) //找到最后一个不相等的元素
                return i;
        }
        return dataList.size();
    }

    /**
     * 去除列表中的重复数据
     * @param dataList 数据列表
     * @param compareOperator 数据比较器
     * @param <T> 数据类型
     */
    private static <T extends java.lang.Comparable<T>> void removeDuplicateRecords(List<T> dataList, Comparable<T,T> compareOperator){
        if(dataList == null || dataList.size() <= 1)
            return;
        for(int i = 1; i<dataList.size(); i++){
            if(compareOperator.compareTo(dataList.get(i - 1),dataList.get(i)) == 0){
                dataList.remove(i);
                i--;
            }
        }
    }

    /**
     * 可查找接口定义
     * @param <T> target类型定义
     * @param <D> 数据列表类型定义
     */
    public interface Comparable<T,D>{
        /**
         * 对比方法
         * @param target 需要在dataList中查找的目标
         * @param data 数据列表
         * @return 返回
         */
        public int compareTo(T target,  D data);
    }
    //endregion
}
