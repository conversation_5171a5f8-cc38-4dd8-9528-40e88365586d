package com.cet.engine.plugin.base.flink.transform;

import com.cet.engine.model.DataExceptPara;
import com.cet.engine.model.DifferenceLog;
import com.cet.engine.model.ExceptionLog;
import com.cet.engine.model.ModifiedLog;
import com.cet.engine.utils.ExceptionHandler;
import com.cet.fbp.common.bean.*;
import com.cet.fbp.common.define.ConstDef;
import com.cet.fbp.common.frame.FbpProperties;
import com.cet.fbp.common.util.TimeFunc;
import com.sun.jna.ptr.IntByReference;
import org.apache.commons.compress.utils.Lists;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.SQLException;
import java.util.*;

import static com.cet.engine.common.TimestampConverter.convertSystemTimeToTimestamp;
import static com.cet.engine.common.TimestampConverter.convertTimestampToSystemTime;

public class EnergyExceptionProcessor extends KeyedProcessFunction<Map<String, Object>, Row, Row> {
    private static final Logger log = LoggerFactory.getLogger(EnergyExceptionProcessor.class);

    public EnergyExceptionProcessor() {
        euclideanDistanceTag = new OutputTag<Tuple2<Map<String, Object>,DataVal>>("EuclideanDistanceTemp"){};
        modifyDatalogListTag = new OutputTag<List<ModifiedLog>>("ModifyDatalogListTemp"){};
    }

    public EnergyExceptionProcessor(Map<String,String> row2TableMap, Map<String,String> table2RowMap, Map<String, String> groupByMap, DataExceptPara dataExceptPara, Map<String,Object> exceptPara,  String id) {
        euclideanDistanceTag = new OutputTag<Tuple2<Map<String, Object>,DataVal>>("EuclideanDistanceTemp"){};
        modifyDatalogListTag = new OutputTag<List<ModifiedLog>>("ModifyDatalogListTemp"){};
        this.row2TableMap = row2TableMap;
        this.table2RowMap = table2RowMap;
        this.groupByMap = groupByMap;
        this.dataExceptPara = dataExceptPara;
        this.id = id;

        Map<String, Boolean> convertedMap = getStringBooleanMap(exceptPara);
        this.exceptionHandler = new ExceptionHandler(convertedMap);
    }

    private static @NotNull Map<String, Boolean> getStringBooleanMap(Map<String, Object> exceptPara) {
        Map<String, Boolean> convertedMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : exceptPara.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof Boolean) {
                convertedMap.put(key, (Boolean) value);
            } else if (value != null) {
                // 这里假设非空值都转换为 false，null 值也转换为 false
                convertedMap.put(key, false);
            } else {
                convertedMap.put(key, false);
            }
        }
        return convertedMap;
    }

    private String id = null;
    private DataExceptPara dataExceptPara;

    private ExceptionHandler exceptionHandler = new ExceptionHandler();

    private Map<String,String> row2TableMap = new HashMap<>();
    private Map<String,String> table2RowMap = new HashMap<>();
    private Map<String, String> groupByMap = new HashMap<>();

    private ValueState<DataVal> m_maxDifValPerMinState;
    private ValueState<DataVal> m_newMaxDifValPerMinState;
    private ValueState<ExceptionLog> m_lastDegreeState;
    private ListState<ExceptionLog> m_exceptDtListState;
    private ListState<ExceptionLog> m_dtlogList4CheckEngDownState;

    // 状态存储列表数据
    private ListState<ExceptionLog> datalogListState;
    // 配置参数
    private static final int maxBatchSize = 1000;
    private static final long maxWaitTimeMs = 60 * 1000 * 5L ; // 5分钟

    private HashMap<Map<String, Object>, Boolean> dtKey2LoggedMap = new HashMap<>();

    OutputTag<Tuple2<Map<String, Object>,DataVal>> euclideanDistanceTag;//欧式距离经验值标签
    public OutputTag<Tuple2<Map<String, Object>, DataVal>> getEuclideanDistanceTag() {
        return euclideanDistanceTag;
    }

    OutputTag<List<ModifiedLog>> modifyDatalogListTag;//补点、异常、换表等修正数据标签
    public OutputTag<List<ModifiedLog>> getModifyDatalogListTag() {
        return modifyDatalogListTag;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化状态
        m_lastDegreeState = getRuntimeContext().getState(
                new ValueStateDescriptor<>("batchLastDegreeState" + id, ExceptionLog.class));
        m_exceptDtListState = getRuntimeContext().getListState(
                new ListStateDescriptor<>("dtListState"  + id, ExceptionLog.class));
        m_maxDifValPerMinState = getRuntimeContext().getState(
                new ValueStateDescriptor<>("maxDifValState"  + id, DataVal.class));
        m_newMaxDifValPerMinState = getRuntimeContext().getState(
                new ValueStateDescriptor<>("newMaxDifValState"  + id, DataVal.class));
        m_dtlogList4CheckEngDownState = getRuntimeContext().getListState(
                new ListStateDescriptor<>("dtlogList4CheckEngDownState"  + id, ExceptionLog.class));

        // 初始化状态
        ListStateDescriptor<ExceptionLog> descriptor =
                new ListStateDescriptor<>("datalog-list"  + id, ExceptionLog.class);
        datalogListState = getRuntimeContext().getListState(descriptor);
    }

    public class RowCollectorAdapter {
        private final Collector<Row> collector;

        public RowCollectorAdapter(Collector<Row> collector) {
            this.collector = collector;
        }

        public void collectEnergyData(List<DifferenceLog> energyDataList) {
            if (energyDataList != null && !energyDataList.isEmpty()) {
                for (DifferenceLog energyData : energyDataList) {
                    collector.collect(convertEnergyDataToRow(energyData));
                }
            }
        }
        public void collectModifyDatalog(List<ModifiedLog> modifyDatalogList) {
            if (modifyDatalogList != null && !modifyDatalogList.isEmpty()) {
                for (ModifiedLog modifyDatalog : modifyDatalogList) {
                    collector.collect(convertModifyDatalogToRow(modifyDatalog));
                }
            }
        }

        public void close() {
            // 关闭底层的 collector
            collector.close();
        }

    }

    private ExceptionLog convertRowToExceptionLog(Row row) {
        // Row到ExceptionLog的转换逻辑,需要做类型转换
        Map<String, Object> key = new HashMap<>();
        for (Map.Entry<String, String> entry : groupByMap.entrySet()) {
            String field = entry.getKey();
            String fieldType = entry.getValue().toUpperCase();
            Object rawValue = row.getField(field);
            // 处理空值和类型转换
            switch (fieldType) {
                case "LONG":
                    key.put(field, rawValue == null ? 0L : ((Number) rawValue).longValue());
                    break;
                case "INT":
                case "INTEGER":
                    key.put(field, rawValue == null ? 0 : ((Number) rawValue).intValue());
                    break;
                case "SHORT":
                    key.put(field, rawValue == null ? (short) 0 : ((Number) rawValue).shortValue());
                    break;
                case "BYTE":
                    key.put(field, rawValue == null ? (byte) 0 : ((Number) rawValue).byteValue());
                    break;
                case "FLOAT":
                    key.put(field, rawValue == null ? 0.0f : ((Number) rawValue).floatValue());
                    break;
                case "DOUBLE":
                    key.put(field, rawValue == null ? null : ((Number) rawValue).doubleValue());
                    break;
                case "STRING":
                    key.put(field, rawValue == null ? "" : rawValue.toString());
                    break;
                case "BOOLEAN":
                    key.put(field, rawValue == null ? false : (Boolean) rawValue);
                    break;
                case "NULL":
                    key.put(field, null);
                    break;
                default:
                    key.put(field, rawValue == null ? "" : rawValue.toString());
                    break;
            }
        }

        // 时间和值的字段转换
        SYSTEMTIME time = convertTimestampToSystemTime(((Number)row.getField("tm")).longValue());
        double value = ((Number)row.getField("val")).doubleValue();
        byte status = ((Number)row.getField("status")).byteValue();

        DatalogVal val = new DatalogVal(time, value, status);
        return new ExceptionLog(key, val);
    }

    private Row convertEnergyDataToRow(DifferenceLog energyData) {
        Row row = Row.withNames();

        for (Map.Entry<String, String> entry : groupByMap.entrySet()) {
            String field = entry.getKey();
            row.setField(row2TableMap.get(field), energyData.getExceptionLog().getKey().get(field));
        }

        row.setField(table2RowMap.get("tm"), convertSystemTimeToTimestamp(energyData.getExceptionLog().getDtVal().getTm()));
        row.setField(table2RowMap.get("val"), energyData.getPriorDtVal().getVal());
        row.setField(table2RowMap.get("status"), energyData.getExceptionLog().getDtVal().getStatus());

        row.setField("diffVal" + id, energyData.getExceptionLog().getDtVal().getVal());
        row.setField("priorTm" + id, convertSystemTimeToTimestamp(energyData.getPriorDtVal().getTm()));
        row.setField("priorVal"+ id, energyData.getPriorDtVal().getVal());
        row.setField("priorStatus"+ id, energyData.getPriorDtVal().getStatus());
        row.setField("latterTm"+ id, convertSystemTimeToTimestamp(energyData.getLatterDtVal().getTm()));
        row.setField("latterVal"+ id, energyData.getLatterDtVal().getVal());
        row.setField("latterStatus"+ id, energyData.getLatterDtVal().getStatus());

        // 设置行类型为INSERT
        row.setKind(RowKind.INSERT);

        return row;
    }

    private Row convertModifyDatalogToRow(ModifiedLog modifyDatalog) {
        Row row = Row.withNames();

        for (Map.Entry<String, String> entry : groupByMap.entrySet()) {
            String field = entry.getKey();
            row.setField(row2TableMap.get(field), modifyDatalog.getExceptionLog().getKey().get(field));
        }

        row.setField(table2RowMap.get("tm"), convertSystemTimeToTimestamp(modifyDatalog.getExceptionLog().getDtVal().getTm()));
        row.setField(table2RowMap.get("val"), modifyDatalog.getExceptionLog().getDtVal().getVal());
        row.setField(table2RowMap.get("status"), modifyDatalog.getExceptionLog().getDtVal().getStatus());

        row.setField("oldVal"+ id, modifyDatalog.getOldVal());

        row.setKind(RowKind.INSERT);
        return row;
    }

    @Override
    public void processElement(Row row, Context context, Collector<Row> collector) throws Exception {
        // 从Row转换为ExceptionLog
        ExceptionLog datalog = convertRowToExceptionLog(row);

        // 添加到状态中
        datalogListState.add(datalog);

        // 获取当前元素的处理时间
        long currentTime = context.timerService().currentProcessingTime();

        // 注册5分钟后的定时器（如果还没有注册过）
        // 使用状态来检查是否已经注册了定时器
        context.timerService().registerProcessingTimeTimer(currentTime + maxWaitTimeMs);

        // 检查是否达到了批处理的大小
        List<ExceptionLog> datalogs = (List<ExceptionLog>) datalogListState.get();
//        for (ExceptionLog log : datalogListState.get()) {
//            datalogs.add(log);
//        }

        if (datalogs.size() >= maxBatchSize) {
            // 处理数据
            processExceptionLog(datalogs, collector);
            // 清空状态
            datalogListState.clear();
            // 取消定时器（可选，取决于您的具体需求）
            // context.timerService().deleteProcessingTimeTimer(currentTime + maxWaitTimeMs);
        }
    }

    public List<ExceptionLog> filterAndSortDataLogs(List<ExceptionLog> datalogs) {
        // 获取当前时间
        SYSTEMTIME currentTime = convertTimestampToSystemTime(System.currentTimeMillis()); // 假设有获取当前时间的方法

        // 创建新列表存储过滤后的结果
        List<ExceptionLog> filteredLogs = new ArrayList<>();

        // 过滤掉超过当前时间的数据，并处理dataID条件
        for (ExceptionLog log : datalogs) {
            // 检查时间条件
            if (log.getDtVal() != null && log.getDtVal().getTm() != null) {
                // 检查dataID条件：如果key中有dataID，则只保留dataID在differenceDataIds列表中的记录
                Map<String, Object> key = log.getKey();
                boolean keepLog = true;
                if (key.containsKey("dataID")) {
                    Object dataID = key.get("dataID");
                    String differenceDataIds = FbpProperties.instance().getDifferenceDataIds();

                    // 如果differenceDataIds不为空
                    if (differenceDataIds != null && !differenceDataIds.isEmpty() && dataID != null) {
                        // 将differenceDataIds分割成数组
                        String[] dataIDArray = differenceDataIds.split(",");
                        boolean found = false;

                        // 检查当前dataID是否在允许的列表中
                        for (String allowedID : dataIDArray) {
                            if (allowedID.trim().equals(dataID.toString())) {
                                found = true;
                                break;
                            }
                        }

                        // 如果没找到匹配的ID，则过滤掉
                        if (!found) {
                            keepLog = false;
                        }
                    } else {
                        // 如果differenceDataIds为空，默认不保留（或者根据你的业务逻辑调整）
                        keepLog = false;
                    }
                }
                // 如果通过dataID检查并且时间不超过当前时间，则保留记录
                if (keepLog && convertSystemTimeToTimestamp(log.getDtVal().getTm()) <= convertSystemTimeToTimestamp(currentTime)) {
                    filteredLogs.add(log);
                }
            }
        }

        // 根据时间排序
        filteredLogs.sort(Comparator.comparing(log -> convertSystemTimeToTimestamp(log.getDtVal().getTm())));

        return filteredLogs;
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<Row> out) throws Exception {
        // 定时器触发，处理积累的数据
        List<ExceptionLog> datalogs = (List<ExceptionLog>) datalogListState.get();

        if (!datalogs.isEmpty()) {
            // 处理数据
            processExceptionLog(datalogs, out);
            // 清空状态
            datalogListState.clear();
        }
    }

    private void processExceptionLog(List<ExceptionLog> datalogs, Collector<Row> collector) throws Exception {
        datalogs = filterAndSortDataLogs(datalogs);

        if (datalogs == null || datalogs.isEmpty()) {
            return;
        }

        // 创建Row收集器适配器
        RowCollectorAdapter rowCollector = new RowCollectorAdapter(collector);

        // 处理第一个电度值并返回处理到的idx序号
        IntByReference idxRef = new IntByReference(0);
        ExceptionLog lastDegree = handleFirstDatalog(idxRef, datalogs, rowCollector, dataExceptPara);
        if (lastDegree == null) {
            return;
        }
        Integer idx = idxRef.getValue();

        // 把用来判断后续点下降的缓存数据合并到datalog前面
        datalogs = mergeFromCheckEngDownList(datalogs);

        // 获取状态数据
        List<ExceptionLog> exceptDtList = Lists.newArrayList(m_exceptDtListState.get().iterator());
        List<ExceptionLog> dtlogList4CheckEngDown = new ArrayList<>();
        int num4CheckEngDown = getNum4CheckEngDown(dataExceptPara);
        List<DifferenceLog> energyDataList = new ArrayList<>();
        List<ModifiedLog> modifyDtList = new ArrayList<>();

        // 循环处理数据点
        lastDegree = processDataPoints(idx, datalogs, lastDegree, exceptDtList,
                dtlogList4CheckEngDown, energyDataList, modifyDtList, num4CheckEngDown,
                rowCollector, dataExceptPara);

        // 更新状态
        updateStatesAndOutput(exceptDtList, dtlogList4CheckEngDown, lastDegree, modifyDtList,
                energyDataList, collector);
        log.info("本批次结束之后，最新lastDegree:{}", m_lastDegreeState.value().toString());
        log.info("本批次结束之后，最新m_maxDifValPerMinState:{}", m_maxDifValPerMinState.value().toString());
        log.info("本批次结束之后，最新m_newMaxDifValPerMinState:{}", m_newMaxDifValPerMinState.value().toString());
    }

    private ExceptionLog processDataPoints(int startIdx, List<ExceptionLog> datalogs,
                                           ExceptionLog lastDegree, List<ExceptionLog> exceptDtList,
                                           List<ExceptionLog> dtlogList4CheckEngDown, List<DifferenceLog> energyDataList,
                                           List<ModifiedLog> modifyDtList, int num4CheckEngDown,
                                           RowCollectorAdapter rowCollector, DataExceptPara dataExceptPara) throws Exception {

        int dtSize = datalogs.size();
        for (int idx = startIdx; idx < dtSize; ++idx) {
            ExceptionLog curDt = datalogs.get(idx);

            // 处理基础时序校验
            if (!checkTimeAsc(lastDegree, curDt, exceptDtList)) {
                continue;
            }

            // 处理特殊状态数据
            ExceptionLog processedDegree = processSpecialStatus(lastDegree, curDt, exceptDtList,
                    dtlogList4CheckEngDown, energyDataList, modifyDtList,
                    rowCollector, dataExceptPara);
            if (processedDegree != null) {
                lastDegree = processedDegree;
                continue;
            }

            // 处理能耗下降检查
            if (isDatalog4CheckEngDown(idx, dtSize, dtlogList4CheckEngDown, curDt, num4CheckEngDown)) {
                continue;
            }

            // 处理数据校验和异常
            lastDegree = processDataValidation(lastDegree, curDt, idx, datalogs,
                    exceptDtList, energyDataList, modifyDtList,
                    rowCollector, dataExceptPara);
        }

        return lastDegree;
    }

    // 处理特殊状态数据
    private ExceptionLog processSpecialStatus(ExceptionLog lastDegree, ExceptionLog curDt,
                                              List<ExceptionLog> exceptDtList, List<ExceptionLog> dtlogList4CheckEngDown,
                                              List<DifferenceLog> energyDataList, List<ModifiedLog> modifyDtList,
                                              RowCollectorAdapter rowCollector, DataExceptPara dataExceptPara) throws Exception {

        // 处理手动设置的数据
        if (curDt.getDtVal().getStatus() == ConstDef.STATUS_MANUAL_SET_VAL || curDt.getDtVal().getStatus() == ConstDef.STATUS_MANUAL_CHANGE_METER) {
            ExceptionLog result = handleManualSetData(lastDegree, curDt, exceptDtList,
                    dtlogList4CheckEngDown, rowCollector, modifyDtList,
                    energyDataList, dataExceptPara);
            energyDataList.clear();
            return result;
        }

        // 处理手动日能耗数据
        if (lastDegree.getDtVal().getStatus() == ConstDef.STATUS_MANUAL_SET_DAY_ENG) {
            if (isDatalogInRange(curDt, dataExceptPara)) {
                List<ModifiedLog> linearCompensationList = new ArrayList<>();
                energyDataList = handleLineAddPoint(lastDegree, curDt, 0, energyDataList,
                        rowCollector, linearCompensationList, dataExceptPara);
                return curDt;
            }
            return lastDegree;
        }

        return null; // 表示没有特殊状态处理
    }

    // 处理数据校验和异常
    private ExceptionLog processDataValidation(ExceptionLog lastDegree, ExceptionLog curDt,
                                               int idx, List<ExceptionLog> datalogs, List<ExceptionLog> exceptDtList,
                                               List<DifferenceLog> energyDataList, List<ModifiedLog> modifyDtList,
                                               RowCollectorAdapter rowCollector, DataExceptPara dataExceptPara) throws Exception {

        double difVal = checkDatalogValid(lastDegree, curDt, modifyDtList, dataExceptPara);
        if (difVal >= 0) {
            return processValidData(lastDegree, curDt, idx, datalogs, difVal,
                    exceptDtList, energyDataList, modifyDtList,
                    rowCollector, dataExceptPara);
        } else {
            return processInvalidData(lastDegree, curDt, difVal, exceptDtList,
                    energyDataList, modifyDtList, rowCollector, dataExceptPara);
        }
    }

    // 处理有效数据
    private ExceptionLog processValidData(ExceptionLog lastDegree, ExceptionLog curDt,
                                          int idx, List<ExceptionLog> datalogs, double difVal,
                                          List<ExceptionLog> exceptDtList, List<DifferenceLog> energyDataList,
                                          List<ModifiedLog> modifyDtList, RowCollectorAdapter rowCollector,
                                          DataExceptPara dataExceptPara) throws Exception {

        // 检查后续点下降情况
        difVal = checkDatalogDownByAfterVal(difVal, lastDegree, curDt, idx, datalogs,
                modifyDtList, dataExceptPara);

        // 处理缺点补点
        List<ModifiedLog> linearCompensationList = new ArrayList<>();
        energyDataList = handleLineAddPoint(lastDegree, curDt, difVal, energyDataList,
                rowCollector, linearCompensationList, dataExceptPara);

        // 标记异常状态
        markExceptStatus(linearCompensationList, exceptDtList, modifyDtList);
        exceptDtList.clear();
        return curDt;
    }

    // 处理无效数据
    private ExceptionLog processInvalidData(ExceptionLog lastDegree, ExceptionLog curDt,
                                            double difVal, List<ExceptionLog> exceptDtList,
                                            List<DifferenceLog> energyDataList, List<ModifiedLog> modifyDtList,
                                            RowCollectorAdapter rowCollector, DataExceptPara dataExceptPara) throws Exception {

        curDt.getDtVal().setStatus((byte)(-difVal));
        exceptDtList.add(curDt);

        // 处理超出缓存限制的异常点
        if (exceptDtList.size() >= dataExceptPara.getCacheDtlogNum()) {
            lastDegree  = processExcessExceptPoints(lastDegree, exceptDtList, modifyDtList, energyDataList,
                    rowCollector, dataExceptPara);
            return processRemainingExceptPoints(lastDegree, exceptDtList, modifyDtList,
                    energyDataList, rowCollector, dataExceptPara);
        }

        return lastDegree;
    }


    // 处理超出缓存限制的异常点
    private ExceptionLog processExcessExceptPoints(ExceptionLog lastDegree, List<ExceptionLog> exceptDtList,
                                           List<ModifiedLog> modifyDtList, List<DifferenceLog> energyDataList,
                                           RowCollectorAdapter collector, DataExceptPara dataExceptPara) throws Exception {

        ExceptionLog firstExceptDt = exceptDtList.get(0);
        firstExceptDt.getDtVal().setStatus(ConstDef.STATUS_AUTO_CHANGE_METER);
        modifyDtList.add(new ModifiedLog(firstExceptDt, null));
        log.error("此点按换表处理：" + firstExceptDt + "，上个点=" + lastDegree);

        energyDataList = handleLineAddPoint(lastDegree, firstExceptDt, 0, energyDataList,
                collector, modifyDtList, dataExceptPara);
        lastDegree = firstExceptDt;
        return lastDegree;
    }

    // 处理剩余异常点
    private ExceptionLog processRemainingExceptPoints(ExceptionLog lastDegree, List<ExceptionLog> exceptDtList,
                                                      List<ModifiedLog> modifyDtList, List<DifferenceLog> energyDataList,
                                                      RowCollectorAdapter collector, DataExceptPara dataExceptPara) throws Exception {

        int newExceptPos = 1;
        for (int exceptIdx = 1; exceptIdx < exceptDtList.size(); ++exceptIdx) {
            ExceptionLog exceptCurDt = exceptDtList.get(exceptIdx);
            double exceptDifVal = checkDatalogValid(lastDegree, exceptCurDt,  modifyDtList, dataExceptPara);

            if (exceptDifVal >= 0) {
                List<ModifiedLog> linearCompensationList = new ArrayList<>();
                energyDataList = handleLineAddPoint(lastDegree, exceptCurDt, exceptDifVal,
                        energyDataList, collector, linearCompensationList, dataExceptPara);
                markExceptStatus(linearCompensationList, exceptDtList, modifyDtList);
                lastDegree = exceptCurDt;
                newExceptPos = exceptIdx + 1;
            }
        }

        // 更新异常点列表
        List<ExceptionLog> newExceptDtList = new ArrayList<>();
        for (int exceptIdx = newExceptPos; exceptIdx < exceptDtList.size(); ++exceptIdx) {
            newExceptDtList.add(exceptDtList.get(exceptIdx));
        }
        exceptDtList.clear();
        exceptDtList.addAll(newExceptDtList);

        return lastDegree;
    }

    // 更新状态并输出结果
    private void updateStatesAndOutput(List<ExceptionLog> exceptDtList, List<ExceptionLog> dtlogList4CheckEngDown,
                                       ExceptionLog lastDegree, List<ModifiedLog> modifyDtList, List<DifferenceLog> energyDataList,
                                       Collector<Row> collector) throws Exception {

        // 输出修改数据
        /*if (!modifyDtList.isEmpty()) {
            context.output(modifyDatalogListTag, modifyDtList);
        }*/

        // 更新状态
        m_exceptDtListState.update(exceptDtList);
        m_dtlogList4CheckEngDownState.update(dtlogList4CheckEngDown);
        m_lastDegreeState.update(lastDegree);

        if (!energyDataList.isEmpty()) {
            // 使用 RowCollectorAdapter 转换并输出结果
            RowCollectorAdapter rowCollector = new RowCollectorAdapter(collector);
            rowCollector.collectEnergyData(energyDataList);
        }
    }

    ExceptionLog handleManualSetData(ExceptionLog lastDegree, ExceptionLog curDtlog, List<ExceptionLog> exceptDtList, List<ExceptionLog> dtlogList4CheckEngDown,
                                     RowCollectorAdapter collector, List<ModifiedLog> modifyDtList,
                                     List<DifferenceLog> energyDataList, DataExceptPara dataExceptPara) throws Exception {
        //把手动值之前缓存的exceptDtList和dtlogList4CheckEngDown定时记录先处理
        lastDegree = handleExceptDtList(lastDegree, exceptDtList, dtlogList4CheckEngDown,  collector, modifyDtList,  energyDataList, dataExceptPara);

        double difVal = 0;
        byte status = curDtlog.getDtVal().getStatus();
        if (status==ConstDef.STATUS_MANUAL_CHANGE_METER || status==ConstDef.STATUS_MANUAL_SET_VAL || status==ConstDef.STATUS_MANUAL_SET_DAY_ENG){
            if (status==ConstDef.STATUS_MANUAL_SET_VAL){//手动修正数据，直接不校验，认为是正常数据
                if (lastDegree.getDtVal().getVal()!=ConstDef.NA){
                    difVal =  curDtlog.getDtVal().getVal()-lastDegree.getDtVal().getVal();
                }
                if (difVal<0) {//数据回退
                    log.debug("数值回退，尝试翻转处理，上个点="+lastDegree+"，当前点="+curDtlog);
                    difVal+=dataExceptPara.getTurnOVerEng();//先按翻转处理后判断是否突变
                }
                log.info("手动修正数据,上个点="+lastDegree+"，当前点="+curDtlog+ " 差值="+difVal);
            }
            else if (status==ConstDef.STATUS_MANUAL_CHANGE_METER){//手动换表数据，直接返回能耗0
                log.info("手动换表数据,上个点="+lastDegree+"，当前点="+curDtlog);
            }
            //手动点之间补的15分钟间隔数据，不写数据库提高性能
            List<ModifiedLog> linearCompensationList = new ArrayList<>();
            energyDataList = handleLineAddPoint(lastDegree, curDtlog, difVal,  energyDataList, collector, linearCompensationList, dataExceptPara);
            lastDegree = curDtlog;

            if (status==ConstDef.STATUS_MANUAL_SET_DAY_ENG){//手动差值处理
                SYSTEMTIME afterTm = new SYSTEMTIME(curDtlog.getDtVal().getTm());
                afterTm.AddDifDay(1);
                DatalogVal dtVal = new DatalogVal(afterTm, ConstDef.NA, curDtlog.getDtVal().getStatus());
                ExceptionLog afterDt = new ExceptionLog(curDtlog.getKey(),dtVal);
                energyDataList = handleLineAddPoint(curDtlog, afterDt, curDtlog.getDtVal().getVal(), energyDataList, collector, linearCompensationList, dataExceptPara);
                lastDegree = afterDt;
            }
        }
        else {
            log.info("未支持数据,上个点="+lastDegree+"，当前点="+curDtlog);
        }

        if (energyDataList.size()>0){
            collector.collectEnergyData(energyDataList);
        }
        return lastDegree;
    }

    //日能耗差值，把之前的表码差值缓存先处理
    ExceptionLog handleExceptDtList(ExceptionLog lastDegree, List<ExceptionLog> exceptDtList, List<ExceptionLog> dtlogList4CheckEngDown,
                                    RowCollectorAdapter collector,
                                    List<ModifiedLog> modifyDtList, List<DifferenceLog> energyDataList, DataExceptPara dataExceptPara) throws Exception {
        //处理异常数据缓存数据
        exceptDtList.addAll(dtlogList4CheckEngDown);
        if (exceptDtList.size()<1) return lastDegree;
        int newExceptPos = 0;
        while (true){
            //把剩下的异常点接着比对判断
            for(int exceptIdx=newExceptPos;exceptIdx<exceptDtList.size();++exceptIdx){
                ExceptionLog exceptCurDt = exceptDtList.get(exceptIdx);
                double exceptDifVal = checkDatalogValid(lastDegree, exceptCurDt, modifyDtList, dataExceptPara);
                if (exceptDifVal>=0){
                    //判断缺点处理，只补整15分钟的点提高性能，因为分时只细分到15分钟
                    List<ModifiedLog> linearCompensationList = new ArrayList<>();
                    energyDataList = handleLineAddPoint(lastDegree, exceptCurDt, exceptDifVal,  energyDataList, collector, linearCompensationList, dataExceptPara);
                    markExceptStatus(linearCompensationList, exceptDtList, modifyDtList);
                    lastDegree = exceptCurDt;
                    newExceptPos=exceptIdx+1;
                }
            }
            if (newExceptPos>=exceptDtList.size()) break;
            //超过设置的异常点缓存个数，则取出异常缓存的第一个点按换表处理，换表则能耗处理成0
            ExceptionLog changeMeterDt = exceptDtList.get(newExceptPos);
            changeMeterDt.getDtVal().setStatus(ConstDef.STATUS_AUTO_CHANGE_METER);
            modifyDtList.add(new ModifiedLog(changeMeterDt, null));
            log.error("处理能耗差值，此点按换表处理："+changeMeterDt+"，上个点="+lastDegree);
            //此处都是补的点，无需标记异常状态
            energyDataList = handleLineAddPoint(lastDegree, changeMeterDt, 0,  energyDataList, collector, modifyDtList, dataExceptPara);
            lastDegree = changeMeterDt;
            ++newExceptPos;
        }
        exceptDtList.clear();
        dtlogList4CheckEngDown.clear();
        return lastDegree;
    }

    //处理第一个电度值并返回处理到的idx序号
    ExceptionLog handleFirstDatalog(IntByReference idxRef, List<ExceptionLog> datalogs, RowCollectorAdapter collector, DataExceptPara dataExceptPara) throws IOException, SQLException,InterruptedException{
        ExceptionLog lastDegree = m_lastDegreeState.value();//取上一次电度值
        /*lastDegree = HandleNodeRecalc(datalogs.get(0).getDtKey(), lastDegree);
        lastDegree = HandlePrjRecalc(datalogs.get(0).getDtKey(), lastDegree);*/
        if (lastDegree!=null) return lastDegree;

        int idx = idxRef.getValue();
        ExceptionLog curDtlog = datalogs.get(idx);
        if (curDtlog.getDtVal().getStatus()==ConstDef.STATUS_MANUAL_SET_DAY_ENG){//第一个点是差值的情况。
            SYSTEMTIME afterTm = new SYSTEMTIME(curDtlog.getDtVal().getTm());
            afterTm.AddDifDay(1);
            DatalogVal dtVal = new DatalogVal(afterTm, ConstDef.NA, curDtlog.getDtVal().getStatus());
            ExceptionLog afterDt = new ExceptionLog(curDtlog.getKey(),dtVal);
            List<ModifiedLog> linearCompensationList = new ArrayList<>();
            List<DifferenceLog> energyDataList = new ArrayList<>();
            energyDataList = handleLineAddPoint(curDtlog, afterDt, curDtlog.getDtVal().getVal(), energyDataList, collector, linearCompensationList, dataExceptPara);
            if (energyDataList.size()>0){
                collector.collectEnergyData(energyDataList);
            }

            ++idx;
            idxRef.setValue(idx);

            DataVal maxDifValPerMin = m_maxDifValPerMinState.value();
            if (maxDifValPerMin==null){
                if (dataExceptPara.isbFixedEmpirical()){//固定经验值
                    maxDifValPerMin = new DataVal(curDtlog.getDtVal().getTm(), dataExceptPara.getMaxDifEng());
                    m_newMaxDifValPerMinState.update(new DataVal(curDtlog.getDtVal().getTm(), dataExceptPara.getMaxDifEng()));
                }else {//非固定经验值
                    // 查询经验值表获取昨天的经验值
                    // maxDifValPerMin = trySelectYesterdayEuclideanDistance(lastDegree);
                /*if (maxDifValPerMin.getTm()==null){//如果数据库查询不到则取配置初值
                    //先把每分钟最大增量值置成配置的最大增量绝对值，后续判断会逐渐加大，并且按日回归
                    maxDifValPerMin = new DataVal(lastDegree.getDtVal().getTm(), dataExceptPara.getMaxDifEng());
                }*/
                    maxDifValPerMin = new DataVal(curDtlog.getDtVal().getTm(), dataExceptPara.getMaxDifEng());
                    m_newMaxDifValPerMinState.update(new DataVal(curDtlog.getDtVal().getTm(), 0));
                }
                m_maxDifValPerMinState.update(maxDifValPerMin);
                //把新经验值输出到数据库
                //context.output(euclideanDistanceTag, new Tuple2<>(lastDegree.getDtKey(),maxDifValPerMin));
            }

            return afterDt;
        }


        boolean bInRange = false;//校验首个定时记录是否在量程内
        for (;idx<datalogs.size();++idx) {
            bInRange = isDatalogInRange(datalogs.get(idx), dataExceptPara);
            if (bInRange) break;
        }
        if (!bInRange) return null;//所有点都不在量程内

        lastDegree = datalogs.get(idx);
        log.info("Flirst point " + lastDegree);
        ++idx;
        idxRef.setValue(idx);

        DataVal maxDifValPerMin = m_maxDifValPerMinState.value();
        if (maxDifValPerMin==null){
            if (dataExceptPara.isbFixedEmpirical()){//固定经验值
                maxDifValPerMin = new DataVal(lastDegree.getDtVal().getTm(), dataExceptPara.getMaxDifEng());
                m_newMaxDifValPerMinState.update(new DataVal(lastDegree.getDtVal().getTm(), dataExceptPara.getMaxDifEng()));
            }else {//非固定经验值
                // 查询经验值表获取昨天的经验值
                // maxDifValPerMin = trySelectYesterdayEuclideanDistance(lastDegree);
                /*if (maxDifValPerMin.getTm()==null){//如果数据库查询不到则取配置初值
                    //先把每分钟最大增量值置成配置的最大增量绝对值，后续判断会逐渐加大，并且按日回归
                    maxDifValPerMin = new DataVal(lastDegree.getDtVal().getTm(), dataExceptPara.getMaxDifEng());
                }*/
                maxDifValPerMin = new DataVal(lastDegree.getDtVal().getTm(), dataExceptPara.getMaxDifEng());
                m_newMaxDifValPerMinState.update(new DataVal(lastDegree.getDtVal().getTm(), 0));
            }
            m_maxDifValPerMinState.update(maxDifValPerMin);
            //把新经验值输出到数据库
            //context.output(euclideanDistanceTag, new Tuple2<>(lastDegree.getDtKey(),maxDifValPerMin));
        }
        return lastDegree;
    }

    List<ExceptionLog> mergeFromCheckEngDownList(List<ExceptionLog> datalogs) throws Exception {//把用来判断后续点下降的缓存数据合并到datalog前面
        List<ExceptionLog> dtlogList4CheckEngDown = Lists.newArrayList(m_dtlogList4CheckEngDownState.get().iterator());
        if (dtlogList4CheckEngDown.size()>0){//用来判断后续点是否下降，缓存的list 先合并到新记录前面
            dtlogList4CheckEngDown.addAll(datalogs);
            datalogs = dtlogList4CheckEngDown;
        }
        return datalogs;
    }

    boolean checkTimeAsc(ExceptionLog lastDegree, ExceptionLog curDt, List<ExceptionLog> exceptDtList){//校验时标是否升序
        SYSTEMTIME lastTm = lastDegree.getDtVal().getTm();
        if (exceptDtList.size()>0){//如果异常列表有值，则异常列表的值时间更大
            lastTm = exceptDtList.get(exceptDtList.size()-1).getDtVal().getTm();
        }
        SYSTEMTIME newTm = curDt.getDtVal().getTm();
        //判断时间是否升序
        int ret = TimeFunc.CompareSysTmUntilMin(newTm, lastTm);
        if (ret<0) //时间回退，丢弃
        {
            //log.error("Time is not ascending order failed, skip this point " + curDt + ", last point is " + lastDegree);
            return false;
        }
        if (ret==0){
            //时间相等的情况，且能耗差值情况
            if (lastDegree.getDtVal().getStatus()!=ConstDef.STATUS_MANUAL_SET_DAY_ENG && curDt.getDtVal().getStatus()!=ConstDef.STATUS_MANUAL_SET_DAY_ENG){
                return false;
            }
        }
        return true;
    }

    int getNum4CheckEngDown(DataExceptPara dataExceptPara){
        /*double recalcTm = BatchDatalogReaderSource.GetRecalcTm(EnergyProperties.instance().isbDataCacheHttp());
        if (recalcTm>0){
            return dataExceptPara.getDtlogNum4CheckEngDown();
        }*/
        return 0;
    }

    boolean isDatalog4CheckEngDown(int idx, int dtSize, List<ExceptionLog> dtlogList4CheckEngDown, ExceptionLog curDt, int num4CheckEngDown){
        // 主要目的是确保在数据流的末尾有足够的点来进行能耗下降检查。如果剩余的点不够，就将当前点缓存起来，可能留待下一批数据到来时再进行检查。
        if (idx+num4CheckEngDown>=dtSize){//需要缓存下来
            dtlogList4CheckEngDown.add(curDt);
            return true;
        }
        return false;
    }

    boolean isDatalogInRange(ExceptionLog dtlog, DataExceptPara dataExceptPara){
        if (dtlog.getDtVal().getVal()<0 || //表码值是负数
                dtlog.getDtVal().getVal()> dataExceptPara.getTurnOVerEng())//上个表码值大于翻转值
        {
            if(needLog(dtlog.getKey())){
                log.error("表码值{}是负数或大于翻转值{}，按能耗0处理，当前点={}",
                        dtlog.getDtVal().getVal(),dataExceptPara.getTurnOVerEng(), dtlog);
            }
            return false;//超出量程范围异常
        }
        return true;
    }

    //返回值>=0表示正常能耗值，返回值<0则返回异常状态
    double checkDatalogValid(ExceptionLog lastDtlog, ExceptionLog curDtlog,
                             List<ModifiedLog> modifyDtList, DataExceptPara dataExceptPara) throws IOException {

        // 检查数值范围
        if(!exceptionHandler.checkInRange(lastDtlog, curDtlog, modifyDtList, dataExceptPara.getTurnOVerEng())){
            return 0;
        }

        double difVal = curDtlog.getDtVal().getVal() - lastDtlog.getDtVal().getVal();

        difVal = exceptionHandler.checkFallback(difVal, dataExceptPara.getTurnOVerEng());

        difVal = exceptionHandler.checkDifValLimit(difVal, dataExceptPara.getLimitDifEng());

        // 如果返回负值，表示验证不通过
        if (difVal < 0) {
            if(needLog(curDtlog.getKey())) {
                log.error("两点能耗为负({})超过限值({})，按异常数据处理。上个点={}，当前点={}" ,
                        difVal, dataExceptPara.getLimitDifEng(), lastDtlog, curDtlog);
            }
            return -ConstDef.STATUS_EXCEPT_LIMIT;
        }

        // 处理欧氏距离相关逻辑
        return exceptionHandler.checkEuclideanDistance(lastDtlog, curDtlog, difVal, dataExceptPara,
                m_maxDifValPerMinState, m_newMaxDifValPerMinState, dtKey2LoggedMap);
    }

    //重算的时候，根据后续缓存的点的下降来判断当前点是否合理，后续点<当前点且后续点>=lastDegree则表示下降想异常，把当前点按0能耗处理
    double checkDatalogDownByAfterVal(double difVal, ExceptionLog lastDegree, ExceptionLog curDt, int idx, List<ExceptionLog> datalogs, List<ModifiedLog> modifyDtList, DataExceptPara dataExceptPara){
        if (difVal>0){
            int dtSize = datalogs.size();
            for(int checkIdx=1;checkIdx<=dataExceptPara.getDtlogNum4CheckEngDown();++checkIdx){
                if (idx+checkIdx>=dtSize) break;//超过datalogs数组下标则直接跳出
                ExceptionLog afterDt = datalogs.get(idx+checkIdx);
                if (afterDt.getDtVal().getStatus()>=ConstDef.STATUS_MANUAL_SET_VAL) break;//后值是手动值，则不用继续判断
                if (afterDt.getDtVal().getVal()>=curDt.getDtVal().getVal()) continue;//值没有下降
                //下降的情况
                if (afterDt.getDtVal().getVal()<lastDegree.getDtVal().getVal()) continue;// 比lastDegree也下降，此下降点当异常点忽略
                //当前异常的上升点，则按0能耗处理
                log.info("后续点({})下降，所以当前点能耗增量按0处理，上个点={}，当前点={}",afterDt,lastDegree,curDt);
                double oldVal = curDt.getDtVal().getVal();
                curDt.getDtVal().setVal(lastDegree.getDtVal().getVal());
                curDt.getDtVal().setStatus(ConstDef.STATUS_EXCEPT_DOWN);
                modifyDtList.add(new ModifiedLog(curDt, oldVal));
                return 0;
            }
        }
        return difVal;
    }

    //处理线性补点，只补15分钟间隔的点，因为后面业务分时只到15分钟，并把补点数据输出到modifydatalogList
    private List<DifferenceLog> handleLineAddPoint(ExceptionLog lastDtlog, ExceptionLog curDtlog, double difVal, List<DifferenceLog> energyDataList,
                                                   RowCollectorAdapter collector, List<ModifiedLog> linearCompensationList, DataExceptPara dataExceptPara){
        while (true){
            SYSTEMTIME lastDtTm = lastDtlog.getDtVal().getTm();
            SYSTEMTIME curDtTm = curDtlog.getDtVal().getTm();
            long difMin =TimeFunc.CalcDifMin(lastDtTm, curDtTm);
            int minRainder = 15-lastDtTm.wMinute%15;//距离下一个15分钟点还有多少分钟
            if(minRainder>=difMin) {//未错过15分钟点，不需要补
                DatalogVal energyVal = new DatalogVal(curDtTm, difVal, curDtlog.getDtVal().getStatus());
                energyDataList.add(new DifferenceLog(new ExceptionLog(curDtlog.getKey(), energyVal), lastDtlog.getDtVal(), curDtlog.getDtVal()));
                if (lastDtTm.getwDay()!=curDtTm.getwDay()){//跨天则先推送数据
                    collector.collectEnergyData(energyDataList);
                    // energyDataList = new ArrayList<>();//不采用clear清空，是防止collect出去的list元素被clear掉
                    energyDataList.clear();
                }
                return energyDataList;
            }
            //以下需要线性补15分钟时刻点
            SYSTEMTIME midDtTm = TimeFunc.UnixTime2SysTmWithoutSec(TimeFunc.SystemTime2UnixTime(lastDtTm)+minRainder*60000);
            double midDifVal = difVal/difMin*minRainder;//中间差值
            double midVal = (lastDtlog.getDtVal().getVal()+midDifVal)%dataExceptPara.getTurnOVerEng();//中间表码值考虑翻转赋值
            DatalogVal midDtVal = new DatalogVal(midDtTm, midVal, ConstDef.STATUS_LINEAR_COMPENSATION);//线性补点状态
            ExceptionLog midDtlog = new ExceptionLog(curDtlog.getKey(),midDtVal);
            log.debug("补缺失点"+midDtlog);
            linearCompensationList.add(new ModifiedLog(midDtlog,null));
            DatalogVal energyVal = new DatalogVal(midDtTm, midDifVal, curDtlog.getDtVal().getStatus());
            energyDataList.add(new DifferenceLog(new ExceptionLog(curDtlog.getKey(), energyVal), lastDtlog.getDtVal(), midDtlog.getDtVal()));
            if (lastDtTm.getwDay()!=midDtTm.getwDay()){//跨天则先推送数据
                collector.collectEnergyData(energyDataList);
                // energyDataList = new ArrayList<>();//不采用clear清空，是防止collect出去的list元素被clear掉
                energyDataList.clear();
            }

            lastDtlog = midDtlog;
            difVal -= midDifVal;
        }
    }

    //标记异常状态，补点modifydatalogList列表中元素如果在异常点列表中，则把对应的元素status状态置对应的异常，并输出modifyDtList
    void markExceptStatus(List<ModifiedLog> linearCompensationList, List<ExceptionLog> exceptDtList, List<ModifiedLog> modifyDtList){
        if (linearCompensationList.size()==0) return;
        int i=0;
        int j=0;
        while (i<linearCompensationList.size() && j<exceptDtList.size()){
            ModifiedLog linearDatalog = linearCompensationList.get(i);
            DatalogVal linearVal = linearDatalog.getExceptionLog().getDtVal();
            DatalogVal exceptVal = exceptDtList.get(j).getDtVal();
            int result = TimeFunc.CompareSysTmUntilMin(linearVal.getTm(), exceptVal.getTm());
            if (result==0) {//相等，把补点状态置程具体的异常状态
                linearDatalog.setOldVal(exceptVal.getVal());
                linearVal.setStatus(exceptVal.getStatus());
                ++i;
                ++j;
                continue;
            }
            if (result<0) {//linear Time<except Time
                ++i;
                continue;
            }
            //linear Time>except Time
            ++j;
        }
        modifyDtList.addAll(linearCompensationList);
    }


    private boolean needLog(Map<String, Object> dtKey){
        Boolean logged = dtKey2LoggedMap.get(dtKey);
        if (logged==null||logged==false) {
            dtKey2LoggedMap.put(dtKey, true);
            return true;
        }
        return false;
    }

}