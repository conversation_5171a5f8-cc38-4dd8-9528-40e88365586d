package com.cet.engine.plugin.base.flink.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.cet.engine.common.CheckResult;
import com.cet.engine.common.Const;
import com.cet.engine.enums.ColumnType;
import com.cet.engine.file.util.writer.FileUtil;
import com.cet.engine.flink.FlinkEnvironment;
import com.cet.engine.flink.stream.FlinkStreamSink;

import com.cet.engine.utils.DataUtil;
import com.cet.fbp.common.util.JdbcUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.JdbcSink;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.*;
import java.util.*;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CommonJdbcSink extends RichOutputFormat<Row> implements FlinkStreamSink<Row, Row> {
    private static final Logger log = LoggerFactory.getLogger(CommonJdbcSink.class);

    private JSONObject config;
    private JSONObject schemaInfo;

    private Map<String,String> fields = new HashMap<>();

    private List<Map<String, Object>> propertyList = new ArrayList<>();

    private Map<String,String> relation = new HashMap<>();

    private String insertTable;

    private String dataBaseURL = "*******************************************************************************************************";
    private String user = "postgres";
    private String password = "cet2024nbg?";

    private String processMethod;
    private String conflictColumns;

    @Override
    public void setConfig(JSONObject config) {
        this.config = config;
    }

    @Override
    public JSONObject getConfig() {
        return config;
    }

    @Override
    public CheckResult checkConfig() {
        return new CheckResult(true,"");
    }

    @Override
    public void prepare(FlinkEnvironment env) {
        try {
            user = JdbcUtil.instance().getUsername();
            password = JdbcUtil.instance().getPassword();
            dataBaseURL = JdbcUtil.instance().getUrl();

            if (user == null || user.trim().isEmpty() ||
                    password == null || password.trim().isEmpty() ||
                    dataBaseURL == null || dataBaseURL.trim().isEmpty()) {
                throw new IllegalStateException("Database connection parameters are missing or empty");
            }

            String dataBaseURLPostfix = "?useUnicode=true&characterEncoding=utf-8&useSSL=false";
            dataBaseURL = dataBaseURL + dataBaseURLPostfix;
        } catch (Exception e) {
            String errorMessage = "Failed to get database connection parameters: " + e.getMessage();
            log.error(errorMessage, e);
            throw new RuntimeException(errorMessage, e);
        }

        JSONArray jsonArray = config.getJSONArray(Const.PROPERTY_LIST);
        propertyList = jsonArrayToListMap(jsonArray);
        conflictColumns = config.getString(Const.CONFLICT_COLUMNS);
        processMethod = config.getString(Const.REPEAT_PROCESS_METHOD);
        String fieldsArray = config.getString(Const.FIELDS_ARRAY);
        schemaInfo = JSONObject.parseObject(config.getString(Const.FIELDS_ARRAY), Feature.OrderedField);
        String modelAssociationJson = config.getString(Const.MODEL_ASSOCIATION);
        insertTable = config.getString(Const.SYNC_MODEL);
        try {
            // 解析成 JSONArray
            JSONArray modelAssociation = JSON.parseArray(modelAssociationJson);

            // 遍历 modelAssociation 处理每一条数据
            for (int i = 0; i < modelAssociation.size(); i++) {
                // 获取每个条目，转换为 JSONObject
                JSONObject entry = modelAssociation.getJSONObject(i);

                String selectedAField = entry.getString(Const.SELECTED_A_FIELD);
                String selectedBField = entry.getString(Const.SELECTED_B_FIELD);

                // 如果 selectedBField 为空字符串，则跳过该条数据
                if (selectedBField.isEmpty()) {
                    continue;
                }

                // 将 selectedAField 和 selectedBField 添加到 Map
                relation.put(selectedAField, selectedBField);
            }

        } catch (Exception e) {
            log.error("解析成 JSONArray 失败",e);
        }

        // 去掉大括号
        fieldsArray = fieldsArray.replaceAll("[{}\"]", "").trim();

        // 按逗号分割
        String[] split = fieldsArray.split(",");

        // 遍历拆分后的数组，按冒号分割键值对并放入Map
        for (String s : split) {
            String[] fieldKV = s.trim().split(":");
            if (fieldKV.length == 2) {
                fields.put(fieldKV[0].trim(), fieldKV[1].trim());
            }
        }
    }

    @Override
    public void configure(Configuration parameters) {

    }

    @Override
    public Integer getParallelism() {

        // 默认为1,
        return config.getInteger(Const.PARALLELISM) == null ? 1 : config.getInteger(Const.PARALLELISM);
    }

    @Override
    public String getName() {

        return StringUtils.isEmpty(config.getString(Const.ID)) ? config.getString(Const.PLUGIN_NAME) : config.getString(Const.ID);
    }

    @Override
    public void open(int taskNumber, int numTasks) throws IOException {

    }

    @Override
    public void writeRecord(Row record) throws IOException {
        System.out.println(record);
    }

    @Override
    public void close() throws IOException {

    }

    @Override
    public void outputStream(FlinkEnvironment env, DataStream<Row> dataStream) throws SQLException {
        String[] element = new String[fields.keySet().size()];
        String[] symbol = new String[fields.keySet().size()];
        for (int i = 0; i < fields.keySet().size(); i++) {
            // 如果 relation 中存在该 key，就更新 element[i] 为 relation 中的 value
            if (relation.containsKey(Lists.newArrayList(fields.keySet().iterator()).get(i))) {
                element[i] = relation.get(Lists.newArrayList(fields.keySet().iterator()).get(i));
            }else {
                element[i] = Lists.newArrayList(fields.keySet().iterator()).get(i);
            }
            symbol[i] = "?";
        }
        String elements = String.join(",", element);
        String symbols = String.join(",", symbol);

        String insertSql = "";


        if ("insert".equalsIgnoreCase(processMethod)) {
            // 基本的插入操作
            insertSql = String.format("INSERT INTO %s (%s) VALUES (%s)", insertTable, elements, symbols);
        } else if ("upsert".equalsIgnoreCase(processMethod)) {
            // 支持 upsert (on conflict do update) 语法
            // 获取数据库连接，执行 SQL 查询
            String updateSet = Arrays.stream(element)
                    // 用 EXCLUDED 来引用插入时的值
                    .map(e -> e + " = EXCLUDED." + e)
                    // 用逗号连接
                    .collect(Collectors.joining(", "));
            insertSql = String.format(
                    "INSERT INTO %s (%s) VALUES (%s) ON CONFLICT (%s) DO UPDATE SET %s",
                    insertTable, elements, symbols, conflictColumns, updateSet);

        } else if ("ignore".equalsIgnoreCase(processMethod)) {
            // 支持 ignore (on constraint do nothing) 语法
            insertSql = String.format("INSERT INTO %s (%s) VALUES (%s) ON CONSTRAINT DO NOTHING", insertTable, elements, symbols);
        } else {
            // 默认的插入行为
            insertSql = String.format("INSERT INTO %s (%s) VALUES (%s)", insertTable, elements, symbols);
        }

        // 添加日志记录 SQL 语句
        log.info("JDBC Sink SQL: {}", insertSql);

        dataStream = validateAndFilter(dataStream, fields, relation);

        // 使用 JdbcSink 执行插入
        SinkFunction<Row> sink = JdbcSink.sink(
                insertSql,
                new CommonSinkBuilder(),
                new JdbcExecutionOptions.Builder()
                        .withBatchSize(config.getInteger(Const.BATCH_SIZE))
                        .withBatchIntervalMs(config.getInteger(Const.BATCH_TIME_MS))
                        .withMaxRetries(3)
                        .build(),
                new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                        .withDriverName("org.postgresql.Driver")
                        .withUrl(dataBaseURL)
                        .withUsername(user)
                        .withPassword(password)
                        .build());

        dataStream.addSink(sink)
            .uid(getName())
            .name(getName())
            .setParallelism(getParallelism());

        resultSinkToFile(dataStream, env, insertTable);
    }

    public List<Map<String, Object>> jsonArrayToListMap(JSONArray jsonArray) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Map<String, Object> map = new HashMap<>();
            for (String key : jsonObject.keySet()) {
                // 直接获取值，保持原始类型
                map.put(key, jsonObject.get(key));
            }
            resultList.add(map);
        }
        return resultList;
    }

    /**
     * 验证并过滤数据流中的行数据
     *
     * @param dataStream 输入的数据流
     * @param fields 字段集合
     * @param relation 字段与关系的映射
     * @return 过滤后的数据流
     */
    public DataStream<Row> validateAndFilter(DataStream<Row> dataStream, Map<String, String> fields,
                                             Map<String, String> relation) {
        // 获取所有字段的集合并转为数组
        String[] keySet = fields.keySet().toArray(new String[0]);

        // 创建一个用于存储有效行的列表并返回一个流
        return dataStream.filter(row -> {
            // 标记当前行是否有效
            boolean isValid = true;

            if (Arrays.asList(keySet).contains("status")) {
                Byte status = row.getFieldAs("status");
                if (!((status == null) || ((status & 0x01) == 0x01))) {
                    return false;
                }
            }

            // 对每个字段进行验证
            for (String key : keySet) {
                Object value = row.getFieldAs(key); // 获取字段对应的值
                String modelKey = relation.get(key); // 获取字段在关系中的键
                Map<String, Object> metadata = getFieldMetadata(modelKey); // 获取字段的元数据

                // 验证字段的有效性
                isValid &= validateField(

                        row, key, value,
                        metadata.get(Const.NULLABLE) instanceof Boolean ? (Boolean) metadata.get(Const.NULLABLE) : null,
                        metadata.get(Const.RANGE_MAX) instanceof Number ? ((Number) metadata.get(Const.RANGE_MAX)).doubleValue() : null,
                        metadata.get(Const.RANGE_MIN) instanceof Number ? ((Number) metadata.get(Const.RANGE_MIN)).doubleValue() : null,
                        metadata.get(Const.MAX_LENGTH) instanceof Number ? ((Number) metadata.get(Const.MAX_LENGTH)).intValue() : null,
                        metadata.get(Const.MIN_LENGTH) instanceof Number ? ((Number) metadata.get(Const.MIN_LENGTH)).intValue() : null,
                        (String) metadata.get(Const.FORMAT_CONSTRAINT)
                );
            }

            // 返回当前行是否有效
            return isValid;
        });
    }

    public Map<String, Object> getFieldMetadata(String propertyLabel) {
        for (Map<String, Object> property : propertyList) {
            if (propertyLabel.equals(property.get("propertyLabel"))) {
                return property;
            }
        }
        return null;
    }

    public boolean validateField(Row row, String key, Object value,
                                 Boolean nullable, Double rangeMax, Double rangeMin,
                                 Integer maxLength, Integer minLength, String formatConstraint) {
        // 检查是否为空
        if (value == null) {
            if (!nullable) {
                log.warn("记录: " + row + " 字段: " + key + " 值: " + value + " 不符合为空约束: " + nullable);
                return false;
            } else {
                return true;
            }
        }

        // 对不同类型的值进行校验
        if (value instanceof Number) {
            return validateNumber(row, key, (Number) value, rangeMin, rangeMax);
        } else if (value instanceof String) {
            return validateString(row, key, (String) value, minLength, maxLength, formatConstraint);
        } else {
            // Boolean值不需要额外校验
            return value instanceof Boolean;
        }

        // 如果是其他类型，返回false
    }

    private boolean validateNumber(Row row, String key, Number value, Double rangeMin, Double rangeMax) {
        double doubleValue = value.doubleValue();

        if (rangeMin != null && doubleValue < rangeMin) {
            log.warn("记录: " + row + " 字段: " + key + " 值: " + value + " 不符合最小值约束: " + rangeMin);
            return false;
        }

        if (rangeMax != null && doubleValue > rangeMax) {
            log.warn("记录: " + row + " 字段: " + key + " 值: " + value + " 不符合最大值约束: " + rangeMax);
            return false;
        }

        return true;
    }

    private boolean validateString(Row row, String key, String value, Integer minLength, Integer maxLength, String formatConstraint) {
        if (minLength != null && value.length() < minLength) {
            log.warn("记录: " + row + " 字段: " + key + " 值: " + value + " 不符合最小长度约束: " + minLength);
            return false;
        }

        if (maxLength != null && value.length() > maxLength) {
            log.warn("记录: " + row + " 字段: " + key + " 值: " + value + " 不符合最大长度约束: " + maxLength);
            return false;
        }

        if (formatConstraint != null && !formatConstraint.isEmpty()) {
            log.warn("记录: " + row + " 字段: " + key + " 值: " + value + " 不符合正则约束: " + formatConstraint);
            return Pattern.matches(formatConstraint, value);
        }

        return true;
    }

    class CommonSinkBuilder implements JdbcStatementBuilder<Row> {
        @Override
        public void accept(PreparedStatement ps, Row row) {
            Set<String> keySet = fields.keySet();
            AtomicInteger i = new AtomicInteger(1);
            keySet.forEach((
                    key -> {
                        try {
                            ColumnType columnType = ColumnType.fromString(fields.get(key).toUpperCase());
                            switch (columnType) {
                                case LONG:
                                    ps.setLong(i.get(), row.getFieldAs(key) == null ? 0L : ((Number) row.getFieldAs(key)).longValue());
                                    i.incrementAndGet();
                                    break;

                                case INT:
                                case INTEGER:
                                    ps.setInt(i.get(), row.getFieldAs(key) == null ? 0 : ((Number) row.getFieldAs(key)).intValue());
                                    i.incrementAndGet();
                                    break;

                                case SHORT:
                                    ps.setShort(i.get(), row.getFieldAs(key) == null ? (short) 0 : ((Number) row.getFieldAs(key)).shortValue());
                                    i.incrementAndGet();
                                    break;

                                case BYTE:
                                    ps.setByte(i.get(), row.getFieldAs(key) == null ? (byte) 0 : ((Number) row.getFieldAs(key)).byteValue());
                                    i.incrementAndGet();
                                    break;

                                case FLOAT:
                                    ps.setFloat(i.get(), row.getFieldAs(key) == null ? 0.0f : ((Number) row.getFieldAs(key)).floatValue());
                                    i.incrementAndGet();
                                    break;

                                case DOUBLE:
                                    if (row.getFieldAs(key) == null) {
                                        ps.setNull(i.get(), java.sql.Types.DOUBLE);
                                    } else {
                                        ps.setDouble(i.get(), Double.parseDouble(DataUtil.roundToDecimalPlaces(row.getFieldAs(key), 6)));
                                    }
                                    i.incrementAndGet();
                                    break;

                                case NULL:
                                    ps.setNull(i.get(), Types.NULL);
                                    i.incrementAndGet();
                                    break;

                                case BOOLEAN:
                                    ps.setBoolean(i.get(), row.getFieldAs(key) != null && (Boolean) row.getFieldAs(key));
                                    i.incrementAndGet();
                                    break;

                                default:
                                    ps.setString(i.get(), row.getFieldAs(key) == null ? "" : row.getFieldAs(key).toString());
                                    i.incrementAndGet();
                                    break;
                            }
                        } catch (Exception e) {
                            log.error("数据类型转换失败", e);
                        }
                    }
            ));

        }
    }

    void resultSinkToFile(DataStream<Row> dataStream, FlinkEnvironment env,String insertTable) {
        JSONObject tableFields = env.getInsertTables().get(insertTable);
        String[] writeFields = null;
        String[] writeTypes = null;
        if (tableFields != null) {
            JSONArray fieldsArray = tableFields.getJSONArray("writeFields");
            JSONArray typesArray = tableFields.getJSONArray("writeTypes");

            writeFields = new String[fieldsArray.size()];
            writeTypes = new String[fieldsArray.size()];
            for (int i = 0; i < typesArray.size(); i++) {
                writeFields[i] = (String) fieldsArray.get(i);
                writeTypes[i] = (String) typesArray.get(i);
            }
        } else {
            JSONObject rs = new JSONObject();
            writeFields = new String[schemaInfo.size()];
            writeTypes = new String[schemaInfo.size()];
            int idx = 0;
            for (String field: schemaInfo.keySet()) {
                writeFields[idx] = relation.get(field) != null ? relation.get(field): field;
                writeTypes[idx] = fields.get(field);
                idx++;
            }
            rs.fluentPut("writeFields",writeFields)
                    .fluentPut("writeTypes",writeTypes);
            env.getInsertTables().put(insertTable,rs);
        }
        String[] finalWriteFields = writeFields;
        dataStream = dataStream.flatMap(new RichFlatMapFunction<Row, Row>() {

            @Override
            public void flatMap(Row element, Collector<Row> collector) throws Exception {
                Row rs = Row.withNames();
                for (String field : finalWriteFields) {
                    rs.setField(field, element.getFieldAs(findKeysByValue(relation,field)));
                }
                collector.collect(rs);
            }
        });
        FileUtil.sinkFile(dataStream, "",insertTable,config.getString(Const.ID), writeFields, writeTypes);
    }

    public static String findKeysByValue(Map<String, String> map, String value) {
        String key = null;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            // 用 Objects.equals 可防止 null 值比对出错
            if (Objects.equals(entry.getValue(), value)) {
                key = entry.getKey();
            }
        }
        return key;
    }
}