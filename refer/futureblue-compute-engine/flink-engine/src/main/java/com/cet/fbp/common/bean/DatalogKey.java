package com.cet.fbp.common.bean;

import com.cet.fbp.common.frame.FbpProperties;

import java.io.Serializable;
import java.util.Objects;

public class DatalogKey implements Serializable {
    private int devID;
    private int dataID;
    private short logicalID;
    private short dataTypeID;

    public DatalogKey() {
    }

    public DatalogKey(int devID, int dataID, short logicalID, short dataTypeID) {
        this.devID = devID;
        this.dataID = dataID;
        this.logicalID = logicalID;
        this.dataTypeID = dataTypeID;
    }

    public int getDevID() {
        return devID;
    }

    public void setDevID(int devID) {
        this.devID = devID;
    }

    public int getDataID() {
        return dataID;
    }

    public void setDataID(int dataID) {
        this.dataID = dataID;
    }

    public short getLogicalID() {
        return logicalID;
    }

    public void setLogicalID(short logicalID) {
        this.logicalID = logicalID;
    }

    public short getDataTypeID() {
        return dataTypeID;
    }

    public void setDataTypeID(short dataTypeID) {
        this.dataTypeID = dataTypeID;
    }

    public String toRedisStr(){
        return devID +
                FbpProperties.instance().redisKeySeparator + dataID +
                FbpProperties.instance().redisKeySeparator + logicalID;
    }

//    public String toRedisStr(){
//        return "{\"deviceid\":" + devID +
//                ",\"dataid\":" + dataID +
//                ",\"logicalid\":" + logicalID +
//                '}';
//    }

    @Override
    public String toString() {
        return "DatalogKey{" +
                "devID=" + devID +
                ", dataID=" + dataID +
                ", logicalID=" + logicalID +
                ", dataTypeID=" + dataTypeID +
                '}';
    }

    public String toSimpleString() {
        return "devID=" + devID +
                ", dataID=" + dataID +
                ", logicalID=" + logicalID +
                ", dataTypeID=" + dataTypeID;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DatalogKey)) return false;
        DatalogKey that = (DatalogKey) o;
        return devID == that.devID && dataID == that.dataID && logicalID == that.logicalID && dataTypeID == that.dataTypeID;
    }

    @Override
    public int hashCode() {
        return Objects.hash(devID, dataID, logicalID, dataTypeID);
    }


}

