package com.cet.engine.file;

import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.junit.Test;

public class FileReaderTest {

    @Test
    public void test1() throws Exception {
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
//        // 必须启用检查点
//        env.enableCheckpointing(50000).getCheckpointConfig().setCheckpointStorage("file:\\data\\checkpoint");;
//        env.setParallelism(1);
//
////        env.addSource(new TailFSocket("E:\\opt\\CET\\Common\\flink\\outputfile\\aa\\T13Input\\2025-04-09--14")).print("数据读取");
//        env.addSource(new FileReader("energy","T1")).print("数据读取");
//
//        env.execute("Real-time File Tail");
    }
}
