package com.cet.engine.file;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.junit.jupiter.api.Test;

public class FileUtilTest {

    @Test
    public void test1() throws Exception {

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
//
//        DataStream<EnergyDTO> tsSource = env.fromElements(new EnergyDTO("a", 1741572600000L, 5));
//        tsSource.print("输出");
//        String pathStr = "/opt/CET/Common/flink/output";
//        FileUtil.sinkFile(tsSource, pathStr,"energy",7);
//        env.execute("test1");

    }
}
