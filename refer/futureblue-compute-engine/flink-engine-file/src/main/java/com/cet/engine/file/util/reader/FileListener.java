package com.cet.engine.file.util.reader;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cet.engine.file.util.common.Const;
import org.apache.commons.io.monitor.FileAlterationListenerAdaptor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.RandomAccessFile;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class FileListener extends FileAlterationListenerAdaptor {
    private static final Logger log = LoggerFactory.getLogger(FileListener.class);
    private Map<String,Long> offsetState;
    private SourceFunction.SourceContext<Row> ctx;
    private JSONObject metaJson;
    public FileListener(SourceFunction.SourceContext<Row> ctx, Map<String,Long> offsetState, JSONObject metaJson) {
        this.ctx = ctx;
        this.offsetState = offsetState;
        this.metaJson = metaJson;
    }

    public FileListener() {}

    @Override
    public void onStart(FileAlterationObserver observer) {
        super.onStart(observer);

        // 递归当前文件夹下的所有文件，读取初始数据
        File dir = observer.getDirectory();
        processFilesRecursively(dir);
    }

    @Override
    public void onDirectoryCreate(File directory) {
    }

    @Override
    public void onDirectoryChange(File directory) {
    }

    @Override
    public void onDirectoryDelete(File directory) {
        String compressedPath = directory.getAbsolutePath();
        offsetState.remove(extractFinalFileName(compressedPath));
    }

    @Override
    public void onFileCreate(File file) {
        readFile(file);
    }

    @Override
    public void onFileChange(File file) {
        readFile(file);
    }

    @Override
    public void onFileDelete(File file) {
        String compressedPath = file.getAbsolutePath();
        offsetState.remove(extractFinalFileName(compressedPath));
    }

    @Override
    public void onStop(FileAlterationObserver observer) {
        super.onStop(observer);
    }

    private void processFilesRecursively(File dir) {
        if (dir == null || !dir.exists()) return;
        File[] files = dir.listFiles();
        if (files == null) return;
        for (File file : files) {
            if (file.isFile()) {
                readFile(file);
            } else if (file.isDirectory()) {
                processFilesRecursively(file);
            }
        }
    }

    private void readFile(File file) {
        try {
            String compressedPath = file.getAbsolutePath();
            JSONArray fields = metaJson.getJSONArray(Const.fields);
            JSONArray types = metaJson.getJSONArray(Const.types);
            String filePath = extractFinalFileName(compressedPath);
            offsetState.putIfAbsent(filePath, 0L);
            long lastPosition = offsetState.get(filePath);
            try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
                // 从上次位置继续读
                raf.seek(lastPosition);
                String line;
                while ((line = raf.readLine()) != null) {
                    String[] values = line.split(",");
                    if (values.length == fields.size()) {
                        Row row = Row.withNames();
                        for (int i = 0; i < fields.size(); i++) {
                            if ("int".equalsIgnoreCase(types.get(i).toString())) {
                                row.setField(fields.get(i).toString(), Integer.parseInt(values[i]));
                            }
                            if ("long".equalsIgnoreCase(types.get(i).toString())) {
                                row.setField(fields.get(i).toString(), Long.parseLong(values[i]));
                            }
                            if ("double".equalsIgnoreCase(types.get(i).toString())) {
                                row.setField(fields.get(i).toString(), Double.parseDouble(values[i]));
                            }
                            if ("byte".equalsIgnoreCase(types.get(i).toString())) {
                                row.setField(fields.get(i).toString(), Byte.parseByte(values[i]));
                            }
                            if ("short".equalsIgnoreCase(types.get(i).toString())) {
                                row.setField(fields.get(i).toString(), Short.parseShort(values[i]));
                            }
                            if ("string".equalsIgnoreCase(types.get(i).toString())) {
                                row.setField(fields.get(i).toString(), values[i]);
                            }
                        }
                        ctx.collect(row);
                    }
                }
                // 更新文件位置
                offsetState.put(filePath, raf.getFilePointer());
            }
        } catch (Exception e) {
            // 处理异常
            log.error(e.toString());
        }
    }

    public static String extractFinalFileName(String src) {
        // src: .part-0-0.inprogress.XXXXX 或 part-0-0.inprogress.XXXXX
        int idx = src.indexOf(".inprogress.");
        if (idx < 0) {
            return src;
        }
        String s = src.startsWith(".") ? src.substring(1, idx) : src.substring(0, idx);
        return s.replace(".","");
    }
}
