package com.cet.engine.file.util.timer;

import com.cet.engine.file.util.common.Const;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 过期 File 清理
 * @author: AI
 */
public class ScheduledExecutor {
    private static final Logger log = LoggerFactory.getLogger(ScheduledExecutor.class);

    public void scheduledFileCleaner() {
        // 创建一个ScheduledExecutorService实例，用于调度任务
        ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);

        // 创建一个Runnable任务，该任务将每隔一段时间执行
        Runnable task = () -> {
            System.out.println("执行任务: " + System.currentTimeMillis());
            try {
                cleanFile();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };

        // 使用ScheduledExecutorService的scheduleAtFixedRate方法来安排任务
        // 第一个参数是Runnable对象，第二个参数是首次执行的延迟时间，
        // 第三个参数是连续执行之间的周期时间，第四个参数是时间单位
        executorService.scheduleAtFixedRate(task, 0, 1, TimeUnit.HOURS);

        // 让主线程等待足够长的时间，以便观察ScheduledExecutorService的工作
        // 在实际应用中，这里可能会有其他的逻辑处理，而不是简单地等待
//        try {
//            System.out.println("ScheduledExecutorService已启动，等待任务执行...");
//            // 等待10秒钟以便观察任务执行情况
//            Thread.sleep(10000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        // 关闭ScheduledExecutorService，这将停止所有正在执行的任务并关闭执行器
//        executorService.shutdown();
//        System.out.println("ScheduledExecutorService已关闭");
    }

    public void cleanFile() throws Exception {
        log.info("定时任务开始！ " + System.currentTimeMillis());
        // 计算3天前的时间戳（毫秒）
        long now = System.currentTimeMillis();
        long expireBefore = now - TimeUnit.DAYS.toMillis(3);

        Path root = Paths.get(Const.FIRST_PATH);
        if (!Files.exists(root) || !Files.isDirectory(root)) {
            log.info("Aggregation root path does not exist: " + Const.FIRST_PATH);
            return;
        }

        Files.walkFileTree(root, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                if (DateFormatDetector.extractFirstDate(file.toString()) == null) {
                    return FileVisitResult.CONTINUE;
                }
                long fileTime = attrs.creationTime().toMillis();
                if (fileTime < expireBefore) {
                    try {
                        Files.delete(file);
                        log.info("Deleted file: " + file);
                    } catch (Exception e) {
                        log.error("Failed to delete file: " + file + ", reason: " + e.getMessage());
                    }
                }
                return FileVisitResult.CONTINUE;
            }

            // 可选: 删除空目录
            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                if (!dir.equals(root)) { // 不删根目录
                    try (DirectoryStream<Path> entries = Files.newDirectoryStream(dir)) {
                        if (!entries.iterator().hasNext()) {
                            Files.delete(dir);
                            log.info("Deleted empty directory: " + dir);
                        }
                    } catch (Exception e) {
                        // Ignore
                    }
                }
                return FileVisitResult.CONTINUE;
            }
        });
    }

}
