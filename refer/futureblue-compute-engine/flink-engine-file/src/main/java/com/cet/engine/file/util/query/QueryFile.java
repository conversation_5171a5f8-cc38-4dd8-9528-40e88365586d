package com.cet.engine.file.util.query;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cet.engine.file.util.common.Const;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryFile extends Singleton{

    public static QueryFile instance() {
        return (QueryFile)Singleton.getInstance(QueryFile.class);
    }

    public List<ModelPropertyDef> getModelPropertyDefList(String modelLabel) throws IOException {
        String path = String.format("%s/%s%s", Const.FIRST_PATH, modelLabel, Const.META_PATH);
        List<ModelPropertyDef> result = new ArrayList<>();

        if (!Files.exists(Paths.get(path))) {
            // 文件不存在：创建并写入内容
            log.info("文件 {} 不存在!", path);
            return result;
        }

        List<String> lines = Files.readAllLines(Paths.get(path), StandardCharsets.UTF_8);
        StringBuilder content = new StringBuilder();
        for(String line : lines){
            content.append(line);
        }
        JSONObject parsed = JSONObject.parseObject(content.toString());

        if (parsed != null) {
            JSONArray fields = parsed.getJSONArray(Const.fields);
            JSONArray types = parsed.getJSONArray(Const.types);
            for (int i = 0; i < fields.size(); i++) {
                result.add(new ModelPropertyDef(fields.get(i).toString(), types.get(i).toString()));
            }
        }
        return result;
    }

    public JSONObject getModelList(String modelLabel) throws IOException {
        String path = String.format("%s/%s%s", Const.FIRST_PATH, modelLabel, Const.META_PATH);
        if (!Files.exists(Paths.get(path))) {
            // 文件不存在：创建并写入内容
            log.info("文件 {} 不存在!", path);
            return new JSONObject();
        }

        List<String> lines = Files.readAllLines(Paths.get(path), StandardCharsets.UTF_8);
        StringBuilder content = new StringBuilder();
        for(String line : lines){
            content.append(line);
        }

        return JSONObject.parseObject(content.toString());
    }
}
