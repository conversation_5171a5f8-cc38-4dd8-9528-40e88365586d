package com.cet.engine.file.util.timer;

import java.util.regex.*;

/**
 * <AUTHOR>
 */
public class DateFormatDetector {
    // 正则表达式，匹配yyyy-MM-dd格式
    private static final Pattern DATE_PATTERN = Pattern.compile("\\d{4}-\\d{2}-\\d{2}");

    // 检查字符串是否包含该日期格式
    public static boolean containsDate(String input) {
        return DATE_PATTERN.matcher(input).find();
    }

    // 可选：提取第一个符合格式的日期字符串
    public static String extractFirstDate(String input) {
        Matcher matcher = DATE_PATTERN.matcher(input);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }
}
