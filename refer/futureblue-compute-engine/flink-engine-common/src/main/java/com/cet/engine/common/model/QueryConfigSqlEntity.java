package com.cet.engine.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryConfigSqlEntity {
    /**
     * 主键 id
     */
    private long id;

    /**
     * flink 作业对应的任务 id
     */
    private String modelLabel;

    /**
     * flink 任务 job_name
     */
    private String linkedLabel;

    /**
     * 作业参数更新时间
     */
    private String sql;

    /**
     * flink 作业规则参数
     */
    private String notes;
    /**
     * 查询结果字段
     */
    private List<FieldDefinition> fields;
}
