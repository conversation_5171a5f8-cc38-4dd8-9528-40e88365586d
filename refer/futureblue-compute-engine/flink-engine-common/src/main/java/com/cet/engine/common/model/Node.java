package com.cet.engine.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class Node {
    private Long id;

    private String modelLabel;

    private String treeId;

    private String name;

    private List<Node> children = new ArrayList<>();
}
