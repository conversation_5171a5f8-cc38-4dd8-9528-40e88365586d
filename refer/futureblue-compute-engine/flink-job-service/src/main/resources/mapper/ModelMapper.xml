<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cet.flinkjobservice.dao.ModelDao">

	<!-- 定义 RuleInfo 映射 -->
	<resultMap id="EngineModel" type="cet.flinkjobservice.def.EngineModel">
		<result column="id" property="id"/>
		<result column="modellabel" property="modelLabel"/>
		<result column="linkedlabel" property="linkedLabel"/>
		<result column="sql" property="sql"/>
		<result column="notes" property="notes"/>
	</resultMap>



	<!-- 查询所有数据，操作 engine_base 表 -->
	<select id="selectAllModelLabel" resultMap="EngineModel">
		SELECT * FROM engine_model
	</select>

</mapper>
