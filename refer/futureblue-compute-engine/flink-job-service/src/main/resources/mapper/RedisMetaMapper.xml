<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cet.flinkjobservice.dao.RedisMetaDao">

    <!-- 定义 RedisMetaData 映射 -->
    <resultMap id="RedisMetaData" type="cet.flinkjobservice.def.RedisMetaData">
        <result column="id" property="id"/>
        <result column="modellabel" property="modelLabel"/>
        <result column="key" property="key"/>
        <result column="aggregationcycle" property="aggregationCycle"/>
        <result column="logtime" property="logTime"/>
        <result column="value" property="value"/>
    </resultMap>



    <!-- 查询所有数据，操作 model_redis_meta 表 -->
    <select id="getRedisMeta" resultMap="RedisMetaData">
        SELECT * FROM model_redis_meta WHERE modellabel = #{modelLabel}
    </select>

</mapper>
