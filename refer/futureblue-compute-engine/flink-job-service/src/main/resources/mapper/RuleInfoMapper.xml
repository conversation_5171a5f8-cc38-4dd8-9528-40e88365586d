<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cet.flinkjobservice.dao.RuleInfoDao">

	<!-- 定义 RuleInfo 映射 -->
	<resultMap id="RuleInfo" type="cet.flinkjobservice.def.RuleInfo">
		<result column="id" property="id"/>
		<result column="job_id" property="jobId"/>
		<result column="job_name" property="jobName"/>
		<result column="update_time" property="updateTime"/>
		<result column="argues" property="argues"/>
		<result column="layout" property="layout"/>
		<result column="alias" property="alias"/>
		<result column="is_delete" property="isDelete"/>
		<result column="status" property="status"/>
	</resultMap>

	<!-- 基础字段列表 -->
	<sql id="Base_List">
		t.id,
        t.job_id,
        t.job_name,
        t.update_time,
        t.argues,
        t.layout,
        t.alias,
        t.is_delete,
        t.status
	</sql>

	<!-- 插入操作，指向 engine_base 表 -->
	<insert id="addRuleInfo" parameterType="cet.flinkjobservice.def.RuleInfo" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO engine_base (
			job_id,
			job_name,
			update_time,
			argues,
			layout,
			alias,
			is_delete,
			status
		) VALUES (
			#{jobId},
			#{jobName},
			#{updateTime},
			#{argues}::jsonb,
			#{layout}::jsonb,
			#{alias},
			#{isDelete},
			#{status}
		);
	</insert>


	<!-- 通过 job_name 加载，操作 engine_base 表 -->
	<select id="loadByJobName" parameterType="java.util.HashMap" resultMap="RuleInfo">
		SELECT <include refid="Base_List" />
		FROM engine_base AS t
		WHERE t.job_name IN
		<foreach item="item" index="index" collection="nameList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 通过 rule_name 加载，操作 engine_base 表 -->
	<select id="loadByRuleName" parameterType="java.util.HashMap" resultMap="RuleInfo">
		SELECT <include refid="Base_List" />
		FROM engine_base AS t
		WHERE t.job_name = #{ruleName}
	</select>

	<!-- 通过 ID 加载，操作 engine_base 表 -->
	<select id="loadById" parameterType="long" resultMap="RuleInfo">
		SELECT * FROM engine_base
		WHERE id = #{id}
	</select>

	<!-- 获取全部规则信息，操作 engine_base 表 -->
	<select id="getRuleInfo" resultMap="RuleInfo">
		SELECT <include refid="Base_List" />
		FROM engine_base AS t
	</select>

	<!-- 获取标签信息，操作 engine_base 表 -->
	<select id="getLabel" parameterType="java.util.HashMap" resultType="java.lang.String">
		SELECT
			GROUP_CONCAT(alias)
		FROM
			engine_base
		WHERE
			FIND_IN_SET(job_name,
						(SELECT GROUP_CONCAT(job_name)
						 FROM engine_base
						 WHERE FIND_IN_SET(#{ruleName}, job_name) > 0
						   AND sub_task_size > 0)
			) > 0
		  AND job_name != #{ruleName}
	</select>

	<!-- 更新规则信息，操作 engine_base 表 -->
	<update id="updateRuleInfo" parameterType="cet.flinkjobservice.def.RuleInfo">
		UPDATE engine_base
		SET
		job_id = #{jobId},
		job_name = #{jobName},
		update_time = #{updateTime},
		argues = COALESCE(#{argues}::jsonb, argues),  <!-- 防止更新为 null -->
		layout = COALESCE(#{layout}::jsonb, layout),  <!-- 防止更新为 null -->
		alias = COALESCE(#{alias}, alias),           <!-- 防止更新为 null -->
		is_delete = COALESCE(#{isDelete}, is_delete),
		status = COALESCE(#{status}, status)
		WHERE id = #{id}
	</update>

	<!-- 查询所有数据，操作 engine_base 表 -->
	<select id="selectAll" resultMap="RuleInfo">
		SELECT * FROM engine_base
	</select>

	<!-- 查询所有数据并分表，操作 engine_base 表 -->
	<select id="selectAllWithPagination" resultMap="RuleInfo">
		SELECT *
		FROM engine_base
		WHERE is_delete = false
		ORDER BY id DESC
			LIMIT #{limit} OFFSET #{index}
	</select>

	<!-- 模糊查询数据并分表，操作 engine_base 表 -->
	<select id="selectAllWithPaginationAndAlias" resultMap="RuleInfo">
		SELECT *
		FROM engine_base
		WHERE is_delete = false
		  AND alias LIKE '%' || #{jobAlias} || '%'
		ORDER BY id DESC
			LIMIT #{limit} OFFSET #{index}
	</select>

	<select id="checkUniqueJobNameOrAlias" parameterType="cet.flinkjobservice.def.RuleInfo"  resultType="int">
		SELECT COUNT(*)
		FROM engine_base
		WHERE (job_name = #{jobName} OR alias = #{alias})
		  AND is_delete = false
	</select>

	<select id="checkUniqueJobName" parameterType="cet.flinkjobservice.def.RuleInfo"  resultType="int">
		SELECT COUNT(*)
		FROM engine_base
		WHERE job_name = #{jobName}
		  AND is_delete = false
	</select>

	<select id="checkUniqueAlias" parameterType="cet.flinkjobservice.def.RuleInfo"  resultType="int">
		SELECT COUNT(*)
		FROM engine_base
		WHERE alias = #{alias}
		  AND is_delete = false
	</select>

	<select id="countAllRules" parameterType="cet.flinkjobservice.def.RuleInfo" resultType="int">
		SELECT COUNT(*)
		FROM engine_base
		WHERE is_delete = false
		<if test="jobAlias != null and jobAlias != ''">
			AND alias LIKE '%' || #{jobAlias} || '%'
		</if>
	</select>

	<select id="countAllSubmittedRules" parameterType="cet.flinkjobservice.def.RuleInfo" resultType="int">
		SELECT COUNT(*)
		FROM engine_base
		WHERE is_delete = false
		  AND status = 'SUBMITTED'
	</select>


	<delete id="deleteAllMarkedAsDeleted" parameterType="cet.flinkjobservice.def.RuleInfo">
		DELETE FROM engine_base
		WHERE is_delete = true
		AND NOW() - INTERVAL '72 hour' > to_timestamp(update_time/1000);
	</delete>
</mapper>
