server:
  port: 8082
flink:
  #运行模式，目前支持 standalone 和 yarn-session，未设置默认是 standalone
  deployMode: standalone
  #yarn-web地址
  yarnUrl: http://************:8088
  #配置flink-api地址，如果是yarn-session模式则不用配置
  flinkApiUrl: http://************:8081
  #配置checkpoint清理计划
  checkpointCleanCron: 0 0 5 * * ?

spring:
  redis:
    host: ***********
    port: 26379
    database: 0
    password: Ceiec4567!
eureka:
  client:
    enabled: false
    serviceUrl:
      #配置服务中心(可配置多个,用逗号隔开)
      #defaultZone: http://************:1001/eureka/
      #defaultZone: http://************:1001/erueka/
      #defaultZone: http://***********:1001/eureka/
      #defaultZone: http://************:1001/eureka/
      defaultZone: http://************:1001//eureka/
    healthcheck:
      enabled: false
  instance:
    lease-renewal-interval-in-seconds: 15
    prefer-ip-address: true
    ip-address: ************
cet:
  base-service:
    model-service:
      name: "model-service"
      url: "127.0.0.1:8085"