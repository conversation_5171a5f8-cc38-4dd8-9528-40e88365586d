server:
  port: 18084
#spring:
#  profiles:
#    active: dev
#  application:
#    name: flink-job-service
#  redis:
#    database: ${SPRING_REDIS_DATABASE}
#    password: ${SPRING_REDIS_PASSWORD}
#    host: ${SPRING_REDIS_HOST}
#    port: ${SPRING_REDIS_PORT}
#logging:
#  file: ../log/${spring.application.name}.log
  #config: classpath:logback-boot.xml
flink:
  #运行模式，目前支持standalone和yarn-session，未设置默认是standalone
  deployMode: standalone
  #yarn-web地址,当deployMode设置为yarn-session时有效
  yarnUrl: http://node01:8088
  #配置flink-api地址，当deployMode设置为standalone时有效
  flinkApiUrl: http://localhost:8081
  #配置checkpoint清理计划
  checkpointCleanCron: 0 0 5 * * ?


mybatis:
  mapper-locations: classpath:mapper/*Mapper.xml

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    driver-class-name: org.postgresql.Driver
    password: cet2024nbg?
#    url: ************************************************************************************************************************************
    url: ***************************************************
    username: postgres
  sleuth:
    web:
      client:
        enabled: true
    sampler:
      probability: 1.0 # 将采样比例设置为 1.0，也就是全部都需要。默认是 0.1
  zipkin:
    base-url: http://localhost:9411/ # 指定了 Zipkin 服务器的地址
    enabled: false

#服务器发现注册配置
eureka:
  client:
    enabled: true
    serviceUrl:
      #配置服务中心(可配置多个,用逗号隔开)
      #defaultZone: http://************:1001/eureka/
      #defaultZone: http://************:1001/erueka/
      #defaultZone: http://***********:1001/eureka/
      #defaultZone: http://************:1001/eureka/
      defaultZone: http://************:1001/eureka/
  instance:
    lease-renewal-interval-in-seconds: 15
    prefer-ip-address: true
    #ip-address: ************
# 定时任务开启状态
task:
  scheduling: true

cet:
  base-service:
    model-service:
      name: "model-service"
      url: "************:8085"
