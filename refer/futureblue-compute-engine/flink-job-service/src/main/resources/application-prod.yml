server:
  port: 18084
  #spring:
  #  profiles:
  #    active: dev
  #  application:
  #    name: flink-job-service
  #  redis:
  #    database: ${SPRING_REDIS_DATABASE}
  #    password: ${SPRING_REDIS_PASSWORD}
  #    host: ${SPRING_REDIS_HOST}
  #    port: ${SPRING_REDIS_PORT}
  #logging:
  #  file: ../log/${spring.application.name}.log
  #config: classpath:logback-boot.xml
flink:
  #运行模式，目前支持standalone和yarn-session，未设置默认是standalone
  deployMode: standalone
  #yarn-web地址,当deployMode设置为yarn-session时有效
  yarnUrl: http://node01:8088
  #配置flink-api地址，当deployMode设置为standalone时有效
  flinkApiUrl: http://localhost:8081
  #配置checkpoint清理计划
  checkpointCleanCron: 0 0 5 * * ?


mybatis:
  mapper-locations: classpath:mapper/*Mapper.xml

spring:
  datasource:
    driver-class-name: ${SPRING_DRIVER_CLASS}
    password: ${SPRING_DB_PASSWORD}
    #    url: ************************************************************************************************************************************
    url: ${SPRING_DB_URL}
    username: ${SPRING_DB_USERNAME}

model:
  DataBaseURL: ${MODEL_DATABASE_URL}
  schemaName: ${MODEL_SCHEMA_NAME}
  serviceURL: ${MODEL_SERVICE_URL}

# 定时任务开启状态
task:
  scheduling: true

cet:
  logback:
    host: 127.0.0.1
    port: 4567