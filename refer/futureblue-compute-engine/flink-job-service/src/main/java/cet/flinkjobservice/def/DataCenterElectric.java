package cet.flinkjobservice.def;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DataCenterElectric {
    @ApiModelProperty(value = "设备id")
    private long deviceid;

    @ApiModelProperty(value = "数据id")
    private long dataid;

    @ApiModelProperty(value = "回路号id")
    private int logicalindex;

    @ApiModelProperty(value = "通道id")
    private long channelid;

    @ApiModelProperty(value = "厂站id")
    private long stationid;

    @ApiModelProperty(value = "物理量id")
    private long quantityobject_id;

    @ApiModelProperty(value = "管网设备id")
    private long objectid;

    @ApiModelProperty(value = "管网设备模型")
    private String objectlabel;

    @ApiModelProperty(value = "路径")
    private String path;

    @ApiModelProperty(value = "组织层级id")
    private long orgid;

    @ApiModelProperty(value = "组织层级标签")
    private String orglabel;
}
