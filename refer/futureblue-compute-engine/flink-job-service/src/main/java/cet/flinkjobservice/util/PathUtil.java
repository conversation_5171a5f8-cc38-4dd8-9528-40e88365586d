package cet.flinkjobservice.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 程序运行路径获取工具类
 * @author: zk
 */
@Slf4j
@Component
public class PathUtil {
    private final File jarF;

    public PathUtil(){
        ApplicationHome h = new ApplicationHome(getClass());
        this.jarF = h.getSource();
    }

    public String getRunDirectory() {
        return jarF.getParentFile().getParentFile().toString();
    }

    /**
     * 根据文件过滤信息，得到过滤的文件名称数组
     * @param directory 目录
     * @param filterRegex 文件过滤正则表达式
     * @return 返回过滤后的文件名称数组（纯文件名，不含目录）
     */
    public String[] filterFiles(String directory,String filterRegex) {
        File checkpointDir = new File(directory);
        return checkpointDir.list(new FilenameFilter() {
            private final Pattern pattern= Pattern.compile(filterRegex);
            @Override
            public boolean accept(File dir, String name) {
                return pattern.matcher(name).matches();
            }
        });
    }
    public void deleteDir(File dir) throws IOException {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < Objects.requireNonNull(children).length; i++) {
                deleteDir(new File(dir, children[i]));
            }
        }
        java.nio.file.Files.delete(Paths.get(dir.getPath()));
    }
}
