package cet.flinkjobservice.util;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.Objects;

public class FileUtils {

    public static String getPath(String jobId) {
        String directory = "/opt/CET/Common/flink/savepoint";
        String path = directory;
        String result = jobId.substring(0, 6);
        String filterRegex = String.format("-%s-", result);
        String[] files = readFile(directory, filterRegex);
        if (files != null && files.length > 0) {
            path = String.format("%s/%s/_metadata", path, files[0]);

        } else  {
            // 2.再找 checkpoint 文件夹进行文件匹配
            directory = "/opt/CET/Common/flink/checkpoint";
            path = directory;
            filterRegex = jobId;
            files = readFile(directory, filterRegex);
            if (files == null || files.length == 0) {
                return null;
            }
            path = path + "/" + files[0];
            File checkpointDir = new File(path);
            int max = 0;
            // 获取最大的 chk 文件目录
            for (String filePath : Objects.requireNonNull(checkpointDir.list())) {
                String chk = filePath.replaceAll("[^0-9]", "").trim();
                if (!chk.isEmpty() && Integer.parseInt(chk)>max)
                    max = Integer.parseInt(chk);
            }
            path = String.format("%s/chk-%s/_metadata", path, max);
        }
        return path;
    }

    public static String[] readFile(String directory, String filterRegex) {
        File checkpointDir = new File(directory);
        return checkpointDir.list(new FilenameFilter() {

            @Override
            public boolean accept(File dir, String name) {
                return name.contains(filterRegex);
            }
        });

    }

    public static void downloadFile(String filePath, HttpServletResponse response) {
        if (filePath == null || filePath.isEmpty()) {
            throw new IllegalArgumentException("File path cannot be empty");
        } else {
            // 获取文件对象
            File file = new File(filePath);

            // 检查文件是否存在并且可读
            if (!file.exists() || !file.canRead()) {
                throw new IllegalArgumentException("File not found or cannot be read: " + filePath);
            }

            try (InputStream inputStream = Files.newInputStream(file.toPath());
                 ServletOutputStream outputStream = response.getOutputStream()) {

                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(file.getName(), "UTF-8") + "\"");

                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }

                outputStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
                throw new IllegalArgumentException("Error occurred while downloading file: " + e.getMessage());
            }
        }
    }


}
