package cet.flinkjobservice.dao;

import cet.flinkjobservice.def.FlinkSubmitInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * @className: FlinkInfoDao
 * @description: dao层
 * @author: chenll
 * @date: 2021/12/23
 **/

@Mapper
public interface FlinkInfoDao {

    /**
     * 查询数据库中 flink 任务包含当前 label 的所有信息
     * @param size 大小
     * @return List
     */
    List<FlinkSubmitInfo> getFlinkInfo(int size);

    /**
     * flink 任务提交成功后，在数据库新增一条信息
     * @param info flink参数类
     * @return int
     */
    int addFlinkInfo(FlinkSubmitInfo info);

    /**
     * flink 任务更改后，更新信息
     * @param info flink参数类
     * @return int
     */
    int updateFlinkInfo(FlinkSubmitInfo info);

    /**
     * 查询与 jobName 相关联的 FlinkInfo
     * @param jobName 任务名
     * @return flink参数类
     */
    FlinkSubmitInfo findRelatedJobName(String jobName);
}
