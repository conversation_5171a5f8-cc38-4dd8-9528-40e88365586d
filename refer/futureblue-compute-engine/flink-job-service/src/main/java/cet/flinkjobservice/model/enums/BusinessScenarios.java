package cet.flinkjobservice.model.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum BusinessScenarios {
    ENERGY_CONSUMPTION(1, "能耗");

    private final int id;
    private final String description;

    BusinessScenarios(int id, String description) {
        this.id = id;
        this.description = description;
    }

    public static List<Map<String, Object>> getRequiredParameters(int id) {
        BusinessScenarios scenario = getById(id);
        return ScenarioParameters.getInstance().getRequiredParametersForScenario(scenario);
    }

    public static List<Map<String, Object>> getOptionalParameters(int id) {
        BusinessScenarios scenario = getById(id);
        return ScenarioParameters.getInstance().getOptionalParametersForScenario(scenario);
    }

    public static BusinessScenarios getById(int id) {
        for (BusinessScenarios scenario : values()) {
            if (scenario.getId() == id) {
                return scenario;
            }
        }
        throw new IllegalArgumentException("未找到ID为 " + id + " 的业务场景");
    }

    public static List<Map<String, Object>> getAll() {
        List<Map<String, Object>> result = new ArrayList<>();
        for (BusinessScenarios scenario : values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("value", String.valueOf(scenario.getId()));
            map.put("label", scenario.getDescription());
            result.add(map);
        }
        return result;
    }
}


