package cet.flinkjobservice.model.enums;

public enum EnumModel {

    RuleStatus(RuleStatus.class),
    FunctionType(FunctionType.class),
    FrontedType(FrontedType.class),
    AggregationType(AggregationType.class);

    private final Class<? extends Enum<?>> enumClass;

    EnumModel(Class<? extends Enum<?>> enumClass) {
        this.enumClass = enumClass;
    }

    public Class<? extends Enum<?>> getEnumClass() {
        return enumClass;
    }

    public static Class<? extends Enum<?>> fromEnumName(String enumName) {
        for (EnumModel model : EnumModel.values()) {
            if (model.name().equalsIgnoreCase(enumName)) {
                return model.getEnumClass();
            }
        }
        return null;
    }
}
