package cet.flinkjobservice.task;


import cet.flinkjobservice.util.PathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * job的checkpoint清理
 * @author: zk
 */
@Component
@Slf4j
public class CheckpointCleaner {

    private final PathUtil pathUtil;

    public CheckpointCleaner(PathUtil pathUtil) {
        this.pathUtil = pathUtil;
    }

    /**
     * checkpoint清理
     */
    @Scheduled(cron = "${flink.checkpointCleanCron}")
    public void cleanCheckpoint() throws IOException {
        //定位到checkpoint目录
        String checkpointPath = pathUtil.getRunDirectory() + "/checkpoint";
        String[] rcdFileNames = pathUtil.filterFiles(checkpointPath,".*\\.rcd");
        for (String rcdFileName : rcdFileNames != null ? rcdFileNames : new String[0]){
            Path rcdFilePath = Paths.get(checkpointPath,rcdFileName);
            HashMap<Integer,String> checkpointMap = new HashMap<>();
            StringBuilder sb = new StringBuilder();
            try(BufferedReader br = Files.newBufferedReader(rcdFilePath, StandardCharsets.UTF_8)){
                int row = -1;
                while(true){
                    String line = br.readLine();
                    row++;
                    if(line == null || line.isEmpty()) //读取到文件结尾
                        break;
                    //保留前3个checkpoint
                    if(row <= 2){
                        sb.append(line);
                        sb.append("\n");
                    }
                    else{
                        String[] properties = line.split("\\|");
                        String jobID = properties[2];
                        checkpointMap.put(row,jobID);
                    }
                }
            }
            Set<Map.Entry<Integer, String>> entrySet = checkpointMap.entrySet();
            //清理checkpoint文件夹
            for(Map.Entry<Integer,String> entry:entrySet){
                pathUtil.deleteDir(new File(checkpointPath,entry.getValue()));
            }
            //重写rcd文件
            try(BufferedWriter writer = Files.newBufferedWriter(rcdFilePath,StandardCharsets.UTF_8)){
                writer.write(sb.toString());
            }
        }
    }
}
