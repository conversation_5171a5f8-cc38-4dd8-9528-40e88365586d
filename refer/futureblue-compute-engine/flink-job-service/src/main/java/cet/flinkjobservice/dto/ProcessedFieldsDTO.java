package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "DTO for receiving processed table fields data")
public class ProcessedFieldsDTO {

    @ApiModelProperty(value = "算子Id", example = "T2", required = true)
    private String id;

    @ApiModelProperty(value = "字段",
            example = "[\n" +
                    "  {\n" +
                    "    \"field\": \"deviceId\",\n" +
                    "    \"description\": \"设备id\",\n" +
                    "    \"fieldType\": \"numeric[50]\",\n" +
                    "    \"frontedType\": \"number\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"field\": \"logicalId\",\n" +
                    "    \"description\": \"回路号\",\n" +
                    "    \"fieldType\": \"numeric[50]\",\n" +
                    "    \"frontedType\": \"number\"\n" +
                    "  }\n" +
                    "]",
            required = true)
    private List<Map<String, String>> upstreamFieldTable;

    @ApiModelProperty(value = "业务场景", example = "1", required = true)
    private String businessScene;

    @ApiModelProperty(value = "动态字段", required = true)
    private DynamicField dynamicField;

    @Data
    public static class DynamicField {
        @ApiModelProperty(value = "分组字段", example = "[\"devID\"]", required = true)
        private List<String> groupField;

        @ApiModelProperty(value = "计算字段Id", example = "logicalID", required = true)
        private String funcField;

        @ApiModelProperty(value = "时间字段Id", example = "tm", required = true)
        private String timeField;

        @ApiModelProperty(value = "状态字段Id", example = "status", required = true)
        private String statusField;
    }
}
