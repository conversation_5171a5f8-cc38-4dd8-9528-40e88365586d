package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "DTO for running a job")
public class RunJobDTO {

    @ApiModelProperty(value = "作业名称", example = "MyFlinkJob1", required = true)
    private String jobName;

    @ApiModelProperty(value = "数据库主键ID", example = "1234", required = true)
    private long id;

    @ApiModelProperty(value = "作业运行参数", example = "String")
    private String argues;
}

