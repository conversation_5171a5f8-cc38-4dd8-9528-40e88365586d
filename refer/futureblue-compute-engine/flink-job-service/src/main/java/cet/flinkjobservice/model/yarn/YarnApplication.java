package cet.flinkjobservice.model.yarn;

import lombok.Data;

/**
 * @author: zk
 */
@Data
public class YarnApplication {
    private String id;
    private String user;
    private String name;
    private String queue;
    private String state;
    private String finalStatus;
    private Double progress;
    private String trackingUI;
    private String trackingUrl;
    private String diagnostics;
    private Long clusterId;
    private String applicationType;
    private String applicationTags;
    private Long startedTime;
    private Long finishedTime;
    private Long elapsedTime;
    private String amContainerLogs;
    private String amHostHttpAddress;
    private Integer allocatedMB;
    private Integer allocatedVCores;
    private Integer runningContainers;
    private Long memorySeconds;
    private Long vcoreSeconds;
    private Integer preemptedResourceMB;
    private Integer preemptedResourceVCores;
    private Integer numNonAMContainerPreempted;
    private Integer numAMContainerPreempted;
}
