package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "DTO for querying job rules")
public class QueryRuleDTO {

    @ApiModelProperty(value = "查询的起始索引", example = "0", required = true)
    private int index;

    @ApiModelProperty(value = "查询的记录条数", example = "20", required = true)
    private int limit;

    @ApiModelProperty(value = "查询的记录名", example = "demo副本", required = false)
    private String jobAlias;
}