package cet.flinkjobservice.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2023/6/2
 */
@Component
@Slf4j
public class CheckPointUtils {
    private final PathUtil pathUtil;

    public CheckPointUtils(PathUtil pathUtil) {
        this.pathUtil = pathUtil;
    }

    public void clearCheckPoint(int savePoint) {
        try {
            //定位到checkpoint目录
            String checkpointPath = pathUtil.getRunDirectory() + "/checkpoint";
            String[] rcdFileNames = pathUtil.filterFiles(checkpointPath, ".*\\.rcd");
            for (String rcdFileName : rcdFileNames != null ? rcdFileNames : new String[0]) {
                Path rcdFilePath = Paths.get(checkpointPath, rcdFileName);
                HashMap<Integer, String> checkpointMap = new HashMap<>();
                StringBuilder sb = new StringBuilder();
                try (BufferedReader br = Files.newBufferedReader(rcdFilePath, StandardCharsets.UTF_8)) {
                    int row = -1;
                    while (true) {
                        String line = br.readLine();
                        row++;
                        if (line == null || line.isEmpty()) //读取到文件结尾
                            break;
                        //保留前3个checkpoint
                        if (row <= savePoint) {
                            sb.append(line);
                            sb.append("\n");
                        } else {
                            String[] properties = line.split("\\|");
                            String jobID = properties[2];
                            checkpointMap.put(row, jobID);
                        }
                    }
                }
                Set<Map.Entry<Integer, String>> entrySet = checkpointMap.entrySet();
                //清理checkpoint文件夹
                for (Map.Entry<Integer, String> entry : entrySet) {
                    pathUtil.deleteDir(new File(checkpointPath, entry.getValue()));
                }

                if(sb.length() > 0) {
                    //重写rcd文件
                    try (BufferedWriter writer = Files.newBufferedWriter(rcdFilePath, StandardCharsets.UTF_8)) {
                        writer.write(sb.toString());
                    }
                }

                if(savePoint < 0) {
                    pathUtil.deleteDir(new File(rcdFilePath.toString()));
                }
            }
        } catch (Exception e) {
            log.error("清理checkpoint报错：", e);
        }
    }
}