package cet.flinkjobservice.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.*;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpClientParams;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName ：HttpUtils
 * @date ：Created in 2021/2/18 8:47
 * @description：http帮助类
 */
@Slf4j
public class HttpUtils {
    /**
     http超时时间
     */
    private static int timeOut = 200000;

    /**
     http请求返回信息
     */
    private static String message;

    public static String getMessage() {
        return message;
    }

    public static void setMessage(String message) {
        HttpUtils.message = message;
    }

    /**
     * description:  get请求
     * create time: 2021/2/18 8:56
     */
    public static String get(String url, String encoding, Map<String, String> params) {
        HttpClient httClient = new HttpClient(new HttpClientParams(),new SimpleHttpConnectionManager(true));
        StringBuffer content = new StringBuffer();
        HttpMethod method = initHttpMethod(url, encoding, params, httClient);
        try {
            httClient.executeMethod(method);
            String respString = method.getResponseBodyAsString();
            content.append(respString);
        } catch (ConnectTimeoutException e) {
            message = "Connect Timeout:" + url;
        } catch (SocketTimeoutException e) {
            message = "Socket Timeout:" + url;
        } catch (IOException e) {
            message = e.toString();
        } finally {
            method.releaseConnection();
        }
        return content.toString();
    }

    /**
     * description:  get请求
     * create time: 2021/2/18 8:56
     */
    public static String doGet(String url, String encoding, Map<String, String> params) {
        HttpClient httClient = new HttpClient(new HttpClientParams(), new SimpleHttpConnectionManager(true));
        StringBuffer content = new StringBuffer();
        HttpMethod method = initHttpMethod1(url, encoding, params, httClient);
        try {
            httClient.executeMethod(method);
            String respString = method.getResponseBodyAsString();
            content.append(respString);
        } catch (ConnectTimeoutException e) {
            message = "Connect Timeout:" + url;
        } catch (SocketTimeoutException e) {
            message = "Socket Timeout:" + url;
        } catch (IOException e) {
            message = e.toString();
        } finally {
            method.releaseConnection();
        }
        return content.toString();
    }

    /**
     * description: 初始化http
     * create time: 2021/2/18 8:57
     */
    private static HttpMethod initHttpMethod1(String url, String encoding,
                                             Map<String, String> params, HttpClient httClient) {
        try {
            // 添加param
            if (params != null && params.size() > 0) {
                StringBuilder sbQuery = new StringBuilder();
                for (Map.Entry<String, String> query : params.entrySet()) {
                    if (0 < params.size()) {
                        sbQuery.append("&");
                    }
                    if (StringUtils.isBlank(query.getKey()) && !StringUtils.isBlank(query.getValue())) {
                        sbQuery.append(query.getValue());
                    }
                    if (!StringUtils.isBlank(query.getKey())) {
                        sbQuery.append(query.getKey());
                        if (!StringUtils.isBlank(query.getValue())) {
                            sbQuery.append("=");
                            sbQuery.append(URLEncoder.encode(query.getValue(), "utf-8"));
                        }
                    }
                }
                if (0 < sbQuery.length()) {
                    url = url + "?" + sbQuery;
                }
            }
            HttpMethod method = new GetMethod(url);
            // 设置url连接超时
            httClient.getHttpConnectionManager().getParams().setConnectionTimeout(timeOut);
            // 设置读取数据超时
            httClient.getHttpConnectionManager().getParams().setSoTimeout(timeOut);
            // 这两行解决 too many open files问题
            httClient.getParams().setBooleanParameter(
                    "http.protocol.expect-continue", false);
            // 解决Httpclient远程请求所造成Socket没有释放
            method.addRequestHeader("Connection", "close");
            method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,
                    encoding);
            return method;
        } catch (Exception e) {
            log.error("http init method error.", e);
            return null;
        }
    }

    /**
     * description:  post请求
     * create time: 2021/2/18 8:56
     */
    public static String post(String url, String contentType, String sendData, String encoding) {
        HttpClient httClient = new HttpClient(new HttpClientParams(),new SimpleHttpConnectionManager(true));
        StringBuffer content = new StringBuffer();
        PostMethod method = new PostMethod(url);
        // 设置url连接超时
        httClient.getHttpConnectionManager().getParams().setConnectionTimeout(
                timeOut);
        // 设置读取数据超时
        httClient.getHttpConnectionManager().getParams().setSoTimeout(timeOut);
        // 这两行解决 too many open files问题
        httClient.getParams().setBooleanParameter(
                "http.protocol.expect-continue", false);
        // 解决Httpclient远程请求所造成Socket没有释放
        method.addRequestHeader("Connection", "close");
        method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,
                encoding);
        InputStream in = null;
        BufferedReader buffer = null;
        //失败重试3次
        for (int i=0; i<3; i++) {
            try {
                RequestEntity entity = new StringRequestEntity(sendData,
                        contentType, encoding);
                method.setRequestEntity(entity);
                httClient.executeMethod(method);
                int code = method.getStatusCode();
                if (code == HttpStatus.SC_OK) {
                    in = method.getResponseBodyAsStream();
                    buffer = new BufferedReader(new InputStreamReader(in, encoding));
                    for (String tempstr = ""; (tempstr = buffer.readLine()) != null; ) {
                        content.append(tempstr);
                    }
                    break;
                }
            } catch (Exception e) {
                log.error("http post error.", e);
                throw new RuntimeException(e);
            } finally {
                try {
                    if (buffer != null) {
                        buffer.close();
                    }
                    if (in != null) {
                        in.close();
                    }
                } catch (IOException e) {
                    log.error("http post error", e);
                }
                method.releaseConnection();
            }
        }
        return content.toString();
    }

    /**
     * description:  二进制post请求
     * create time: 2021/2/18 8:56
     */
    public static byte[] binaryPost(String url, String contentType, String sendData, String encoding) {
        HttpClient httClient = new HttpClient(new HttpClientParams(),new SimpleHttpConnectionManager(true));
        PostMethod method = new PostMethod(url);
        // 设置url连接超时
        httClient.getHttpConnectionManager().getParams().setConnectionTimeout(
                timeOut);
        // 设置读取数据超时
        httClient.getHttpConnectionManager().getParams().setSoTimeout(timeOut);
        // 这两行解决 too many open files问题
        httClient.getParams().setBooleanParameter(
                "http.protocol.expect-continue", false);
        // 解决Httpclient远程请求所造成Socket没有释放
        method.addRequestHeader("Connection", "close");
        method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,
                encoding);
        InputStream in = null;
        byte[] data = null;
        //失败重试3次
        for (int i=0; i<3; i++) {
            try {
                RequestEntity entity = new StringRequestEntity(sendData,
                        contentType, encoding);
                method.setRequestEntity(entity);
                httClient.executeMethod(method);
                int httpCode = method.getStatusCode();
                if (httpCode == HttpStatus.SC_OK) {
                    in = method.getResponseBodyAsStream();
                    data = method.getResponseBody();
                    break;
                }
            } catch (Exception e) {
                log.error("http binaryPost error.", e);
            } finally {
                IOUtils.closeQuietly(in);
                method.releaseConnection();
            }
        }
        return data;
    }

    /**
     * description: 初始化http
     * create time: 2021/2/18 8:57
     */
    private static HttpMethod initHttpMethod(String url, String encoding,
                                             Map<String, String> params, HttpClient httClient) {
        try {
            HttpMethod method = new GetMethod(url);
            // 设置url连接超时
            httClient.getHttpConnectionManager().getParams().setConnectionTimeout(
                    timeOut);
            // 设置读取数据超时
            httClient.getHttpConnectionManager().getParams().setSoTimeout(timeOut);
            // 这两行解决 too many open files问题
            httClient.getParams().setBooleanParameter(
                    "http.protocol.expect-continue", false);
            // 解决Httpclient远程请求所造成Socket没有释放
            method.addRequestHeader("Connection", "close");
            // 添加额外的Header
            if (params != null && params.size() > 0) {
                Set<String> keys = params.keySet();
                for (String key : keys) {
                    method.addRequestHeader(key, params.get(key));
                }
            }
            method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,
                    encoding);
            return method;
        } catch (Exception e) {
            log.error("http init method error.", e);
            return null;
        }
    }
}
