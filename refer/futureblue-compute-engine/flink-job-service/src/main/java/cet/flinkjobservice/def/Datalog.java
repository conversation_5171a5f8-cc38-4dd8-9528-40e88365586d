package cet.flinkjobservice.def;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Datalog {
    @ApiModelProperty(value = "设备id")
    private int devID;

    @ApiModelProperty(value = "数据id")
    private int dataID;

    @ApiModelProperty(value = "回路号id")
    private short logicalID;

    @ApiModelProperty(value = "数据类型id")
    private short dataTypeID;

    @ApiModelProperty(value = "时间")
    private long tm;

    @ApiModelProperty(value = "表码值")
    private double val;

    @ApiModelProperty(value = "状态")
    private byte status;
}
