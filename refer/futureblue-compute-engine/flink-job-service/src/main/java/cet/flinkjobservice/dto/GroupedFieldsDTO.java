package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "DTO for receiving grouped table fields data")
public class GroupedFieldsDTO {

    @ApiModelProperty(value = "分组模式", example = "10", required = true)
    private Integer func;

    @ApiModelProperty(value = "分组字段", example = "[\"deviceId\", \"logicalId\"]", required = true)
    private List<String> groupBy;

    @ApiModelProperty(value = "计算字段Id", example = "account_min", required = true)
    private String funcField;

    @ApiModelProperty(value = "算子Id", example = "T00001", required = true)
    private String id;

    @ApiModelProperty(value = "周期字段", required = true)
    private List<Map<String, String>> periodField;

    @ApiModelProperty(value = "合并后的表格数据返回字段",
            example = "[\n" +
                    "  {\n" +
                    "    \"field\": \"deviceId\",\n" +
                    "    \"description\": \"设备id\",\n" +
                    "    \"fieldType\": \"numeric[50]\",\n" +
                    "    \"frontedType\": \"number\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"field\": \"logicalId\",\n" +
                    "    \"description\": \"回路号\",\n" +
                    "    \"fieldType\": \"numeric[50]\",\n" +
                    "    \"frontedType\": \"number\"\n" +
                    "  }\n" +
                    "]",
            required = true)
    private List<Map<String, String>> joinTable;
}
