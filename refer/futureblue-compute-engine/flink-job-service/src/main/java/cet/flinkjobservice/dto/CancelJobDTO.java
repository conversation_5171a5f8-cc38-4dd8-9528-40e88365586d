package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "DTO for canceling a job")
public class CancelJobDTO {

    @ApiModelProperty(value = "作业ID", example = "1234567890abcdef", required = true)
    private String jobId;

    @ApiModelProperty(value = "作业名称", example = "MyFlinkJob", required = true)
    private String jobName;

    @ApiModelProperty(value = "数据库主键ID", example = "1234", required = true)
    private long id;
}

