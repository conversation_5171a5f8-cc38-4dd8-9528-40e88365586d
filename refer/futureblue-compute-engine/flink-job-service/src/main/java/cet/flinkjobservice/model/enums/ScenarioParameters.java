package cet.flinkjobservice.model.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
class ScenarioParameters {
    private static final ScenarioParameters INSTANCE = new ScenarioParameters();
    private ScenarioParameters() {}
    public static ScenarioParameters getInstance() {
        return INSTANCE;
    }
    public List<Map<String, Object>> getRequiredParametersForScenario(BusinessScenarios scenario) {
        List<Map<String, Object>> params = new ArrayList<>();
        switch (scenario) {
            case ENERGY_CONSUMPTION:
                return new EnergyConsumptionRequiredParameters().getRequiredParameters();
        }
        return params;
    }
    public List<Map<String, Object>> getOptionalParametersForScenario(BusinessScenarios scenario) {
        List<Map<String, Object>> params = new ArrayList<>();
        switch (scenario) {
            case ENERGY_CONSUMPTION:
                return new EnergyConsumptionOptionalParameters().getOptionalParameters();
        }
        return params;
    }
}
