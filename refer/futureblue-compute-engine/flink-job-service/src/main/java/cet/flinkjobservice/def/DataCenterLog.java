package cet.flinkjobservice.def;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DataCenterLog {
    @ApiModelProperty(value = "设备id")
    private long deviceid;

    @ApiModelProperty(value = "数据id")
    private long dataid;

    @ApiModelProperty(value = "回路号id")
    private int logicalindex;

    @ApiModelProperty(value = "通道id")
    private long channelid;

    @ApiModelProperty(value = "厂站id")
    private long stationid;

    @ApiModelProperty(value = "物理量id")
    private long quantityobject_id;

    @ApiModelProperty(value = "能源类型")
    private int energytype;

    @ApiModelProperty(value = "管网id")
    private long monitoredid;

    @ApiModelProperty(value = "管网标签")
    private String monitoredlabel;

    @ApiModelProperty(value = "路径")
    private String path;

    @ApiModelProperty(value = "层级")
    private int level;

    @ApiModelProperty(value = "比例")
    private double rate;

    @ApiModelProperty(value = "组织层级id")
    private long orgid;

    @ApiModelProperty(value = "组织层级标签")
    private String orglabel;

}
