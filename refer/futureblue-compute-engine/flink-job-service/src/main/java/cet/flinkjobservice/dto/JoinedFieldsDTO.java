package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "DTO for receiving joined table fields data")
public class JoinedFieldsDTO {

    @ApiModelProperty(value = "合并方式", example = "leftjoin", required = true)
    private String joinMode;

    @ApiModelProperty(value = "表A名称", example = "ANode", required = true)
    private String streamA;

    @ApiModelProperty(value = "表B名称", example = "BNode", required = true)
    private String streamB;

    @ApiModelProperty(value = "关联条件", example = "a.deviceId = b.deviceId", required = true)
    private String associationCondition;

    @ApiModelProperty(value = "算子Id", example = "T00001", required = true)
    private String id;

    @ApiModelProperty(value = "表A字段",
            example = "[\n" +
                    "  {\n" +
                    "    \"field\": \"deviceId\",\n" +
                    "    \"description\": \"设备id\",\n" +
                    "    \"fieldType\": \"numeric[50]\",\n" +
                    "    \"frontedType\": \"number\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"field\": \"logicalId\",\n" +
                    "    \"description\": \"回路号\",\n" +
                    "    \"fieldType\": \"numeric[50]\",\n" +
                    "    \"frontedType\": \"number\"\n" +
                    "  }\n" +
                    "]",
            required = true)
    private List<Map<String, String>> tableA;

    @ApiModelProperty(value = "表B字段",
            example = "[\n" +
                    "  {\n" +
                    "    \"field\": \"deviceId\",\n" +
                    "    \"description\": \"设备id\",\n" +
                    "    \"dataType\": \"numeric[50]\",\n" +
                    "    \"frontedType\": \"number\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"field\": \"logicalId\",\n" +
                    "    \"description\": \"回路号\",\n" +
                    "    \"fieldType\": \"numeric[50]\",\n" +
                    "    \"frontedType\": \"number\"\n" +
                    "  }\n" +
                    "]",
            required = true)
    private List<Map<String, String>> tableB;
}
