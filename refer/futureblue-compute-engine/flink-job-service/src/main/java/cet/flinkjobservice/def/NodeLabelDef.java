package cet.flinkjobservice.def;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 管理层级节点或者设备节点常量
 *
 * <AUTHOR>
 * @date 2020-09-24
 */
public class NodeLabelDef {
    private NodeLabelDef() {
    }

    /**
     * 项目
     */
    public static final String PROJECT = "project";
    /**
     * 建筑物
     */
    public static final String BUILDING = "building";
    /**
     * 市政管道
     */
    public static final String CIVIC_PIPE = "civicpipe";
    /**
     * 楼层
     */
    public static final String FLOOR = "floor";
    /**
     * 房间
     */
    public static final String ROOM = "room";
    /**
     * it机柜
     */
    public static final String IT_CABINET = "itcabinet";
    /**
     * ups
     */
    public static final String UPS = "ups";
    /**
     * 母线
     */
    public static final String BUS_BAR_SECTION = "busbarsection";
    /**
     * 变压器
     */
    public static final String POWER_TRANS_FORMER = "powertransformer";
    /**
     * 蓄电池
     */
    public static final String BATTERY = "battery";
    /**
     * 高压直流设备
     */
    public static final String HVDC = "hvdc";
    /**
     * 电容柜
     */
    public static final String CAPACITOR = "capacitor";
    /**
     * 计量柜
     */
    public static final String METERING_CABINET = "meteringcabinet";
    /**
     * 发电机
     */
    public static final String GENERATOR = "generator";
    /**
     * 母联
     */
    public static final String BUS_BAR_CONNECTOR = "busbarconnector";
    /**
     * 带开关柜的线路段
     */
    public static final String LINE_SEGMENT_WITH_SWITCH = "linesegmentwithswitch";
    /**
     * 低压回路
     */
    public static final String LOW_VOLTAGE_CIRCUIT = "lowvoltagecircuit";
    /**
     * 用能设备
     */
    public static final String MANU_EQUIPMENT = "manuequipment";
    /**
     * 厂区
     */
    public static final String SECTION_AREA = "sectionarea";
    /**
     * PT柜
     */
    public static final String PT_CABINET = "ptcabinet";
    /**
     * 列头柜
     */
    public static final String ARRAY_CABINET = "arraycabinet";
    /**
     * AVC
     */
    public static final String AVC = "avc";
    /**
     * 光电转换器
     */
    public static final String PHOTO_ELE_CONVERTER = "photoeleconverter";
    /**
     * 交换机
     */
    public static final String INTER_CHANGER = "interchanger";
    /**
     * 直流屏
     */
    public static final String DC_PANEL = "dcpanel";
    /**
     * 计算机
     */
    public static final String COMPUTER = "computer";
    /**
     * 通信管理机
     */
    public static final String GATEWAY = "gateway";
    /**
     * 计算机
     */
    public static final String METER = "meter";
    /**
     * 计算机
     */
    public static final String ATS = "ATS";
    /**
     * 管道
     */
    public static final String PIPELINE = "pipeline";

    /**
     * 开关柜
     */
    public static final String SWITCH_CABINET = "switchcabinet";

    /**
     * 配电柜
     */
    public static final String POWER_DIS_CABINET = "powerdiscabinet";

    /**
     * 一段线
     */
    public static final String LINESEGMENT = "linesegment";

    /**
     * 冷却主机
     */
    public static final String COLD_WATER_MAINENGINE = "coldwatermainengine";

    /**
     * 风柜
     */
    public static final String WINDSET = "windset";

    /**
     * 冷却塔
     */
    public static final String COOLING_TOWER = "coolingtower";

    /**
     * 空压机
     */
    public static final String AIR_COMPRESSOR = "aircompressor";

    /**
     * 冷干机
     */
    public static final String COLD_DRYING_MACHINE = "colddryingmachine";

    /**
     * 干燥机
     */
    public static final String DRYING_MACHINE = "dryingmachine";

    /**
     * 蒸汽锅炉
     */
    public static final String BOILER = "boiler";

    /**
     * 泵
     */
    public static final String PUMP = "pump";

    /**
     * 气象监测仪
     */
    public static final String METEOROLOGICAL_MONITOR = "meteorologicalmonitor";


    /**
     * 空调
     */
    public static final String AIR_CONDITIONER = "airconditioner";

    /**
     * 板式换热器
     */
    public static final String PLATE_HEAT_EXCHANGER = "plateheatexchanger";

    /**
     * 泵
     */
    public static final String CITY = "city";
    /**
     * plc
     */
    public static final String PLC = "plc";

    /**
     * 配电室设备
     */
    public static final List<String> POWER_ROOM_DEVICE = Collections.unmodifiableList(Arrays.asList(POWER_TRANS_FORMER,
            LINE_SEGMENT_WITH_SWITCH));

    /**
     * 所有设备
     */
    public static final List<String> ALL_DEVICE = Collections.unmodifiableList(Arrays.asList(UPS, BATTERY, BUS_BAR_CONNECTOR, POWER_TRANS_FORMER,
            LINE_SEGMENT_WITH_SWITCH, BUS_BAR_SECTION, GENERATOR, HVDC, LOW_VOLTAGE_CIRCUIT, CAPACITOR,
            METERING_CABINET, PT_CABINET, ARRAY_CABINET, ATS, AVC, DC_PANEL, IT_CABINET, MANU_EQUIPMENT,
            INTER_CHANGER, PHOTO_ELE_CONVERTER, COMPUTER, METER, GATEWAY, PIPELINE, SWITCH_CABINET, POWER_DIS_CABINET, LINESEGMENT,
            COLD_DRYING_MACHINE, WINDSET, COLD_WATER_MAINENGINE, COOLING_TOWER, AIR_COMPRESSOR, DRYING_MACHINE, BOILER, PUMP));

    /**
     * 配电设备
     */
    public static final List<String> POWER_DEVICE = Collections.unmodifiableList(Arrays.asList(UPS, BATTERY, BUS_BAR_CONNECTOR, POWER_TRANS_FORMER,
            LINE_SEGMENT_WITH_SWITCH, BUS_BAR_SECTION, GENERATOR, HVDC, LOW_VOLTAGE_CIRCUIT, CAPACITOR,
            METERING_CABINET, PT_CABINET, ARRAY_CABINET, ATS, AVC, DC_PANEL, IT_CABINET, PIPELINE, LINESEGMENT, POWER_DIS_CABINET, SWITCH_CABINET));

    /**
     * 配电设备
     */
    public static final List<String> TOPOLOGY_EXPORT_DEVICE = Collections.unmodifiableList(Arrays.asList(UPS, BATTERY, BUS_BAR_CONNECTOR, POWER_TRANS_FORMER,
            LINE_SEGMENT_WITH_SWITCH, BUS_BAR_SECTION, GENERATOR, HVDC, LOW_VOLTAGE_CIRCUIT, CAPACITOR,
            METERING_CABINET, PT_CABINET, ARRAY_CABINET, ATS, AVC, DC_PANEL, IT_CABINET, PIPELINE, POWER_DIS_CABINET, SWITCH_CABINET,
            COLD_DRYING_MACHINE, WINDSET, COLD_WATER_MAINENGINE, COOLING_TOWER, AIR_COMPRESSOR, DRYING_MACHINE, BOILER,PLATE_HEAT_EXCHANGER,PLC));

    /**
     * 台账导出设备
     */
    public static final List<String> EXPORT_DEVICE = Collections.unmodifiableList(Arrays.asList(
            CIVIC_PIPE, PIPELINE, PUMP, COOLING_TOWER, WINDSET, COLD_WATER_MAINENGINE, AIR_COMPRESSOR, COLD_DRYING_MACHINE,
            DRYING_MACHINE, BOILER, LINESEGMENT, POWER_TRANS_FORMER, CAPACITOR, LINE_SEGMENT_WITH_SWITCH, BUS_BAR_SECTION, METERING_CABINET,
            PT_CABINET, POWER_DIS_CABINET, SWITCH_CABINET, ARRAY_CABINET, UPS, BATTERY, HVDC, INTER_CHANGER, DC_PANEL, GENERATOR));
    /**
     * 通信设备
     */
    public static final List<String> COMMUNICATION_DEVICE = Collections.unmodifiableList(Arrays.asList(INTER_CHANGER, PHOTO_ELE_CONVERTER, COMPUTER, METER, GATEWAY));

    /**
     * 其他能源设备
     */
    public static final List<String> OTHER_DEVICE = Collections.unmodifiableList(Arrays.asList(PIPELINE));
    /**
     * 压缩空气能源设备
     */
    public static final List<String> AIRCOMPRESSOR_DEVICE = Collections.unmodifiableList(Arrays.asList(PIPELINE, AIR_COMPRESSOR, COLD_DRYING_MACHINE, DRYING_MACHINE, COOLING_TOWER));
    /**
     * 冷量设备
     */
    public static final List<String> REFRIGRATION_DEVICE = Collections.unmodifiableList(Arrays.asList(PIPELINE, WINDSET, COLD_WATER_MAINENGINE, COOLING_TOWER, PLATE_HEAT_EXCHANGER, PUMP));
    /**
     * 热量和热水设备
     */
    public static final List<String> BOILER_DEVICE = Collections.unmodifiableList(Arrays.asList(PIPELINE, BOILER));
    /**
     * 项目节点树
     */
    public static final List<String> PROJECT_TREE_NODE = Collections.unmodifiableList(Arrays.asList(BUILDING, FLOOR, ROOM, MANU_EQUIPMENT, CIVIC_PIPE, PIPELINE, POWER_TRANS_FORMER, CAPACITOR, LINE_SEGMENT_WITH_SWITCH, BUS_BAR_SECTION, METERING_CABINET, BUS_BAR_CONNECTOR, PT_CABINET));

    /**
     * 所有设备
     */
    public static final List<String> DEVICE_MANAGER = Collections.unmodifiableList(Arrays.asList(UPS, BATTERY, BUS_BAR_CONNECTOR, POWER_TRANS_FORMER,
            LINE_SEGMENT_WITH_SWITCH, BUS_BAR_SECTION, GENERATOR, HVDC, CAPACITOR,
            METERING_CABINET, PT_CABINET, ARRAY_CABINET, ATS, AVC, DC_PANEL, IT_CABINET,
            INTER_CHANGER, PHOTO_ELE_CONVERTER, COMPUTER, METER, GATEWAY, PIPELINE, SWITCH_CABINET, POWER_DIS_CABINET, LINESEGMENT,
            COLD_DRYING_MACHINE, WINDSET, COLD_WATER_MAINENGINE, COOLING_TOWER, AIR_COMPRESSOR, DRYING_MACHINE, BOILER));
}
