package cet.flinkjobservice.model.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class EnergyConsumptionRequiredParameters {

    /**
     * 获取能源消耗场景所需的参数
     * @return 包含所有必需参数的Map
     */
    public List<Map<String, Object>> getRequiredParameters() {
        List<Map<String, Object>> paramsList = new ArrayList<>();

        // 添加分组字段参数
        paramsList.add(createFieldParamMap("groupField", "分组字段", "请指定计算分组ID", true, true));

        // 添加计算字段参数
        paramsList.add(createFieldParamMap("funcField", "计算字段", "请指定计算字段ID", false, true));

        // 添加时间字段参数
        paramsList.add(createFieldParamMap("timeField", "时间字段", "请指定时间字段ID", false, true));

        // 添加状态字段参数
        paramsList.add(createFieldParamMap("statusField", "状态字段", "请指定状态字段ID", false, true));

        return paramsList;
    }

    /**
     * 创建字段参数Map
     * @param field 字段名称
     * @param fieldName 字段中文名称
     * @param description 字段描述
     * @param isChooseMore 是否可多选
     * @param isRequired 是否必需
     * @return 包含字段参数的Map
     */
    private Map<String, Object> createFieldParamMap(String field, String fieldName, String description, boolean isChooseMore, boolean isRequired) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("field", field);
        paramMap.put("fieldName", fieldName);
        paramMap.put("description", description);
        paramMap.put("isChooseMore", isChooseMore);
        paramMap.put("isRequired", isRequired);
        return paramMap;
    }
}
