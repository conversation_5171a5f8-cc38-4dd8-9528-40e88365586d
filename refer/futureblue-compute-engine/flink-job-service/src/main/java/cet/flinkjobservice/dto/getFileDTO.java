package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "DTO for getting a file")
public class getFileDTO {
    @ApiModelProperty(value = "作业名称", example = "MyFlinkJob", required = true)
    private String jobName;

    @ApiModelProperty(value = "算子ID", example = "T00001", required = true)
    private String id;

    @ApiModelProperty(value = "当前页码", example = "0", required = true)
    private int index;

    @ApiModelProperty(value = "每页记录数", example = "20", required = true)
    private int limit;
}
