package cet.flinkjobservice.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Scanner;

/**
 * 命令行执行工具类
 * @author:zk
 */
@Slf4j
public class CommandUtil {
    public static String run(String command) throws IOException {
        Scanner input = null;
        String result = "";
        Process process = null;
        /*try {
            process = Runtime.getRuntime().exec(command);
            try {
                //等待命令执行完成
                process.waitFor(10, TimeUnit.SECONDS);
                System.out.println("current process: " + process);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            InputStream is = process.getInputStream();
            input = new Scanner(is);
            while (input.hasNextLine()) {
                if(!result.isEmpty())
                    result += "\n";
                result += input.nextLine();
                System.out.println("current result: " + input.nextLine());
            }
            log.info(command + "\n" + result); //加上命令本身，打印出来
        } finally {
            if (input != null) {
                input.close();
            }
            if (process != null) {
                process.destroy();
            }
        }
        return result;*/


        try {
            process = Runtime.getRuntime().exec(command);
            // 获取标准输入流
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = null;
            while ((line = reader.readLine()) != null) {
                if(!result.isEmpty())
                    result += "\n";
                result += line;
            }
            System.out.println("current result: " + result);
            // waitFor 阻塞等待 异步进程结束，并返回执行状态，0代表命令执行正常结束。
            System.out.println(process.waitFor());
        } catch (IOException | InterruptedException e) {
            if (process != null) {
                // 销毁当前进程，阻断当前命令执行
                process.destroy();
            }
            e.printStackTrace();
        }
        return result;



    }




}
