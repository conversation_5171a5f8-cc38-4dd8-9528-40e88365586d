package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "DTO for saving job arguments")
public class SaveArgsDTO {

    @ApiModelProperty(value = "数据库主键ID", example = "1234", required = true)
    private long id;

    @ApiModelProperty(value = "参数信息（JSON格式）", example = "String", required = true)
    private String argues;
}
