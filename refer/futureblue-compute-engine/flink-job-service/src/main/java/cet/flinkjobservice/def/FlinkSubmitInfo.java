package cet.flinkjobservice.def;

import lombok.Data;

/**
 * @className: FlinkInfor
 * @description: TODO
 * @author: chenll
 * @date: 2021/12/23
 **/
@Data
public class FlinkSubmitInfo {

    /**
     * 主键 id
     */
    int id;

    /**
     * flink 任务名
     */
    String jobName;

    /**
     * flink 任务 application_id
     */
    String appId;

    /**
     * flink 任务 job_id
     */
    String jobId;

    /**
     * flink 任务启动参数
     */
    String execArgs;

    /**
     * flink 任务包含子规则条数
     */
    int subTaskSize;

    /**
     * 数据源 标签
     */
    String sourceLabel;

    /**
     * 当前 flink 任务 savepoint 路径
     */
    String savepath;

    /**
     * 当前 flink 任务 jobmanager.memory.process.size 大小
     */
    String jm;

    /**
     * 当前 flink 任务 taskmanager.memory.process.size 大小
     */
    String tm;

    /**
     * 当前 flink 任务 parallelism 大小
     */
    String p;

    /**
     * flink 任务创建时间
     */
    String createTime;

    /**
     * flink 任务更新时间
     */
    String updateTime;

    /**
     * flink 任务操作人
     */
    String staff;
}
