package cet.flinkjobservice.model.enums;

import cet.flinkjobservice.def.DataCenterElectric;
import cet.flinkjobservice.def.Datalog;
import cet.flinkjobservice.def.DataCenterLog;
import lombok.Getter;

@Getter
public enum SourceModel {
    Datalog(Datalog.class, "DatalogDataStreamSource"),
    DataCenterLog(DataCenterLog.class, "data-center-agg"),
    DataCenterElectric(DataCenterElectric .class,"data-center-electric");

    // 成员变量
    private Class clz;
    private String functionType;

    // 构造方法
    private SourceModel(Class clz, String functionType) {
        this.clz = clz;
        this.functionType = functionType;
    }

    public static Class fromFunctionType(String functionType) {
        for (SourceModel c : SourceModel.values()) {
            if (c.getFunctionType().equals(functionType)) {
                return c.clz;
            }
        }
        return null;
    }

}
