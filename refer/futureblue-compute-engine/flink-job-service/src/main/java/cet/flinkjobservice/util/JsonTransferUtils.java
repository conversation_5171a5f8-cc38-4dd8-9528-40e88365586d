package cet.flinkjobservice.util;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019年11月1日 Json转换工具类
 */
@SuppressWarnings("rawtypes")
public class JsonTransferUtils {

	private static final Logger logger = LoggerFactory.getLogger(JsonTransferUtils.class);

	private static ObjectMapper objectMapper = new ObjectMapper();

	static {
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		// 如果是空对象的时候,不抛异常
		objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
	}

	public static <T> List<T> transferList(List list, Class<T> clazz) {
		if (CollectionUtils.isEmpty(list)) {
			return new ArrayList<T>();
		}
		try {
			JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, clazz);
			return objectMapper.readValue(objectMapper.writeValueAsString(list), javaType);
		} catch (Exception ex) {
			logger.error("JsonTransferUtils.transferList", ex);
			throw new RuntimeException("JSON转换集合失败！", ex);
		}
	}

	public static <T> List<T> transferJsonString(String jsonString, Class<T> clazz) {
		if (StringUtils.isBlank(jsonString)) {
			return new ArrayList<T>();
		}
		try {
			JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, clazz);
			return objectMapper.readValue(jsonString, javaType);
		} catch (IOException ex) {
			logger.error("JsonTransferUtils.transferJsonString", ex);
			throw new RuntimeException("JSON转换集合失败！",ex);
		}
	}

	public static <T> T parseObject(String data, Class<T> clazz) {
		try {
			return objectMapper.readValue(data, clazz);
		} catch (IOException e) {
			logger.error("JsonTransferUtils.parseObject data {} "+ data, e);
			throw new RuntimeException("JSON转换对象失败！", e);
		}
	}

	public static <T> String toJsonString(List<T> data) {
		try {
			return objectMapper.writeValueAsString(data);
		} catch (JsonProcessingException e) {
			logger.error("JsonTransferUtils.toJSONString", e);
			throw new RuntimeException("JSON转换对象失败！", e);
		}
	}

	public static <T> String toJsonString(T data) {
		try {
			return objectMapper.writeValueAsString(data);
		} catch (JsonProcessingException e) {
			logger.error("JsonTransferUtils.toJSONString", e);
			throw new RuntimeException("JSON转换对象失败！", e);
		}
	}

	public static <T> List<List<T>> transferNestingJson(String jsonString, Class<T> clazz) {
		List<List<T>> result = new ArrayList<>();
		List<List> list = transferJsonString(jsonString, List.class);
		list.forEach(e -> {
			result.add(transferList(e, clazz));
		});
		return result;
	}


	@SuppressWarnings("unchecked")
	public static Map<String, Object> transJosn2Map(JSONObject json) {
		Map<String, Object> map = parseObject(json.toString(), HashMap.class);
		return map;
	}

	public static List<Map<String, Object>> transArray2MapList(List<JSONObject> list) {
		List<Map<String, Object>> mapList = new ArrayList<>();
		for (JSONObject json : list) {
			Map<String, Object> map = transJosn2Map(json);
			mapList.add(map);
		}
		return mapList;
	}

}