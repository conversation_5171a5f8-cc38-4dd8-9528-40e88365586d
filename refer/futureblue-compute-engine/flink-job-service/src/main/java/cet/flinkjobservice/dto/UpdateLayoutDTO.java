package cet.flinkjobservice.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "DTO for saving job arguments")
public class UpdateLayoutDTO {

    @ApiModelProperty(value = "数据库主键ID", example = "1234", required = true)
    private long id;

    @ApiModelProperty(value = "作业名称", example = "MyFlinkJob", required = true)
    private String jobName;

    @ApiModelProperty(value = "布局信息（JSON格式）",
            example = "String",
            required = true)
    private String layout;
}
