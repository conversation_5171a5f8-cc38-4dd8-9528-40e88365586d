package cet.flinkjobservice.model.enums;

public enum FrontedType {
    STRING("(?i)string"),
    DATE("(?i)date"),
    NUMBER("(?i)int|short|byte|long|float|double|integer|bigdecimal|biginteger|decimal|numeric"),
    BOOLEAN("(?i)boolean"),
    ENUM("(?i)enum"),
    OBJECT("(?i)object"),
    ARRAY("(?i).*\\[\\]"),  // 匹配数组类型，如 int[], String[]
    GENERIC_OBJECT("(?i).*<.*>"), // 泛型类型匹配，如 List<String>
    DEFAULT("");

    private final String fieldTypePattern;

    FrontedType(String fieldTypePattern) {
        this.fieldTypePattern = fieldTypePattern;
    }

    public String getFieldTypePattern() {
        return fieldTypePattern;
    }

    public static FrontedType fromFieldType(String fieldType, String description) {
        // 判断是否匹配 STRING 类型
        if (fieldType.matches(STRING.fieldTypePattern)) {
            return STRING;
        }
        // 判断是否匹配 DATE 类型（基于描述中的关键字）
        if (description != null && description.contains("时间")) {
            return DATE;
        }
        // 判断是否匹配 NUMBER 类型
        if (fieldType.matches(NUMBER.fieldTypePattern)) {
            return NUMBER;
        }
        // 判断是否匹配 BOOLEAN 类型
        if (fieldType.matches(BOOLEAN.fieldTypePattern)) {
            return BOOLEAN;
        }
        // 判断是否匹配 ENUM 类型
        if (fieldType.matches(ENUM.fieldTypePattern)) {
            return ENUM;
        }
        // 判断是否匹配 OBJECT 类型
        if (fieldType.matches(OBJECT.fieldTypePattern)) {
            return OBJECT;
        }
        // 判断是否匹配 ARRAY 类型
        if (fieldType.matches(ARRAY.fieldTypePattern)) {
            return ARRAY;
        }
        // 判断是否匹配 GENERIC_OBJECT 类型
        if (fieldType.matches(GENERIC_OBJECT.fieldTypePattern)) {
            return GENERIC_OBJECT;
        }
        // 如果没有匹配的类型，返回 DEFAULT 类型
        return ENUM;
    }
}
