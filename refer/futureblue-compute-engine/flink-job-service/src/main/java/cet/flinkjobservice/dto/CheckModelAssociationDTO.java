package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "DTO for checking model association")
public class CheckModelAssociationDTO {

    @ApiModelProperty(value = "模型映射表",
            example = "[\n" +
                    "  {\n" +
                    "    \"selectedAtype\": \"float\",\n" +
                    "    \"selectedBtype\": \"float8\",\n" +
                    "    \"selectedAField\": \"deviceid\",\n" +
                    "    \"selectedBField\": \"floorarea\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"selectedAtype\": \"long\",\n" +
                    "    \"selectedBtype\": \"\",\n" +
                    "    \"selectedAField\": \"dataid\",\n" +
                    "    \"selectedBField\": \"\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"selectedAtype\": \"int\",\n" +
                    "    \"selectedBtype\": \"int8\",\n" +
                    "    \"selectedAField\": \"logicalindex\",\n" +
                    "    \"selectedBField\": \"population\"\n" +
                    "  }\n" +
                    "]",
            required = true)
    private List<Map<String, String>> modelAssociation;

}
