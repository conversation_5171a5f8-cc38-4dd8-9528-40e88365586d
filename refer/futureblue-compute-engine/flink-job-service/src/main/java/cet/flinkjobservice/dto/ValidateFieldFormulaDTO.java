package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "DTO for validating a formula field")
public class ValidateFieldFormulaDTO {

    @ApiModelProperty(value = "所有字段,包括公式字段",
            example = "["
                    + "  {"
                    + "    \"field\": \"devID\","
                    + "    \"description\": \"设备id\","
                    + "    \"fieldType\": \"long\","
                    + "    \"frontedType\": \"number\""
                    + "  },"
                    + "  {"
                    + "    \"field\": \"logicalID\","
                    + "    \"description\": \"long\","
                    + "    \"fieldType\": \"long\","
                    + "    \"frontedType\": \"number\""
                    + "  },"
                    + "  {"
                    + "    \"field\": \"formula\","
                    + "    \"fieldType\": \"\","
                    + "    \"description\": \"新计算字段\","
                    + "    \"frontedType\": \"number\","
                    + "    \"formula\": \"2+$devID#+8*7-9\""
                    + "  }"
                    + "]",
            required = true)
    private List<Map<String, String>> tableArray;

}
