package cet.flinkjobservice.model.enums;

public enum AggregationType implements NumberEnum {
    MIN(4, "vMin", "最小值", "double"),
    MAX(3, "vMax", "最大值", "double"),
    AVG(11, "vAvg", "平均值", "double"),
    SUM(10, "vSum", "累加值", "double"),
    ARITHSUM(18, "vArithSum", "算术累加", "double"),
    CP95(6, "vCp95", "95百分位值", "double"),
    COUNT(17, "vCount", "计数结果", "double"),
    QUALIFICATION(12, "vQualificationRate", "合格率", "double");

    private final int code;
    private final String fieldName;
    private final String description;
    private final String fieldType;

    AggregationType(int code, String fieldName, String description, String fieldType) {
        this.code = code;
        this.fieldName = fieldName;
        this.description = description;
        this.fieldType = fieldType;
    }

    public int getCode() {
        return code;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getDescription() {
        return description;
    }

    public String getFieldType() {
        return fieldType;
    }

    @Override
    public Object getValue() {
        return this.code;
    }

    @Override
    public String getLabel() {
        return this.fieldName;
    }

    // 根据 code 查找对应的 AggregationType
    public static AggregationType fromCode(int code) {
        for (AggregationType type : AggregationType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unsupported aggregation type: " + code);
    }
}
