package cet.flinkjobservice.def;

/**
 * <AUTHOR>
 * @className ModelLabelDefinition
 * @description 建模表或者枚举的modelLabel
 * @date 2020/2/24 13:48
 */

public class ModelLabelDef {
    private ModelLabelDef() {
    }

    public static final String DIMENSION = "dimension";
    public static final String TAG = "tag";
    public static final String LEVEL = "level";
    /**
     * 管理节点关联关系
     */
    public static final String NODE_RELATIONSHIP = "noderelationship";

    /**
     *
     * @deprecated 项目
     */
    @Deprecated
    public static final String PROJECT = "project";
    /**
     * @deprecated 建筑物
     */
    @Deprecated
    public static final String BUILDING = "building";
    /**
     * @deprecated 市政管道
     */
    @Deprecated
    public static final String CIVIC_PIPE = "civicpipe";
    /**
     * @deprecated 楼层
     */
    @Deprecated
    public static final String FLOOR = "floor";
    /**
     * @deprecated 房间
     */
    @Deprecated
    public static final String ROOM = "room";
    /**
     * @deprecated 油田公司
     */
    @Deprecated
    public static final String OIL_COMPANY = "oilcompany";
    /**
     * 模型表后缀
     */
    public static final String PREFIX = "_model";

    /**
     * 模型表后缀
     */
    public static final String PREFIX_ID = "_id";
    /**
     * 能耗数据
     */
    public static final String ENERGY_CONSUMPTION = "energyconsumption";
    /**
     * 分摊能耗数据
     */
    public static final String ENERGY_CONSUMPTION_SHARE = "energyconsumptionshare";
    /**
     * 基于分摊统计的能耗数据
     */
    public static final String ENERGY_CONSUMPTION_ON_SHARE = "energyconsumptiononshare";
    /**
     * 能耗计划数据
     */
    public static final String ENERGY_CONSUMPE_PLAN = "energyconsumeplan";
    /**
     * 能耗计划数据
     */
    public static final String ENERGY_CONSUMPTION_PLAN = "energyconsumptionplan";
    /**
     * 能耗预测数据
     */
    public static final String TOTAL_ENERGY_CONSUMPTION_PREDICT = "totalenergyconsumptionpredict";
    /**
     * 能耗预测数据
     */
    public static final String ENERGY_CONSUMPTION_PREDICT = "energyconsumptionpredict";

    /**
     * 费率方案
     */
    public static final String FEE_SCHEME = "feescheme";
    /**
     * 单一费率
     */
    public static final String SINGLE_FEE_RECORD = "singlefeerecord";
    /**
     * timeshareperiod
     */
    public static final String TIME_SHARE_PERIOD = "timeshareperiod";
    /**
     * 分时方案
     */
    public static final String TIME_SHARE_FEE_RECORD = "timesharefeerecord";
    /**
     * 分时费率
     */
    public static final String TIME_SHARE_FEE_SINGLE_RATE = "timesharefeesinglerate";
    /**
     * timesharerelationship
     */
    public static final String TIMESHARERELATIONSHIP = "timesharerelationship";
    /**
     * stagefeerecord
     */
    public static final String STAGE_FEE_RECORD = "stagefeerecord";
    /**
     * 阶梯计费费率设置
     */
    public static final String STAGE_FEE_SET = "stagefeeset";
    /**
     * powertarifffactor
     */
    public static final String POWER_TARIFF_FACTOR = "powertarifffactor";
    /**
     * 力调电费惩奖因数
     */
    public static final String POWER_TARIFF = "powertariff";
    /**
     * 附加费用修改记录
     */
    public static final String SURCHARGE_FEE_RECORD = "surchargefeerecord";
    /**
     * 容量费率记录
     */
    public static final String VOLUME_FEE_RECORD = "volumefeerecord";
    /**
     * 需量费率记录
     */
    public static final String DEMAND_FEE_RECORD = "demandfeerecord";
    /**
     * dayshareset
     */
    public static final String DAY_SHARE_SET = "dayshareset";
    /**
     * dayset
     */
    public static final String DAY_SET = "dayset";
    /**
     * 成本分摊
     */
    public static final String ENERGY_SHARE_CONFIG = "energyshareconfig";
    /**
     * 成本核算方案节点关联
     */
    public static final String COST_CHECK_NODE_CONFIG = "costchecknodeconfig";
    /**
     * 成本核算方案
     */
    public static final String COST_CHECK_PLAN = "costcheckplan";
    /**
     * 成本构成项
     */
    public static final String COST_CHECK_ITEM = "costcheckitem";
    /**
     * 计费项值
     */
    public static final String FEE_ITEM_VALUE = "feeitemvalue";

    /**
     * 成本构成项值
     */
    public static final String COST_CHECK_ITEM_VALUE = "costcheckitemvalue";
    /**
     * 费率类型
     */
    public static final String FEE_RATE_TYPE = "feeratetype";
    /**
     * 能耗分摊
     */
    public static final String ENERGY_SHARE_TO_OBJECT = "energysharetoobject";
    /**
     * 能分摊，用于kttle统计
     */
    public static final String ENERGY_SHARE_SCHEME = "energysharescheme";
    /**
     * 一段线或者开关柜
     */
    public static final String LINE_SEGMENT_WITH_SWITCH = "linesegmentwithswitch";
    public static final String DEMAND_GROUP = "demandgroup";
    public static final String DEMAND_ACCOUNT = "demandaccount";
    public static final String DEMAND_DECLARE_ITEM = "demanddeclareitem";
    public static final String VOLUME_OR_CHARGING_CHANGE_RECORD = "volumeorchargingchangerecord";
    /**
     * 费率方案
     */
    public static final String TABLE_FEE_SCHEME = "feescheme";
    /**
     * 容量计费记录
     */
    public static final String TABLE_VOLUME_FEE_RECORD = "volumefeerecord";
    /**
     * 需量计费记录
     */
    public static final String TABLE_DEMAND_FEE_RECORD = "demandfeerecord";
    /**
     * 报警方案
     */
    public static final String ALARM_SCHEME = "alarmscheme";
    /**
     * 报警等级设置
     */
    public static final String ALARM_LEVEL_CONFIG = "alarmlevelconfig";
    /**
     * 对标方案
     */
    public static final String BENCH_MARK_SET = "benchmarkset";
//    /**
//     * 用能设备
//     */
//    public static final String MANUFACTURE_EQUIPMENT_TEMPLATE = "manufactureequipmenttemplate";
    /**
     * @deprecated 新的用能设备
     */
    @Deprecated
    public static final String MANU_EQUIPMENT = "manuequipment";
    /**
     * @deprecated  机采设备
     */
    @Deprecated
    public static final String MECHANICAL_MINING_MACHINE = "mechanicalminingmachine";
    /**
     * @deprecated 机采设备
     */
    @Deprecated
    public static final String PUMP = "pump";
    /**
     *@deprecated  加热炉
     */
    @Deprecated
    public static final String HEATING_FURNACE = "heatingfurnace";
    public static final String DIM_ENERGY = "dim2energy";
    /**
     * 被分摊对象能源类型
     */
    public static final String OBJECT_ENERGY_TYPE = "objectenergytype";
    /**
     * 子节点全选半选状态
     */
    public static final String CHILD_SELECT_STATE = "childSelectState";
    /**
     * 监测设备
     */
    public static final String PEC_DEVICE_EXTEND = "pecdeviceextend";
    public static final String EEM_OPERATION_LOG = "eemoperationlog";
    public static final String EEM_OPERATION_TYPE = "eemoperationtype";
    public static final String EEM_OPERATION_SUB_TYPE = "eemoperationsubtype";
    public static final String OPERATION_TYPE = "operationtype";
    public static final String LOGIN_OUT = "loginout";
    public static final String OPERATE_USER_GROUP = "operateusergroup";
    public static final String OPERATE_TENANT = "operatetenant";
    public static final String OPERATE_ROLE = "operaterole";
    public static final String OPERATE_USER = "operateuser";
    public static final String BASIC_FEE_RATE = "basicfeerate";
    public static final String PROJECT_ENERGY_TYPE = "projectenergytype";
    public static final String ENERGY_TYPE = "energytype";
    /**
     * 供电关系
     */
    public static final String ENERGY_SUPPLY_TO = "energysupplyto";
    /**
     * 计划产量
     */
    public static final String PRODUCTION_PLAN = "productionplan";
    /**
     * 计划产量
     */
    public static final String PRODUCTION_DATA = "productiondata";
    /**
     * 能效实体配置项
     */
    public static final String KPISET = "kpiset";
    /**
     * 通用能效配置
     */
    public static final String ENERGY_EFFICIENCY_SET = "energyefficiencyset";
    /**
     * 故障场景
     */
    public static final String FAULT_SCENARIOS = "faultscenarios";
    /**
     * 故障预案
     */
    public static final String EVENT_PLAN = "eventplan";
    /**
     * 设备归类
     */
    public static final String DEVICE_CLASSIFICATION = "deviceclassification";
    /**
     * 事件归类
     */
    public static final String EVENT_CLASSIFICATION = "eventclassification";
    /**
     * 故障关键词
     */
    public static final String ALARM_KEYWORD = "alarmkeyword";
    /**
     * 收敛事件
     */
    public static final String CONVERGENCE_EVENT = "convergenceevent";
    /**
     * 根据事件确认状态统计事件
     */
    public static final String CONVERGENCE_CONFIRM_COUNT = "convergenceconfirmcount";
    /**
     * 多维度收敛事件统计
     */
    public static final String MULTI_CONVERGENCE_EVENT_COUNT = "multiconvergenceeventcount";
    /**
     * 收敛事件统计
     */
    public static final String CONVERGENCE_EVENT_COUNT = "convergenceeventcount";

    /**
     * SARFI曲线
     */
    public static final String SARFI_CHART_CURVE = "sarfichartcurve";
    /**
     * SARFI曲线
     */
    public static final String SARFI_CHART_VALUE = "sarfichartvalue";
    public static final String RO_PQ_EVENT_COUNT = "ro_pqeventcount";
    /**
     * PQ变动事件
     */
    public static final String PQ_VARIATION_EVENT = "pqvariationevent";
    /**
     * 管网连接关系模型
     */
    public static final String PIPE_NET_WORK_CONNECTION_MODEL = "pipenetworkconnectionmodel";
    /**
     * 母线
     */
    public static final String BUS_BAR_SECTION = "busbarsection";
    /**
     * 母联
     */
    public static final String BUS_BAR_CONNECTOR = "busbarconnector";
    /**
     * 事件等级
     */
    public static final String PEC_EVENT_LEVEL = "peceventlevel";
    /**
     * peccore事件类型
     */
    public static final String PEC_EVENT_TYPE = "peceventtype";
    /**
     * 系统事件类型
     */
    public static final String EVENT_TYPE = "eventtype";
    /**
     * 事件确认状态
     */
    public static final String CONFIRM_EVENT_STATUS = "confirmeventstatus";
    /**
     * pec事件poi
     */
    public static final String POI_RECORD = "poirecord";
    /**
     * 被监测关系
     */
    public static final String MEASURED_BY = "measuredby";
    /**
     * 系统事件
     */
    public static final String SYSTEM_EVENT = "systemevent";
    /**
     * DATA_INPUT_MAPPING_TABLE
     */
    public static final String DATA_INPUT_MAPPING_TABLE = "datainputmappingtable";
    /**
     * PROJECT_DEMAND_CONFIG
     */
    public static final String PROJECT_DEMAND_CONFIG = "projectdemandconfig";
    /**
     * DEMAND_PREDICT_DATA
     */
    public static final String DEMAND_PREDICT_DATA = "demandpredictdata";
    /**
     * DYNAMO_METER_CARD
     */
    public static final String DYNAMO_METER_CARD = "dynamometercard";
    /**
     * 油井数据
     */
    public static final String OIL_WELL_DATA = "oilwelldata";
    public static final String PEC_EVENT_EXTEND = "peceventextend";
    /**
     * 电压变动事件类型
     */
    public static final String PQ_VARIATION_EVENT_TYPE = "pqvariationeventtype";
    /**
     * 故障方向类型
     */
    public static final String TRANSIENT_FAULT_DIRECTION = "transientfaultdirection";
    /**
     * ITIC容忍度区域
     */
    public static final String TOLERANCE_BAND = "toleranceband";
    public static final String MO_PQ_EVENT_COUNT = "mo_pqeventcount";
    /**
     * 联系人
     */
    public static final String CONTACT = "contact";
    /**
     * pq压降事件统计
     */
    public static final String PQ_SAG_EVENT_COUNT = "pqsageventcount";
    /**
     * 折标煤转换系数
     */
    public static final String CONVERTED_STANDARD_COAL_COEF = "convertedstandardcoalcoef";

    public static final String AGGREGATION_CYCLE = "aggregationcycle";
    public static final String TIME_PERIOD_LABEL = "timeperiodlabel";
    public static final String FILE_MODEL = "filemodel";
    public final static String FILE_FORMAT = "fileformat";
    /**
     * 暂态事件统计
     */
    public static final String MO_PQ_VARIATION_EVENT_COUNT = "mo_pqvariationeventtypecount";
    /**
     * 电能质量事件变动
     */
    public static final String RO_PQ_VARIATION_EVENT_COUNT = "ro_pqvariationeventcount";
    /**
     * 电能质量事件区域统计模型
     */
    public static final String MO_PQ_VARIATION_EVENT_TOLERANCE_TYPE_COUNT = "mo_pqvariationeventtolerancetypecount";
    /**
     * 电能质量故障方向统计模型
     */
    public static final String MOPQ_VARIATION_EVENT_TRANSIENT_FAULT_DIRECTION_COUNT = "mo_pqvariationeventtransientfaultdirectioncount";
    /**
     * 五项国标
     */
    public static final String PQ_FIVES_TD_RATE = "dcpqfivestdrate";
    /**
     * 稳态指标超标次数
     */
    public static final String PQ_STEADY_EXCEED_COUNT = "dcpqsteadyexceedcount";
    /**
     * 电能质量对标管理
     */
    public static final String BENCHMARK_MANAGEMENT = "benchmarkmanagement";

    public static final String DC_INDICATOR_TYPE = "dcindicatortype";

    public static final String INDICATOR_TYPE = "indicatortype";

    public static final String INDICATOR_LIMIT_TYPE = "indicatorlimittype";

    public static final String PROVINCE = "province";

    public static final String DISTRICT = "district";

    public static final String CITY = "city";

    /**
     * 设备类型
     */
    public static final String METER_TYPE = "metertype";

    /**
     * 设备子类型
     */
    public static final String SUB_METER_TYPE = "submetertype";
    public static final String DEVICE_EFF_TYPE = "deviceefftype";
    public static final String ENERGY_EFFICIENCY_TYPE = "energyefficiencytype";
    public static final String PRODUCT_TYPE = "producttype";
    public static final String ENERGY_EFFICIENCY_UNIT_TYPE = "energyefficiencyunittype";

    /**
     * 定义能源基础单位以及与最小单位转换方式
     */
    public static final String ENERGY_UNIT_DEFINE = "energyunitdefine";

    /**
     * 用户自定义单位转换
     */
    public static final String USER_DEFINE_UNIT = "userdefineunit";

    /**
     * 用户自定义单位转换
     */
    public static final String UNIT_MULTIPLIER = "unitmultiplier";
    public static final String UNIT_SYMBOL = "unitsymbol";
    /**
     * 对标管理
     */
    public static final String BENCHMARK_SET = "benchmarkset";

    /**
     * 摄像头文件夹
     */
    public static final String CAMERA_FOLD = "camerafold";
    /**
     * 视频文件夹与文件关系
     */
    public static final String FOLD_CAMERA_ID = "foldcameraid";
    /**
     * 视频节点模型
     */
    public static final String CAMERA = "camera";

    /**
     * 视频监控模型
     */
    public static final String VIDEO_MONITOR = "videomonitor";

    /**
     * 签到组
     */
    public static final String REGISTRATION_GROUP = "registrationgroup";

    /**
     * 签到点
     */
    public static final String REGISTRATION_POINT = "registrationpoint";

    /**
     * 签到点设备
     */
    public static final String REGISTRATION_EQUIPMENT = "registrationequipment";

    /**
     * 签到点顺序
     */
    public static final String REGISTRATION_POINT_SEQUENCE = "registrationpointsequence";

    /**
     * 巡检参数详情
     */
    public static final String INSPECTION_SCHEME_DETAIL = "inspectionschemedetail";

    /**
     * 巡检方案
     */
    public static final String INSPECTION_SCHEME = "inspectionscheme";

    /**
     * 巡检参数
     */
    public static final String INSPECTION_PARAMETER = "inspectionparameter";

    /**
     * 设备巡检计划关联关系
     */
    public static final String DEVICE_PLAN_RELATIONSHIP = "deviceplanrelationship";

    /**
     * 巡检计划
     */
    public static final String PLAN_SHEET = "plansheet";
    /**
     * 工单审核暂存信息
     */
    public static final String WORK_ORDER_CHECK_INFO = "workordercheckinfo";
    /**
     *
     */
    public static final String PM_WORK_SHEET = "pmworksheet";

    /**
     * 签到点统计
     */
    public final static String SIGN_IN_STATISTICS_TABLE = "signinstatisticstable";

    /**
     * 值班状态
     */
    public final static String DUTY_STATUS = "dutystatus";

    /**
     * 工单状态
     */
    public final static String WORK_SHEET_STATUS = "worksheetstatus";

    /**
     * 交接班记录
     */
    public final static String SHIFTING_DUTY = "shiftingduty";

    /**
     * 工单故障原因
     */
    public final static String WORKSHEET_ABNORMAL_REASON = "worksheetabnormalreason";
    /**
     * 异常原因
     */
    public final static String ABNORMAL_REASON = "abnormalreason";
    /**
     * 流程日志
     */
    public final static String PROCESS_FLOW_UNIT = "processflowunit";

    /**
     * 维保项目组
     */
    public final static String MAINTENANCE_GROUP = "maintenancegroup";

    /**
     * 维保项目
     */
    public final static String MAINTENANCE_ITEM = "maintenanceitem";
    /**
     * 备件零件功能-系统
     */
    public final static String DEVICE_SYSTEM = "devicesystem";
    /**
     * 备件零件功能-设备
     */
    public final static String SPARE_PARTS_DEVICE = "sparepartsdevice";
    /**
     * 备件零件功能-备件
     */
    public final static String SPARE_PARTS_STORAGE = "sparepartsstorage";
    /**
     * 备件零件功能-备件更换记录
     */
    public final static String SPAREPARTS_REPLACERECORD = "sparepartsreplacerecord";
    /**
     * 备件零件功能-设备零件
     */
    public final static String DEVICE_COMPONENT = "devicecomponent";
    /**
     * 维保类型
     */
    public final static String MAINTENANCE_TYPE = "maintenancetype";
    /**
     * 维保类型
     */
    public final static String MAINTENANCE_TYPE_DEFINE = "maintenancetypedefine";
    /**
     * 任务等级
     */
    public final static String WORKSHEET_TASK_LEVEL = "worksheettasklevel";


    /**
     * 模板节点树表
     */
    public final static String TEMPLATE_NODE_TREE = "nodetemplatetree";

    /**
     * 模板分组表
     */
    public final static String RUNNING_PARAM_TEMPLATE_GROUP = "runningparamgroup";

    /**
     * 模板表
     */
    public final static String NODE_TEMPLATE = "nodetemplate";

    /**
     * 运行参数表
     */
    public final static String RUNNING_PARAM = "runningparam";

    /**
     * 技术参数TECH_PARAM_TEMPLATE模板表
     */
    public final static String TECH_PARAM_TEMPLATE = "techparamtemplate";

    /**
     * 运行参数节点树表
     */
    public final static String MEASURE_NODE_GROUP = "measurenodegroup";

    /**
     * 运行参数节点表
     */
    public final static String MEASURE_NODE = "measurenode";

    /**
     * 技术参数表
     */
    public final static String TECH_PARAM_DATA = "techparamdata";

    /**
     * 一段线/抽屉柜
     */
    public final static String LINE_SEGMENT = "linesegment";

    /**
     * 配电柜
     */
    public final static String POWER_DIS_CABINET = "powerdiscabinet";

    /**
     * 设备通用信息
     */
    public final static String DEVICE_COMMON_INFO = "devicecommoninfo";

    /**
     * 列头柜
     */
    public final static String ARRAY_CABINET = "arraycabinet";

    public final static String WORK_SHEET_TASK_TYPE = "worksheettasktype";

    public final static String WORK_ORDER_SOURCE_TYPE = "workordersourcetype";

    public final static String SIGN_IN_STATUS_RECORD = "signinstatusrecord";

    /**
     * 节点字段定义
     */
    public final static String NODE_FIELD_DEF = "nodefielddef";

    /**
     * 数据类型定义
     */
    public final static String DATA_TYPE_DEF = "datatypedef";

    /**
     * 房间类型
     */
    public final static String ROOM_TYPE = "roomtype";

    /**
     * 房间类型
     */
    public final static String REPAIR_TYPE = "repairtype";

    /**
     * 填报类型
     */
    public final static String FILL_FORM_TYPE = "fillformtype";

    public static final String PRODUCT = "product";
    public static final String DIM_ENERGY_EFFICIENCY_DATA = "dimenergyefficiencydata";

    /**
     * 多维度与分时能耗统计
     */
    public static final String MULTI_DIM_ENERGY_CONSUMPTION = "multidimenergyconsumption";

    public static final String ENERGY_EFFICIENCY_EXTREME_COUNT = "energyefficiencyextremecount";

    /**
     * 全国
     */
    public static final String COUNTRY = "country";

    /**
     * 能效数据
     */
    public static final String ENERGY_EFFICIENCY_DATA = "energyefficiencydata";

    /**
     *
     */
    public static final String OBJECT_COST_VALUE = "objectcostvalue";

    public static final String BILL = "bill";

    public static final String CHILDREN = "children";

    public static final String UNNATURALSET = "unnaturalset";

    /**
     * 单位用能成本
     */
    public static final String UNIT_OBJECT_COST_VALUE = "unitobjectcostvalue";
    /**
     * 分时成本
     */
    public static final String TS_OBJECT_COST_VALUE = "tsobjectcostvalue";
    /**
     * 平均电价
     */
    public static final String AVERAGE_ELECTRICITY_PRICE = "averageelectricityprice";

    /**
     * 其他单位类型
     */
    public static final String OTHER_UNIT_TYPE = "otherunittype";
    /**
     * 预测天气
     */
    public static final String WEATHER_PREDICT = "weatherpredict";
    /**
     * 能耗折算方案详情
     */
    public static final String ENERGY_CONVER_SCHEME_DETAIL = "energyconvertschemedetail";
    /**
     * 折算方案关联
     */
    public static final String ENERGY_CONVER_SCHEME_RELATIONSHIP = "energyconvertschemerelationship";
    /**
     * 能耗折算方案
     */
    public static final String ENERGY_CONVER_SCHEME = "energyconvertscheme";
    /**
     * 碳足迹指标模型
     */
    public static final String CARBON_CALCULATE_OPTION = "carboncalculateoption";

    /**
     * 节能措施
     */
    public static final String ENERGY_CONSERVATION_MEASURE = "energyconservationmeasure";

    /**
     * 跟进人员
     */
    public static final String FOLLOWUP_PEOPLE = "followuppeople";

    /**
     * EEM poi记录
     */
    public static final String EEM_POI_RECORD = "eempoirecord";

    /**
     * 日期类型
     */

    public static final String WORKRESTDAY_RECORDS = "workrestdayrecords";

    public static final String WEATHER = "weather";

    public static final String TENANT = "tenant";

    /**
     * 分时时段比
     */
    public static final String TIME_SHARE_PERIOD_RATIO = "timeshareperiodratio";

    /**
     * 设备连锁
     */
    public static final String DEVICE_CHAIN = "devicechain";

    /**
     * 预测数据
     */
    public static final String COLD_PREDICT = "coldpredict";

    /**
     * 设备连锁详情
     */
    public static final String DEVICE_CHAIN_DETAIL = "devicechaindetail";

    /**
     * 设备操作规则
     */
    public static final String DEVICE_OPERATION_RULE = "deviceoperationrule";

    /**
     * 制冷系统运行策略
     */
    public static final String REFRIGERATING_RUNNING_STRATEGY = "refrigeratingrunningstrategy";

    /**
     * 制冷系统
     */
    public static final String REFRIGERATING_SYSTEM = "refrigeratingsystem";

    /**
     * 制冷系统实际数据
     */
    public static final String COLD_ACTUAL = "coldactual";

    /**
     * 制冷系统实际数据
     */
    public static final String TIME_SHARE_PERIOD_RATIO_TYPE = "timeshareperiodratiotype";
    /**
     * 单位系数
     */
    public static final String UNITMULTIPLIER = "unitmultiplier";

    public static final String USER = "user";

    public static final String USER_GROUP = "usergroup";

    /**
     * 仪表台账信息表
     */
    public static final String METER_EXTEND_INFO = "meterextendinfo";
    /**
     * 仪表台账扩展属性
     */
    public static final String METER_PROPERTY_EXTEND = "meterpropertyextend";
    /**
     * 仪表台账动态属性表
     */
    public static final String METER_DYNAMIC_VALUE = "meterdynamicvalue";
    /**
     * 仪表检定记录
     */
    public static final String INSTRUMENT_INSPECT_RECORD = "instrumentinspectrecord";
}