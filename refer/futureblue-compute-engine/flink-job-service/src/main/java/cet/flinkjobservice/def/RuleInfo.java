package cet.flinkjobservice.def;

import cet.flinkjobservice.model.enums.RuleStatus;
import lombok.Data;

@Data
public class RuleInfo {

    /**
     * 主键 id
     */
    long id;

    /**
     * flink 作业对应的任务 id
     */
    String jobId;

    /**
     * flink 任务 job_name
     */
    String jobName;

    /**
     * 作业参数更新时间
     */
    long updateTime;

    /**
     * flink 作业规则参数
     */
    String argues;

    /**
     * flink 作业的坐标参数
     */
    String layout;

    /**
     * flink 作业别名
     */
    String alias;

    /**
     * 作业是否标记删除
     */
    Boolean isDelete;

    /**
     * 作业状态
     */
    RuleStatus status;
}
