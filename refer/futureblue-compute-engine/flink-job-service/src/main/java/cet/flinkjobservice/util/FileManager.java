package cet.flinkjobservice.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileFilter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.concurrent.TimeUnit;

public class FileManager {
    public static void main(String[] args) {
        // 创建文件管理器，设置最大保留 30 个文件，最大保存 30 天，删除.out结尾的文件
        FileFilter noOutFileFilter = (file) -> !file.getName().endsWith(".out");
        FileManager fileManager = new FileManager(
                "D:\\log", 30, 60L, noOutFileFilter);

        // 启动定时清理任务，每 1 小时执行一次清理操作
        fileManager.startCleaningTask(TimeUnit.HOURS.toMillis(1));
    }

    private static final Logger log = LoggerFactory.getLogger(FileManager.class);

    // 文件目录
    private String fileDir;

    // 保留的文件数量
    private int maxFileCount;

    // 设置文件存储的最大保存时长（单位：分钟）
    private long maxFileAgeInMinutes;

    // 文件需要满足的条件
    private FileFilter fileFilter;

    public FileManager(String fileDir, int maxFileCount, long maxFileAgeInMinutes, FileFilter fileFilter) {
        this.fileDir = fileDir;
        this.maxFileCount = maxFileCount;
        this.maxFileAgeInMinutes = maxFileAgeInMinutes;
        this.fileFilter = fileFilter;
    }

    // 清理文件
    public void cleanOldFiles() {
        log.info("正在清除目录: " + fileDir);
        File fileFolder = new File(fileDir);

        if (!fileFolder.exists() || !fileFolder.isDirectory()) {
            log.warn("文件目录不存在或不是一个目录: " + fileDir);
            return;
        }

        // 获取所有的文件
        File[] files = fileFolder.listFiles();

        if (files == null || files.length == 0) {
            log.info("目录中没有文件: " + fileDir);
            return;
        }

        // 遍历文件，删除不符合过滤条件的文件
        for (File file : files) {
            // 如果文件不符合过滤条件，删除该文件
            if (fileFilter != null && !fileFilter.accept(file)) {
                log.info("删除不符合过滤条件的文件: " + file.getName());
                deleteFile(file);
            }
        }

        // 按修改时间排序
        files = fileFolder.listFiles();
        Arrays.sort(files, Comparator.comparingLong(File::lastModified));

        // 清理过期的文件
        long currentTime = System.currentTimeMillis();
        for (File file : files) {
            // 计算文件年龄
            long ageInMilliseconds = currentTime - file.lastModified();
            long ageInMinutes = TimeUnit.MILLISECONDS.toMinutes(ageInMilliseconds);
            long ageInHours = TimeUnit.MILLISECONDS.toHours(ageInMilliseconds);
            long ageInDays = TimeUnit.MILLISECONDS.toDays(ageInMilliseconds);


            // 删除超过最大保存时长的文件
            if (ageInMinutes > maxFileAgeInMinutes) {
                String timeUnit = "分钟";
                long age = ageInMinutes;

                // 动态选择时间单位
                if (ageInDays > 0) {
                    timeUnit = "天";
                    age = ageInDays;
                } else if (ageInHours > 0) {
                    timeUnit = "小时";
                    age = ageInHours;
                }

                // 输出日志
                log.info("删除过期文件: " + file.getName() + " (最后修改于: " + age + " " + timeUnit + "前)");

                // 删除文件的逻辑
                deleteFile(file);
            }
        }

        // 清理超过最大数量的文件
        files = fileFolder.listFiles();
        Arrays.sort(files, Comparator.comparingLong(File::lastModified));
        if (files.length > maxFileCount) {
            int filesToDelete = files.length - maxFileCount;
            log.info("删除 " + filesToDelete + " 个旧的文件以保持最大文件数量限制。");
            for (int i = 0; i < filesToDelete; i++) {
                log.info("删除文件: " + files[i].getName());
                deleteFile(files[i]);
            }
        }
    }

    // 删除文件
    private void deleteFile(File file) {
        if (file.exists()) {
            // 如果是文件，直接删除
            if (file.isFile()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("文件删除成功: " + file.getName());
                } else {
                    log.warn("文件删除失败: " + file.getName());
                }
            }
            // 如果是目录，递归删除目录中的所有内容
            else if (file.isDirectory()) {
                // 获取目录中的所有文件和子目录
                File[] files = file.listFiles();
                if (files != null) {
                    for (File subFile : files) {
                        deleteFile(subFile);  // 递归删除目录中的每个文件或子目录
                    }
                }
                // 删除空目录
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("目录删除成功: " + file.getName());
                } else {
                    log.warn("目录删除失败: " + file.getName());
                }
            }
        } else {
            log.warn("文件或目录不存在: " + file.getName());
        }
    }


    // 启动定时清理任务
    public void startCleaningTask(long intervalInMillis) {
        // 定期清理文件
        Runnable cleanupTask = new Runnable() {
            @Override
            public void run() {
                cleanOldFiles();
            }
        };

        // 定时执行清理任务
        java.util.concurrent.Executors.newSingleThreadScheduledExecutor()
                .scheduleAtFixedRate(cleanupTask, 0, intervalInMillis, TimeUnit.MILLISECONDS);
    }
}
