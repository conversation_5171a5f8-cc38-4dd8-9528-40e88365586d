package com.cet.electric.flink.engine;

import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.client.program.PackagedProgram;
import org.apache.flink.client.program.PackagedProgramUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.runtime.jobgraph.JobGraph;
import org.apache.flink.runtime.minicluster.MiniCluster;
import org.apache.flink.runtime.minicluster.MiniClusterConfiguration;

import java.io.File;
import java.util.Base64;

/**
 * <AUTHOR>
 */
public class FlinkEngine {

    private static volatile FlinkEngine instance;

    private final MiniCluster miniCluster;

    public static FlinkEngine getInstance() {
        if (instance == null) {
            synchronized (FlinkEngine.class) {
                if (instance == null) {
                    instance = new FlinkEngine();
                }
            }
        }
        return instance;
    }

    private FlinkEngine() {
        Configuration config = new Configuration();
        config.setInteger(RestOptions.PORT, 8086);
        MiniClusterConfiguration miniClusterConfiguration = new MiniClusterConfiguration.Builder()
                .setConfiguration(config)
                .setNumTaskManagers(1)
                .setNumSlotsPerTaskManager(2)
                .build();
        miniCluster = new MiniCluster(miniClusterConfiguration);
        try {
            miniCluster.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public String runJob(String jarFilePath, String entryPointClass, String arguments) throws Exception {
        File jarFile = new File(jarFilePath);
        String encodedArgs = Base64.getEncoder().encodeToString(arguments.getBytes());
        String[] programArgs = new String[2];
        programArgs[0] = "--input";
        programArgs[1] = encodedArgs;
        PackagedProgram program = PackagedProgram.newBuilder()
                .setJarFile(jarFile)
                .setEntryPointClassName(entryPointClass)
                .setArguments(programArgs)
                .build();
        JobGraph jobGraph = PackagedProgramUtils.createJobGraph(
                program, new Configuration(),1, false);
        String jobId;
        JobExecutionResult jobExecutionResult = miniCluster.executeJobBlocking(jobGraph);
        jobId = jobExecutionResult.getJobID().toString();

        return jobId;
    }
}
