package com.cet.flinkjobservice.handler;

import cet.flinkjobservice.handler.FormulaHandler;
import com.googlecode.aviator.AviatorEvaluator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;

@RunWith(PowerMockRunner.class)
@PrepareForTest({FormulaHandler.class, AviatorEvaluator.class})
public class FormulaHandlerTest {

    @InjectMocks
    private FormulaHandler formulaHandler;

    @Before
    public void setUp() {
        // 模拟AviatorEvaluator以防止在测试中实际执行
        PowerMockito.mockStatic(AviatorEvaluator.class);
    }

    /**
     * 测试有效的字段公式
     */
    @Test
    public void testValidFieldFormula() {
        List<Map<String, String>> validTableArray = new ArrayList<>();
        Map<String, String> field1 = new HashMap<>();
        field1.put("field", "formula");
        field1.put("fieldType", "double");
        field1.put("frontedType", "number");
        field1.put("formula", "2+$devID#+8*7-9");

        Map<String, String> field2 = new HashMap<>();
        field2.put("field", "formula1");
        field2.put("fieldType", "double");
        field2.put("frontedType", "number");
        field2.put("formula", "2+$formula#+8*7-9+$devID#");

        // 模拟成功编译
        Mockito.when(AviatorEvaluator.compile(Mockito.anyString())).thenReturn(null);

        validTableArray.add(field1);
        validTableArray.add(field2);

        // 直接调用被测试的实例
        formulaHandler.validateFieldFormula(validTableArray);

        // 验证逻辑
        // 此处没有具体的验证代码，因为 validateFieldFormula 方法的内部逻辑没有返回值，不报错就说明成功
    }

    /**
     * 测试不支持的类型的无效字段公式
     */
    @Test
    public void testInvalidFieldFormulaUnsupportedType() {
        List<Map<String, String>> invalidTableArray = new ArrayList<>();
        Map<String, String> field = new HashMap<>();
        field.put("field", "formula");
        field.put("fieldType", "double");
        field.put("frontedType", "String");
        field.put("formula", "2+$devID#+8*7-9");

        invalidTableArray.add(field);

        // 调用实际的验证逻辑，使用断言捕获异常
        assertThrows(RuntimeException.class, () -> formulaHandler.validateFieldFormula(invalidTableArray));
    }

    /**
     * 测试空公式的字段
     */
    @Test
    public void testEmptyFormula() {
        List<Map<String, String>> tableArray = new ArrayList<>();
        Map<String, String> field = new HashMap<>();
        field.put("field", "formula");
        field.put("fieldType", "double");
        field.put("frontedType", "number");
        field.put("formula", "");

        tableArray.add(field);

        formulaHandler.validateFieldFormula(tableArray);

        // 此处没有具体的验证代码，因为 validateFieldFormula 方法的内部逻辑没有返回值
    }

    /**
     * 测试包含中文字符的无效字段公式
     */
    @Test
    public void testInvalidFieldFormulaWithChineseCharacters() {
        List<Map<String, String>> invalidTableArray = new ArrayList<>();
        Map<String, String> field = new HashMap<>();
        field.put("field", "formula");
        field.put("fieldType", "double");
        field.put("frontedType", "number");
        field.put("formula", "2+$devID#+8*7-9中文");

        invalidTableArray.add(field);

        // 调用实际的验证逻辑，使用断言捕获异常
        assertThrows(RuntimeException.class, () -> formulaHandler.validateFieldFormula(invalidTableArray));
    }

    /**
     * 测试包含未包装英文字符的无效字段公式
     */
    @Test
    public void testInvalidFieldFormulaWithUnwrappedEnglish() {
        List<Map<String, String>> invalidTableArray = new ArrayList<>();
        Map<String, String> field = new HashMap<>();
        field.put("field", "formula");
        field.put("fieldType", "double");
        field.put("frontedType", "number");
        field.put("formula", "2+$devID#+8*7-9*abc");

        invalidTableArray.add(field);

        // 调用实际的验证逻辑，使用断言捕获异常
        assertThrows(RuntimeException.class, () -> formulaHandler.validateFieldFormula(invalidTableArray));
    }

    /**
     * 测试循环引用
     */
    @Test
    public void testCircularReference() {
        List<Map<String, String>> circularTableArray = new ArrayList<>();
        Map<String, String> field1 = new HashMap<>();
        field1.put("field", "formula");
        field1.put("fieldType", "double");
        field1.put("frontedType", "number");
        field1.put("formula", "$formula1#");

        Map<String, String> field2 = new HashMap<>();
        field2.put("field", "formula1");
        field2.put("fieldType", "double");
        field2.put("frontedType", "number");
        field2.put("formula", "$formula#");

        circularTableArray.add(field1);
        circularTableArray.add(field2);

        // 调用实际的验证逻辑，使用断言捕获异常
        assertThrows(RuntimeException.class, () -> formulaHandler.validateFieldFormula(circularTableArray));
    }

    /**
     * 测试空字段表
     */
    @Test
    public void testEmptyFieldTable() {
        List<Map<String, String>> emptyTableArray = new ArrayList<>();

        // 调用实际的验证逻辑，使用断言捕获异常
        assertThrows(RuntimeException.class, () -> formulaHandler.validateFieldFormula(emptyTableArray));
    }

    /**
     * 测试编译异常的情况
     */
    @Test
    public void testInvalidFieldFormulaWithException() {
        List<Map<String, String>> invalidTableArray = new ArrayList<>();
        Map<String, String> field = new HashMap<>();
        field.put("field", "formula");
        field.put("fieldType", "double");
        field.put("frontedType", "number");
        field.put("formula", "2+$devID#+8*7-9");

        invalidTableArray.add(field);

        // 模拟AviatorEvaluator在调用compile时抛出异常
        Mockito.when(AviatorEvaluator.compile(Mockito.anyString())).thenThrow(new RuntimeException("Compilation error"));

        // 调用实际的验证逻辑，使用断言捕获异常
        assertThrows(RuntimeException.class, () -> formulaHandler.validateFieldFormula(invalidTableArray));
    }

    /**
     * 测试缺少运算符的公式
     */
    @Test
    public void testMissingOperatorInFormula() {
        List<Map<String, String>> invalidTableArray = new ArrayList<>();
        Map<String, String> field = new HashMap<>();
        field.put("field", "formula");
        field.put("fieldType", "double");
        field.put("frontedType", "number");
        field.put("formula", "$devID#$tempID#$humidID#");

        invalidTableArray.add(field);

        // 调用实际的验证逻辑，使用断言捕获异常
        assertThrows(RuntimeException.class, () -> formulaHandler.validateFieldFormula(invalidTableArray));
    }

    /**
     * 测试除数为0的公式
     */
    @Test
    public void testDivideByZeroInFormula() {
        List<Map<String, String>> invalidTableArray = new ArrayList<>();
        Map<String, String> field = new HashMap<>();
        field.put("field", "formula");
        field.put("fieldType", "double");
        field.put("frontedType", "number");
        field.put("formula", "$devID#/ 0");

        invalidTableArray.add(field);

        // 调用实际的验证逻辑，使用断言捕获异常
        assertThrows(RuntimeException.class, () -> formulaHandler.validateFieldFormula(invalidTableArray));
    }
}
