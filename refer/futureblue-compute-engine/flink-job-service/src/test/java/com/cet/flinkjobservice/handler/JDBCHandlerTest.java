package com.cet.flinkjobservice.handler;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import cet.flinkjobservice.handler.JDBCHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.sql.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DriverManager.class, JDBCHandler.class})
public class JDBCHandlerTest {

    @InjectMocks
    private JDBCHandler jdbcHandler;

    @Mock
    private Connection mockConnection;

    @Mock
    private PreparedStatement mockPreparedStatement;

    @Mock
    private ResultSet mockResultSet;

    @Before
    public void setUp() throws Exception {
        // Initialize mocks
        MockitoAnnotations.initMocks(this);

        // Mock static methods
        PowerMockito.mockStatic(DriverManager.class);
        when(DriverManager.getConnection(any(String.class), any(String.class), any(String.class))).thenReturn(mockConnection);

        // Set values for the fields using setters
        jdbcHandler.setSchemaName("public");
        jdbcHandler.setUser("postgres");
        jdbcHandler.setPassword("cet2024nbg?");
        jdbcHandler.setModelDataBaseURL("*******************************************************************************************************");
    }

    @Test
    public void testGetConflictColumnsString_WithUniqueColumns() throws Exception {
        // Arrange
        String tableName = "test_table";
        String expected = "column1,column2";

        when(mockConnection.prepareStatement(any(String.class))).thenReturn(mockPreparedStatement);
        when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true, true, false); // Simulate 2 results
        when(mockResultSet.getString("column_name")).thenReturn("column1", "column2");

        // Act
        String result = jdbcHandler.getConflictColumnsString(tableName);

        // Assert
        assertEquals(expected, result);

        // Verify interactions
        verify(mockPreparedStatement).setString(1, tableName);
        verify(mockPreparedStatement).setString(2, "public");
        verify(mockPreparedStatement).executeQuery();
        verify(mockResultSet, times(2)).getString("column_name");
    }

    @Test
    public void testGetConflictColumnsString_NoConflictColumns() throws Exception {
        // Arrange
        String tableName = "test_table";
        String expected = "";

        when(mockConnection.prepareStatement(any(String.class))).thenReturn(mockPreparedStatement);
        when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(false); // Simulate no results

        // Act
        String result = jdbcHandler.getConflictColumnsString(tableName);

        // Assert
        assertEquals(expected, result);
    }

    @Test(expected = SQLException.class)
    public void testGetConflictColumnsString_ConnectionError() throws Exception {
        // Arrange
        String tableName = "test_table";
        when(DriverManager.getConnection(any(String.class), any(String.class), any(String.class)))
                .thenThrow(new SQLException("Connection error"));

        // Act
        jdbcHandler.getConflictColumnsString(tableName);
    }
}