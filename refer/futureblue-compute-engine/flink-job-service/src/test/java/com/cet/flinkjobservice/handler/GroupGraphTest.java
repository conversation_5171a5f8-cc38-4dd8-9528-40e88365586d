package com.cet.flinkjobservice.handler;

import cet.flinkjobservice.handler.GroupGraph;
import com.google.common.collect.Multimap;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class GroupGraphTest {

    private GroupGraph groupGraph;

    @Before
    public void setUp() {
        groupGraph = new GroupGraph();
    }

    /**
     * 测试添加边的功能
     */
    @Test
    public void testAddEdge() {
        groupGraph.addEdge("A", "A");
        groupGraph.addEdge("A", "C");
        groupGraph.addEdge("B", "D");

        // 检查边是否正确添加
        Multimap<String, String> map = groupGraph.map;

        assertTrue(map.containsEntry("A", "A"));
        assertTrue(map.containsEntry("A", "C"));
        assertTrue(map.containsEntry("B", "D"));
    }

    /**
     * 测试图的分组功能
     */
    @Test
    public void testGroupGraph() {
        groupGraph.addEdge("A", "B");
        groupGraph.addEdge("B", "C");
        groupGraph.addEdge("C", "D");
        groupGraph.addEdge("E", "F");
        groupGraph.addEdge("F", "E"); // 添加环以验证分组
        groupGraph.addEdge("P", "B");
        groupGraph.addEdge("H", "K");
        groupGraph.addEdge("1", "2");

        Multimap<String, String> result = groupGraph.groupGraph();

        // 检查分组后的结果
        System.out.println(result); // 输出结果以便调试
        // 以下期望值是示例，您可能需要根据addEdge和groupGraph的逻辑调整期望值
        assertTrue(result.keySet().contains("A"));
        assertTrue(result.keySet().contains("E")); // 'E' 应该被包括在结果中，因为它与 'F' 短路
    }

    /**
     * 测试图的分组功能，确认当无连接的节点时正确处理
     */
    @Test
    public void testGroupGraphWithDisconnectedNodes() {
        groupGraph.addEdge("A", "B");
        groupGraph.addEdge("C", "D");

        Multimap<String, String> result = groupGraph.groupGraph();

        // 验证 'A' 和 'C' 的分组情况
        assertTrue(result.keySet().contains("A"));
        assertTrue(result.keySet().contains("C"));
        assertEquals(2, result.keySet().size());
    }

    /**
     * 测试双向连接的情况
     */
    @Test
    public void testAddBiDirectionalEdges() {
        groupGraph.addEdge("A", "B");
        groupGraph.addEdge("B", "A"); // 添加反向边

        Multimap<String, String> result = groupGraph.groupGraph();

        // 检查双向连接的节点
        assertTrue(result.containsKey("A"));
    }
}