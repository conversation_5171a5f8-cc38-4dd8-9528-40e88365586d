package com.cet.flinkjobservice.handler;

import cet.flinkjobservice.def.RuleInfo;
import cet.flinkjobservice.handler.JDBCHandler;
import cet.flinkjobservice.handler.JsonHandler;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

import cet.flinkjobservice.model.enums.RuleStatus;
import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class JsonHandlerTest {

    @InjectMocks
    private JsonHandler jsonHandler;

    @Mock
    private JDBCHandler jdbcHandler;

    private static final String FLINK_TASK_MODE_SAVE = "save";
    private static final String FLINK_TASK_MODE_RUN = "run";
    private static final String FLINK_TASK_MODE_DEBUG = "debug";
    private static final String SINK = "sink";
    private static final String TRANS = "trans";
    private static final String SOURCE = "source";
    private static final String RESOURCE = "resource";
    private static final String JOB_NAME = "jobName";
    private static final String JOBMANAGER = "jobmanager";
    private static final String TASKMANAGER = "taskmanager";
    private static final String PARALLELISM = "parallelism";
    private static final String TYPE = "type";
    private static final String CONFLICT_COLUMNS = "conflictColumns";
    private static final String FIELDS_ARRAY = "fieldsArray";
    private static final String BATCH_SIZE = "batchSize";
    private static final String BATCH_TIME_MS = "batchTimeMs";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testProcessJson_Success_SaveMode() throws Exception {
        // 定义输入 JSON，包含 jobName
        String jsonString = "[{\"resource\": {\"jobName\": \"testJob\"}}]";

        // 创建一个期望的资源节点
        JSONObject expectedResourceNode = new JSONObject();
        expectedResourceNode.put(JOB_NAME, "testJob");
        expectedResourceNode.put(JOBMANAGER, "1024M");
        expectedResourceNode.put(TASKMANAGER, "1024M");
        expectedResourceNode.put(PARALLELISM, 1);

        // 创建最终的 JSON 数组，将期望的资源节点添加到数组中
        JSONArray expectedJsonArray = new JSONArray();
        JSONObject firstObject = new JSONObject();
        firstObject.put(RESOURCE, expectedResourceNode);
        expectedJsonArray.add(firstObject);

        // 调用被测试的方法
        String result = jsonHandler.processJson(jsonString, FLINK_TASK_MODE_SAVE);

        // 将 result 转换为 JSONArray 进行比较
        JSONArray resultJsonArray = JSONArray.parseArray(result);

        // 断言最终结果与预期一致
        assertEquals(expectedJsonArray.toJSONString(), resultJsonArray.toJSONString());
    }

    @Test
    public void testProcessJson_ThrowsException_IfSourceNodeIsMissing() {
        String jsonString = "[{}]"; // no SOURCE node
        try {
            jsonHandler.processJson(jsonString, FLINK_TASK_MODE_RUN);
            fail("Expected IllegalArgumentException for missing source node");
        } catch (IllegalArgumentException e) {
            assertEquals("输入节点不能没有!", e.getMessage());
        } catch (Exception e) {
            fail("Expected IllegalArgumentException but got a different exception: " + e.getMessage());
        }
    }

    @Test
    public void testProcessJson_ThrowsException_IfSinkNodeIsMissing() {
        String jsonString = "[{\"source\": []}]"; // no SINK node
        try {
            jsonHandler.processJson(jsonString, FLINK_TASK_MODE_RUN);
            fail("Expected IllegalArgumentException for missing sink node");
        } catch (IllegalArgumentException e) {
            assertEquals("输入输出节点不能缺少且必须进行设置!", e.getMessage());
        } catch (Exception e) {
            fail("Expected IllegalArgumentException but got a different exception: " + e.getMessage());
        }
    }

    @Test
    public void testProcessJson_UpdateResourceProperties() throws Exception {
        // 定义输入 JSON 字符串，包含 jobName
        String jsonString = "[{\"resource\": {\"jobName\": \"exampleJob\"}}]";

        // 创建一个资源节点，设置预期的值
        JSONObject expectedResourceNode = new JSONObject();
        expectedResourceNode.put(JOB_NAME, "exampleJob");
        expectedResourceNode.put(JOBMANAGER, "1024M");
        expectedResourceNode.put(TASKMANAGER, "1024M");
        expectedResourceNode.put(PARALLELISM, 1);

        // 创建最终的 JSON 数组，并将预期的资源节点添加到数组中
        JSONArray expectedJsonArray = new JSONArray();
        JSONObject firstObject = new JSONObject();
        firstObject.put(RESOURCE, expectedResourceNode);
        expectedJsonArray.add(firstObject);

        // 调用被测试的方法
        String result = jsonHandler.processJson(jsonString, FLINK_TASK_MODE_SAVE);

        // 将 result 转换为 JSONObject 进行比较（JSON 格式比较）
        JSONArray resultJsonArray = JSONArray.parseArray(result);

        // 断言最终结果与预期一致
        assertEquals(expectedJsonArray.toJSONString(), resultJsonArray.toJSONString());
    }

    @Test
    public void testProcessJson_Fails_WhenJobNameIsMissing() {
        String jsonString = "[{\"resource\": {}}]"; // job name missing
        try {
            jsonHandler.processJson(jsonString, FLINK_TASK_MODE_SAVE);
            fail("Expected IllegalArgumentException for missing job name");
        } catch (IllegalArgumentException e) {
            assertEquals("jobName can not be null!", e.getMessage());
        } catch (Exception e) {
            fail("Expected IllegalArgumentException but got a different exception: " + e.getMessage());
        }
    }

    @Test
    public void testProcessJson_ThrowsException_NoJobName() {
        String jsonString = "[{\"resource\": {}}]"; // 没有 jobName
        try {
            jsonHandler.processJson(jsonString, FLINK_TASK_MODE_SAVE);
            fail("Expected IllegalArgumentException for missing job name");
        } catch (IllegalArgumentException e) {
            assertEquals("jobName can not be null!", e.getMessage());
        } catch (Exception e) {
            fail("Expected IllegalArgumentException but got a different exception: " + e.getMessage());
        }
    }

    @Test
    public void testProcessJson_InvalidSourceNode() {
        String jsonString = "[{\"source\": null}]"; // 无效的 source
        try {
            jsonHandler.processJson(jsonString, FLINK_TASK_MODE_RUN);
            fail("Expected IllegalArgumentException for invalid source node");
        } catch (Exception e) {
            assertEquals("输入节点不能没有!", e.getMessage());
        }
    }

    @Test
    public void testProcessJson_EmptySourceNode() {
        String jsonString = "[{\"source\": []}]"; // 空的 source
        try {
            jsonHandler.processJson(jsonString, FLINK_TASK_MODE_RUN);
            fail("Expected IllegalArgumentException for empty source node");
        } catch (Exception e) {
            assertEquals("输入输出节点不能缺少且必须进行设置!", e.getMessage());
        }
    }

    @Test
    public void testProcessJson_ThrowsException_IfResourceNodeIsEmpty() {
        String jsonString = "[{\"resource\": {}}]"; // 资源节点为空
        try {
            jsonHandler.processJson(jsonString, FLINK_TASK_MODE_SAVE);
            fail("Expected IllegalArgumentException for empty resource node");
        } catch (Exception e) {
            assertEquals("jobName can not be null!", e.getMessage());
        }
    }

    @Test
    public void testProcessJson_ValidJsonWithNullValues() throws Exception {
        String jsonString = "[{\"resource\":{\"jobName\":\"testJob\",\"jobmanager\":\"1024M\",\"taskmanager\":\"1024M\",\"parallelism\":1},\"sink\":[{}],\"source\":[{}]}]";

        String result = jsonHandler.processJson(jsonString, FLINK_TASK_MODE_SAVE);
        JSONArray resultJsonArray = JSONArray.parseArray(result);

        // 验证返回结果
        assertEquals(jsonString, resultJsonArray.toJSONString());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testProcessJson_RunModeWithNullSource() throws Exception {
        // 每个测试方法内重新声明 jsonArray 和 jsonObject
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(SINK, new JSONArray());
        jsonObject.put(TRANS, new JSONArray());
        jsonObject.put(SOURCE, null);  // 将 source 设置为 null
        jsonObject.put(RESOURCE, new JSONObject());
        jsonArray.add(jsonObject);

        // 当 source 节点为 null 时，应抛出 IllegalArgumentException 异常
        String jsonString = jsonArray.toJSONString();
        jsonHandler.processJson(jsonString, FLINK_TASK_MODE_RUN);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testProcessJson_RunModeWithNullSink() throws Exception {
        // 每个测试方法内重新声明 jsonArray 和 jsonObject
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(SINK, null);  // 将 sink 设置为 null
        jsonObject.put(TRANS, new JSONArray());
        jsonObject.put(SOURCE, new JSONArray());
        jsonObject.put(RESOURCE, new JSONObject());
        jsonArray.add(jsonObject);

        // 当 sink 节点为 null 时，应抛出 IllegalArgumentException 异常
        String jsonString = jsonArray.toJSONString();
        jsonHandler.processJson(jsonString, FLINK_TASK_MODE_RUN);
    }

    @Test
    public void testProcessJson_DebugMode() throws Exception {
        // 每个测试方法内重新声明 jsonArray 和 jsonObject
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(SINK, new JSONArray());
        jsonObject.put(TRANS, new JSONArray());
        jsonObject.put(SOURCE, new JSONArray());
        jsonObject.put(RESOURCE, new JSONObject());
        jsonArray.add(jsonObject);

        // 模拟 debug 模式
        String jsonString = jsonArray.toJSONString();
        String result = jsonHandler.processJson(jsonString, FLINK_TASK_MODE_DEBUG);

        // 验证是否正确处理了 source、sink、trans 节点
        assertNotNull(result);
        assertTrue(result.contains("sink"));
        assertTrue(result.contains("source"));
        assertTrue(result.contains("trans"));
    }

    /**
     * 测试 javaType 为 null 时，返回对应的 Java 类型
     */
    @Test
    public void testIsCompatibleOrMapTypeWhenJavaTypeIsNull() {
        String pgType = "integer";
        String expected = "bigdecimal,double,float,long,int,Integer,short,byte";
        String actual = jsonHandler.isCompatibleOrMapType(null, pgType);

        assertEquals(expected, actual);
    }

    /**
     * 测试 javaType 与 pgType 完全相同的情况
     */
    @Test
    public void testIsCompatibleOrMapTypeWhenTypesAreEqual() {
        String javaType = "int";
        String pgType = "int";
        String expected = "true";
        String actual = jsonHandler.isCompatibleOrMapType(javaType, pgType);

        assertEquals(expected, actual);
    }

    /**
     * 测试 javaType 兼容 pgType 的情况
     */
    @Test
    public void testIsCompatibleOrMapTypeWhenJavaTypeIsCompatible() {
        String javaType = "int";
        String pgType = "smallint";
        String expected = "true";
        String actual = jsonHandler.isCompatibleOrMapType(javaType, pgType);

        assertEquals(expected, actual);
    }

    /**
     * 测试 javaType 不兼容 pgType 的情况
     */
    @Test
    public void testIsCompatibleOrMapTypeWhenJavaTypeIsNotCompatible() {
        String javaType = "String";
        String pgType = "boolean";
        String expected = "false";
        String actual = jsonHandler.isCompatibleOrMapType(javaType, pgType);

       assertEquals(expected, actual);
    }

    /**
     * 测试加入复杂类型映射
     */
    @Test
    public void testIsCompatibleOrMapTypeWithComplexTypeMapping() {
        String javaType = null; // null 代表需要进行类型映射
        String pgType = "jsonb";
        String expected = "jsonobject,jsonarray,String"; // 期望的 Java 类型
        String actual = jsonHandler.isCompatibleOrMapType(javaType, pgType);

        assertEquals(expected, actual);
    }

    /**
     * 测试不匹配类型时，返回 false
     */
    @Test
    public void testIsCompatibleOrMapTypeWhenTypesDoNotMatch() {
        String javaType = "double";
        String pgType = "boolean";
        String expected = "false";
        String actual = jsonHandler.isCompatibleOrMapType(javaType, pgType);

        assertEquals(expected, actual);
    }

    /**
     * 测试对未知 PostgreSQL 类型的处理，期望返回 null
     */
    @Test
    public void testIsCompatibleOrMapTypeWhenPgTypeUnknown() {
        String javaType = null; // null 代表需要进行类型映射
        String pgType = "unknownType";
        String expected = null; // 期望没有返回
        String actual = jsonHandler.isCompatibleOrMapType(javaType, pgType);

        assertEquals(expected, actual);
    }

    /**
     * 测试生成 JSON 内容的情况，规则信息列表不为空。
     */
    @Test
    public void testGenerateJsonContentWithNonEmptyList() {
        // 准备测试数据
        List<RuleInfo> ruleInfoList = new ArrayList<>();

        // 创建示例 RuleInfo 对象
        RuleInfo ruleInfo1 = new RuleInfo();
        ruleInfo1.setId(1);
        ruleInfo1.setJobId("job1");
        ruleInfo1.setJobName("Job Name 1");
        ruleInfo1.setUpdateTime(System.currentTimeMillis());
        ruleInfo1.setArgues("arg1");
        ruleInfo1.setLayout("layout1");
        ruleInfo1.setAlias("alias1");
        ruleInfo1.setIsDelete(false);
        ruleInfo1.setStatus(RuleStatus.ADDED);

        RuleInfo ruleInfo2 = new RuleInfo();
        ruleInfo2.setId(2);
        ruleInfo2.setJobId("job2");
        ruleInfo2.setJobName("Job Name 2");
        ruleInfo2.setUpdateTime(System.currentTimeMillis());
        ruleInfo2.setArgues("arg2");
        ruleInfo2.setLayout("layout2");
        ruleInfo2.setAlias("alias2");
        ruleInfo2.setIsDelete(true);
        ruleInfo2.setStatus(RuleStatus.CANCELED);

        ruleInfoList.add(ruleInfo1);
        ruleInfoList.add(ruleInfo2);

        // 调用方法
        String actualJson = jsonHandler.generateJsonContent(ruleInfoList);

        // 构建预期结果
        JSONArray expectedJsonArray = new JSONArray();

        for (RuleInfo ruleInfo : ruleInfoList) {
            JSONObject expectedJson = new JSONObject();
            expectedJson.put("id", ruleInfo.getId());
            expectedJson.put("jobId", ruleInfo.getJobId());
            expectedJson.put("jobName", ruleInfo.getJobName());
            expectedJson.put("updateTime", ruleInfo.getUpdateTime());
            expectedJson.put("argues", ruleInfo.getArgues());
            expectedJson.put("layout", ruleInfo.getLayout());
            expectedJson.put("alias", ruleInfo.getAlias());
            expectedJson.put("isDelete", ruleInfo.getIsDelete());
            expectedJson.put("status", ruleInfo.getStatus().toString()); // 规则状态字符串化

            expectedJsonArray.add(expectedJson);
        }

        String expectedJson = JSON.toJSONString(expectedJsonArray, true);

        // 使用断言比较结果
        Assert.assertEquals(expectedJson, actualJson);
    }

    /**
     * 测试生成 JSON 内容的情况，规则信息列表为空。
     */
    @Test
    public void testGenerateJsonContentWithEmptyList() {
        // 准备测试数据
        List<RuleInfo> ruleInfoList = new ArrayList<>();

        // 调用方法
        String actualJson = jsonHandler.generateJsonContent(ruleInfoList);

        // 预期结果为空的 JSON 数组
        String expectedJson = JSON.toJSONString(new ArrayList<>(), true);

        // 使用断言比较结果
        Assert.assertEquals(expectedJson, actualJson);
    }


    /*----------------------------------------测试用例分隔符------------------------------------------------*/

    /**
     * 测试提取 freeSlots 的情况，提供有效的 JSON 响应。
     */
    @Test
    public void testExtractFreeSlotsFromJsonResponse() throws Exception {
        // 准备有效的 JSON 响应
        String jsonResponse = "{ \"taskmanagers\": [" +
                "{ \"freeSlots\": 5, \"slotsNumber\": 10 }]" +
                "}";

        // 调用方法
        int freeSlots = jsonHandler.extractFreeSlotsFromJsonResponse(jsonResponse);

        // 进行断言
        Assert.assertEquals(5, freeSlots);
    }

    /**
     * 测试提取 freeSlots 的情况，提供空的 taskManagers 数组。
     */
    @Test(expected = Exception.class)
    public void testExtractFreeSlotsFromJsonResponse_EmptyTaskManagers() throws Exception {
        // 准备空的 taskmanagers 数组的 JSON 响应
        String jsonResponse = "{ \"taskmanagers\": [] }";

        // 调用方法
        jsonHandler.extractFreeSlotsFromJsonResponse(jsonResponse);
    }

    /**
     * 测试提取 slotsNumber 的情况，提供有效的 JSON 响应。
     */
    @Test
    public void testExtractSlotsNumberFromJsonResponse() throws Exception {
        // 准备有效的 JSON 响应
        String jsonResponse = "{ \"taskmanagers\": [" +
                "{ \"freeSlots\": 5, \"slotsNumber\": 10 }]" +
                "}";

        // 调用方法
        int slotsNumber = jsonHandler.extractSlotsNumberFromJsonResponse(jsonResponse);

        // 进行断言
        Assert.assertEquals(10, slotsNumber);
    }

    /**
     * 测试提取 slotsNumber 的情况，提供空的 taskManagers 数组。
     */
    @Test(expected = Exception.class)
    public void testExtractSlotsNumberFromJsonResponse_EmptyTaskManagers() throws Exception {
        // 准备空的 taskmanagers 数组的 JSON 响应
        String jsonResponse = "{ \"taskmanagers\": [] }";

        // 调用方法
        jsonHandler.extractSlotsNumberFromJsonResponse(jsonResponse);
    }

    /**
     * 测试提取 freeSlots 和 slotsNumber 的情况，提供无效的 JSON 响应。
     */
    @Test(expected = Exception.class)
    public void testExtractFreeSlotsFromInvalidJsonResponse() throws Exception {
        // 准备无效的 JSON 响应
        String jsonResponse = "invalid json string";

        // 调用方法
        jsonHandler.extractFreeSlotsFromJsonResponse(jsonResponse);
    }

    @Test(expected = Exception.class)
    public void testExtractSlotsNumberFromInvalidJsonResponse() throws Exception {
        // 准备无效的 JSON 响应
        String jsonResponse = "invalid json string";

        // 调用方法
        jsonHandler.extractSlotsNumberFromJsonResponse(jsonResponse);
    }

}
