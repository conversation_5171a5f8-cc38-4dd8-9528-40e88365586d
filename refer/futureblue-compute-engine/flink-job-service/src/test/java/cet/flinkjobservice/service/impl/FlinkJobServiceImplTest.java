package cet.flinkjobservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cet.engine.common.model.TableColumn;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class FlinkJobServiceImplTest {
    @Test
    public void testGetModelPropertyList() {
        //物理量
        TableColumn tableColumn1 = new TableColumn("设备id", "deviceid", "long");
        TableColumn tableColumn2 = new TableColumn("数据id", "dataid", "long");
        TableColumn tableColumn3 = new TableColumn("回路号id", "logicalindex", "long");
        TableColumn tableColumn4 = new TableColumn("通道id", "channelid", "long");
        TableColumn tableColumn5 = new TableColumn("场站id", "stationid", "long");
        TableColumn tableColumn6 = new TableColumn("物理量id", "quantityobject_id", "long");
        TableColumn tableColumn7 = new TableColumn("能源类型", "energytype", "int");
        TableColumn tableColumn8 = new TableColumn("官网id", "monitoreid", "long");
        TableColumn tableColumn9 = new TableColumn("官网标签", "monitorlabel", "string");
        TableColumn tableColumn10 = new TableColumn("路径", "path", "string");
        TableColumn tableColumn11 = new TableColumn("层级", "level", "int");
        TableColumn tableColumn12 = new TableColumn("比例", "rate", "double");
        TableColumn tableColumn13 = new TableColumn("组织层级id", "orgid", "long");
        TableColumn tableColumn14 = new TableColumn("组织层级标签", "orglabel", "string");
        List<TableColumn> tableColumns = Lists.newArrayList(tableColumn1, tableColumn2, tableColumn3, tableColumn4, tableColumn5, tableColumn6, tableColumn7,
                tableColumn8, tableColumn9, tableColumn10, tableColumn11, tableColumn12, tableColumn13, tableColumn14);
        System.out.println(JSONObject.toJSONString(tableColumns));
        //能耗
        tableColumn1 = new TableColumn("设备id", "deviceid", "long");
        tableColumn2 = new TableColumn("数据id", "dataid", "long");
        tableColumn3 = new TableColumn("回路号id", "logicalindex", "long");
        tableColumn4 = new TableColumn("通道id", "channelid", "long");
        tableColumn5 = new TableColumn("场站id", "stationid", "long");
        tableColumn6 = new TableColumn("物理量id", "quantityobject_id", "long");
        tableColumn8 = new TableColumn("官网id", "monitoreid", "long");
        tableColumn9 = new TableColumn("官网标签", "monitorlabel", "string");
        tableColumn10 = new TableColumn("路径", "path", "string");
        tableColumn12 = new TableColumn("比例", "rate", "double");
        tableColumn13 = new TableColumn("组织层级id", "orgid", "long");
        tableColumn14 = new TableColumn("组织层级标签", "orglabel", "string");
        tableColumns = Lists.newArrayList(tableColumn1, tableColumn2, tableColumn3, tableColumn4, tableColumn5, tableColumn6,
                tableColumn8, tableColumn9, tableColumn10, tableColumn12, tableColumn13, tableColumn14);
        System.out.println(JSONObject.toJSONString(tableColumns));
    }
}
