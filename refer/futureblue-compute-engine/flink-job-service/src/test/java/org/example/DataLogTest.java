package org.example;

import cet.flinkjobservice.def.Datalog;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;

public class DataLogTest {
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException, NoSuchMethodException, InvocationTargetException, NoSuchFieldException, InvocationTargetException {

        Datalog data = new Datalog();
        Class<? extends Datalog> c = data.getClass();

        //获取属性值和类型
        Field[] fields1=c.getDeclaredFields();
        System.out.println("getDeclaredFields：");
        for (Field f:fields1){
            f.setAccessible(true);

            System.out.println(f.getName()+": " + f.getType());

            boolean annotation = f.isAnnotationPresent(ApiModelProperty.class);
            // true 是含有 含有再获取添加到list中
            if (annotation) {
                String value = f.getAnnotation(ApiModelProperty.class).value();
//                list.add(value);
                System.out.println(f.getName()+": " + f.getType() + ": " + value);
            }

        }


    }

}
