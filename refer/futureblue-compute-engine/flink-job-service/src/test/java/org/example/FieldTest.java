package org.example;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class FieldTest {

    public static void main(String[] args) {
        String s = "{\"objectId\":\"int\", \"objectLabel\":\"string\",\"energyType\":\"int\",\"aggregationCycle\":\"int\",\"value\":\"double\"}";
        JSONObject fields = new JSONObject()
                .fluentPut("computeField", "aaa")
                .fluentPut("computeFormula", "(a+b)");
        getFields(JSONObject.parseObject(s), fields);
    }

    public static JSONArray getFields(JSONObject json, JSONObject fields) {
        JSONArray array = new JSONArray();
        Object old = json.clone();
        if (fields.get("computeField") != null) {
            Object field1 = fields.get("computeField");
            json.put((String) field1,"string");
        }

        array.add(old);
        array.add(json);

        return array;
    }
}
