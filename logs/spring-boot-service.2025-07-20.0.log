2025-07-20 14:10:58.077 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-20 14:10:58.081 - INFO - [TaskSchedule.java:45] : 开始删除项目
2025-07-20 14:10:58.098 - INFO - [TaskSchedule.java:51] : 没有需要清理的已删除项目记录
2025-07-20 14:10:58.099 - INFO - [TaskSchedule.java:97] : 开始删除仿真任务
2025-07-20 14:10:58.184 - INFO - [TaskSchedule.java:103] : 没有需要清理的已删除仿真任务
2025-07-20 14:10:58.185 - INFO - [TaskSchedule.java:158] : 开始清理null文件夹
2025-07-20 14:11:03.430 - INFO - [TaskSchedule.java:165] : 成功清理0个null文件夹
2025-07-20 14:11:03.431 - INFO - [TaskSchedule.java:171] : null文件夹清理完毕
2025-07-20 14:11:03.431 - INFO - [TaskSchedule.java:42] : 删除完毕
2025-07-21 10:57:29.150 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplicationTests on DESKTOP-9LFKQ75 with PID 33200 (started by dell in D:\CETWorkSpace\ngap-server)
2025-07-21 10:57:29.154 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-21 10:57:30.834 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 10:57:31.385 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-21 10:57:31.489 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-21 10:57:31.952 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-21 10:57:32.135 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-21 10:57:32.169 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-21 10:57:32.189 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-21 10:57:32.224 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-21 10:57:32.428 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-21 10:57:32.454 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-21 10:57:32.455 - INFO - [TaskSchedule.java:45] : 开始删除项目
2025-07-21 10:57:32.458 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplicationTests in 3.833 seconds (JVM running for 11.054)
2025-07-21 10:57:32.472 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-21 10:57:32.491 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 10:57:32.491 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-21 10:57:32.492 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-21 10:57:32.493 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-21 10:57:32.665 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-21 10:57:32.666 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-21 10:57:32.667 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-21 10:57:32.680 - INFO - [TaskSchedule.java:51] : 没有需要清理的已删除项目记录
2025-07-21 10:57:32.681 - INFO - [TaskSchedule.java:97] : 开始删除仿真任务
2025-07-21 10:57:32.735 - INFO - [TaskSchedule.java:103] : 没有需要清理的已删除仿真任务
2025-07-21 10:57:32.735 - INFO - [TaskSchedule.java:159] : 开始清理null文件夹
2025-07-21 10:57:32.736 - INFO - [TaskSchedule.java:179] : 开始在data目录中清理null文件夹: D:\CETWorkSpace\ngap-server\data
2025-07-21 10:57:33.749 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.750 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.750 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.773 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=1
2025-07-21 10:57:33.775 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 1, 指标数量: 2
2025-07-21 10:57:33.820 - INFO - [TaskSchedule.java:181] : 成功清理0个null文件夹
2025-07-21 10:57:33.820 - INFO - [TaskSchedule.java:187] : null文件夹清理完毕
2025-07-21 10:57:33.820 - INFO - [TaskSchedule.java:42] : 删除完毕
2025-07-21 10:57:33.880 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.880 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.880 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.916 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=1, queryDTO=UserVoltageReportQueryDTO(keyword=用户, pageNum=1, pageSize=10)
2025-07-21 10:57:33.916 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 1, 用户数量: 2
2025-07-21 10:57:33.923 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.924 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.924 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.925 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=测试仿真
2025-07-21 10:57:33.925 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-07-21 10:57:33.933 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.933 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.934 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.934 - INFO - [SimulationController.java:183] : 接收到获取仿真策略列表请求
2025-07-21 10:57:33.934 - INFO - [SimulationController.java:186] : 仿真策略列表获取成功，总数量: 2
2025-07-21 10:57:33.940 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.940 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.940 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.940 - INFO - [SimulationController.java:118] : 接收到删除仿真任务请求: simulationId=1
2025-07-21 10:57:33.941 - INFO - [SimulationController.java:122] : 仿真任务删除成功，ID: 1
2025-07-21 10:57:33.946 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.946 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.946 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.948 - INFO - [SimulationController.java:244] : 接收到获取仿真任务指标数据请求: simulationId=1, metricName=voltage
2025-07-21 10:57:33.948 - INFO - [SimulationController.java:249] : 获取仿真任务指标数据成功，任务ID: 1, 指标名称: voltage, 数据点数量: 3
2025-07-21 10:57:33.955 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.955 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.955 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.958 - INFO - [SimulationController.java:156] : 接收到上传文件请求，文件名：test.dss, 文件类型：dss, 文件大小：17
2025-07-21 10:57:33.958 - INFO - [SimulationController.java:162] : 文件上传成功，保存路径：/path/to/uploaded/file
2025-07-21 10:57:33.964 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.964 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.965 - INFO - [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-07-21 10:57:33.967 - INFO - [SimulationController.java:276] : 接收到导出仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricExportDTO(pic=chart, metricIds=[voltage, current], startTimestamp=1000)
2025-07-21 10:57:33.972 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.973 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.973 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.973 - INFO - [SimulationController.java:206] : 接收到导入仿真任务请求，文件名：simulation.json, 文件大小：36
2025-07-21 10:57:33.974 - INFO - [SimulationController.java:210] : 仿真任务导入成功，仿真任务ID：1
2025-07-21 10:57:33.979 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.979 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.979 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.981 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricQueryDTO(metricIds=[voltage, current], startTimestamp=1000)
2025-07-21 10:57:33.981 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 1, 指标数量: 2
2025-07-21 10:57:33.988 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.988 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.988 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.988 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=4
2025-07-21 10:57:33.991 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 4, 总数据点: 1000, 合格数据点: 985, 合格率: 98.50%
2025-07-21 10:57:33.998 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:33.999 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:33.999 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:33.999 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=1
2025-07-21 10:57:33.999 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 1, 总数据点: 10000, 合格数据点: 8550, 合格率: 85.50%
2025-07-21 10:57:34.005 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.005 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.005 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.005 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=3
2025-07-21 10:57:34.006 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 3, 总数据点: 100, 合格数据点: 75, 合格率: 75.00%
2025-07-21 10:57:34.011 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.011 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.012 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.012 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=1
2025-07-21 10:57:34.012 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 1
2025-07-21 10:57:34.018 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.018 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.018 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.018 - INFO - [SimulationController.java:82] : 接收到重命名仿真任务请求: simulationId=1, newName=新仿真名称
2025-07-21 10:57:34.020 - INFO - [SimulationController.java:86] : 仿真任务重命名成功，ID: 1, 新名称: 新仿真名称
2025-07-21 10:57:34.026 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.026 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.027 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.027 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=  测试仿真  
2025-07-21 10:57:34.027 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-07-21 10:57:34.034 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.034 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.034 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.035 - INFO - [SimulationController.java:195] : 接收到导出仿真任务请求: simulationId=1
2025-07-21 10:57:34.041 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.041 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.041 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.043 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectId=1, keyword=null, startTime=null, endTime=null)
2025-07-21 10:57:34.044 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 0
2025-07-21 10:57:34.051 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.051 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.051 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.052 - INFO - [SimulationController.java:170] : 接收到运行仿真任务请求: simulationId=1
2025-07-21 10:57:34.052 - INFO - [SimulationController.java:176] : 仿真任务运行成功，任务ID: 1
2025-07-21 10:57:34.058 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.058 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.058 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.058 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=1
2025-07-21 10:57:34.060 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 1, 用户总数: 100, 合格率: 95.5
2025-07-21 10:57:34.065 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.065 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.065 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.073 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=0
2025-07-21 10:57:34.074 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真ID不能为空或无效
2025-07-21 10:57:34.078 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.078 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.078 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.080 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=1, simulationModel=新模型, simulationDraft=新草稿, simulationScript=新脚本, nodes=新节点, controlStrategy=新控制策略
2025-07-21 10:57:34.080 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 1, 更新后模型: 新模型, 更新后脚本: 新脚本
2025-07-21 10:57:34.085 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.085 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.085 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.086 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-07-21 10:57:34.086 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-21 10:57:34.091 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.091 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.091 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.092 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=999
2025-07-21 10:57:34.092 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真任务不存在
2025-07-21 10:57:34.096 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.096 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.096 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.097 - INFO - [SimulationController.java:142] : 接收到复制仿真任务请求: simulationId=1
2025-07-21 10:57:34.097 - INFO - [SimulationController.java:146] : 仿真任务复制成功，新ID: 2
2025-07-21 10:57:34.101 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.101 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.101 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.102 - INFO - [SimulationController.java:218] : 接收到获取仿真任务输出路径请求: simulationId=1
2025-07-21 10:57:34.102 - INFO - [SimulationController.java:223] : 获取仿真任务输出路径成功，任务ID: 1, 输出路径: /output/path/simulation_1
2025-07-21 10:57:34.112 -ERROR - [DeviceServiceImpl.java:141] : 设备更新失败：参数代码已被其他设备使用: CONFLICT_PARAM
2025-07-21 10:57:34.112 -ERROR - [DeviceServiceImpl.java:168] : 设备删除失败：指定ID的设备不存在: 999
2025-07-21 10:57:34.114 - INFO - [DeviceServiceImpl.java:152] : 设备更新成功: Device(parameterId=1, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM, version=2.0)
2025-07-21 10:57:34.115 -ERROR - [DeviceServiceImpl.java:105] : 查询设备失败：ID不能为空
2025-07-21 10:57:34.115 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: invalidField, sortOrder: desc, keyword: null
2025-07-21 10:57:34.115 - WARN - [DeviceServiceImpl.java:74] : 无效的排序字段: invalidField，使用默认排序字段 parameterId
2025-07-21 10:57:34.115 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-07-21 10:57:34.115 - INFO - [DeviceServiceImpl.java:53] : 设备创建成功: Device(parameterId=null, deviceType=LINE, parameterCode=NEW_PARAM_001, version=1.0)
2025-07-21 10:57:34.116 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: parameterId, sortOrder: desc, keyword: null
2025-07-21 10:57:34.116 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-07-21 10:57:34.118 - INFO - [DeviceServiceImpl.java:178] : 设备删除成功: 1
2025-07-21 10:57:34.118 -ERROR - [DeviceServiceImpl.java:133] : 设备更新失败：指定ID的设备不存在: 999
2025-07-21 10:57:34.120 -ERROR - [DeviceServiceImpl.java:161] : 设备删除失败：ID不能为空
2025-07-21 10:57:34.120 -ERROR - [DeviceServiceImpl.java:42] : 设备创建失败：参数代码已存在: EXISTING_PARAM
2025-07-21 10:57:34.121 -ERROR - [DeviceServiceImpl.java:111] : 查询设备失败：指定ID的设备不存在: 999
2025-07-21 10:57:34.122 - INFO - [DeviceServiceImpl.java:96] : 开始查询设备总数, keyword: null
2025-07-21 10:57:34.123 - INFO - [DeviceServiceImpl.java:98] : 查询设备总数完成，共 5 条记录
2025-07-21 10:57:34.123 - INFO - [DeviceServiceImpl.java:115] : 查询设备成功: Device(parameterId=1, deviceType=LINE, parameterCode=TEST_PARAM_001, version=1.0)
2025-07-21 10:57:34.134 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.134 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.134 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.135 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-21 10:57:34.135 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=1, deviceType=LINE, parameterCode=UPDATED_PARAM001, version=1.1)
2025-07-21 10:57:34.136 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=2, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM002, version=2.1)
2025-07-21 10:57:34.139 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.139 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.139 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.140 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-21 10:57:34.141 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-21 10:57:34.141 -ERROR - [DeviceController.java:69] : 设备操作失败: 设备类型不能为空.
2025-07-21 10:57:34.146 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.146 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.146 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.147 - INFO - [DeviceController.java:81] : 接收到分页查询设备请求: DeviceQueryDTO [page=1, size=10, sortBy=parameterId, sortOrder=desc, keyword=test]
2025-07-21 10:57:34.148 - INFO - [DeviceController.java:93] : 设备查询成功，总数量: 2
2025-07-21 10:57:34.153 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.153 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.153 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.154 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-07-21 10:57:34.154 - INFO - [DeviceController.java:115] : 设备删除结果: true
2025-07-21 10:57:34.158 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.158 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.158 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.158 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-07-21 10:57:34.158 - INFO - [DeviceController.java:115] : 设备删除结果: false
2025-07-21 10:57:34.162 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.162 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.162 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.164 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-07-21 10:57:34.164 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-21 10:57:34.170 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.170 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.170 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.171 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-07-21 10:57:34.171 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-21 10:57:34.175 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.175 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.175 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:34.177 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-21 10:57:34.177 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-21 10:57:34.177 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 2
2025-07-21 10:57:34.181 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:34.181 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:34.182 - INFO - [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-07-21 10:57:34.182 - INFO - [DeviceController.java:100] : 接收到查询设备请求: parameterId=1
2025-07-21 10:57:34.182 - INFO - [DeviceController.java:104] : 设备查询成功: Device(parameterId=1, deviceType=LINE, parameterCode=PARAM001, version=1.0)
2025-07-21 10:57:39.331 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-07-21 10:57:39.546 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/non_existent.sql
2025-07-21 10:57:39.551 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-21 10:57:40.774 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-21 10:57:40.777 - WARN - [DatabaseUtils.java:146] : 数据库连接测试失败: jdbc:invalid:url - No suitable driver found for jdbc:invalid:url
2025-07-21 10:57:40.846 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 10:57:40.992 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 10:57:41.604 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-21 10:57:41.604 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:41.604 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:41.604 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 10:57:41.604 - INFO - [VoltageQualityReportPerformanceTest.java:96] : === 中等文件性能测试 ===
2025-07-21 10:57:41.748 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit7985352039295566070\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit7985352039295566070\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 142ms
2025-07-21 10:57:41.748 - INFO - [VoltageQualityReportPerformanceTest.java:107] : 中等文件处理时间: 144ms
2025-07-21 10:57:41.749 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 100
  总处理时间: 142ms
  平均处理时间: 142ms
  处理速度: 704.23 记录/秒
  文件处理速度: 7.04 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=8ms, avg=8ms, min=8ms, max=8ms}
    generateReport: OperationStats{count=1, total=27ms, avg=27ms, min=27ms, max=27ms}
    generateVoltageReport: OperationStats{count=1, total=142ms, avg=142ms, min=142ms, max=142ms}
    readVoltageData: OperationStats{count=1, total=104ms, avg=104ms, min=104ms, max=104ms}
    calculateUserStats: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-07-21 10:57:41.750 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:41.750 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:41.764 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 10:57:41.818 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 10:57:42.289 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-21 10:57:42.289 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:42.289 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:42.289 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 10:57:42.290 - INFO - [VoltageQualityReportPerformanceTest.java:66] : === 小文件性能测试 ===
2025-07-21 10:57:42.308 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit4695581713519687686\small_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit4695581713519687686\电压合格率报表.xlsx, 处理记录数: 10, 耗时: 18ms
2025-07-21 10:57:42.331 - INFO - [VoltageQualityReportPerformanceTest.java:82] : 小文件处理时间 - 第一次: 18ms, 第二次: 23ms, 提升: -27.77777777777778%
2025-07-21 10:57:42.331 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 10
  总处理时间: 18ms
  平均处理时间: 18ms
  处理速度: 555.56 记录/秒
  文件处理速度: 55.56 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    generateReport: OperationStats{count=1, total=6ms, avg=6ms, min=6ms, max=6ms}
    generateVoltageReport: OperationStats{count=2, total=41ms, avg=20ms, min=18ms, max=23ms}
    readVoltageData: OperationStats{count=1, total=12ms, avg=12ms, min=12ms, max=12ms}
    calculateUserStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-07-21 10:57:42.331 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:42.331 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:42.345 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 10:57:42.403 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 10:57:42.875 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1248KB
2025-07-21 10:57:42.875 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:42.875 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:42.875 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 10:57:42.875 - INFO - [VoltageQualityReportPerformanceTest.java:141] : === 缓存效率测试 ===
2025-07-21 10:57:42.918 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit3331050213189568674\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit3331050213189568674\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 43ms
2025-07-21 10:57:42.918 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:42.958 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第1次处理时间: 39ms
2025-07-21 10:57:42.975 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第2次处理时间: 17ms
2025-07-21 10:57:42.996 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第3次处理时间: 21ms
2025-07-21 10:57:43.012 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第4次处理时间: 16ms
2025-07-21 10:57:43.030 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第5次处理时间: 18ms
2025-07-21 10:57:43.030 - INFO - [VoltageQualityReportPerformanceTest.java:162] : 缓存统计: CacheStats{size=5, hits=12, misses=15, hitRate=44.44%}
2025-07-21 10:57:43.031 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 0
  总记录数: 0
  总处理时间: 0ms
  平均处理时间: 0ms
  处理速度: 0.00 记录/秒
  文件处理速度: 0.00 文件/秒
  操作统计:
    generateVoltageReport: OperationStats{count=5, total=110ms, avg=22ms, min=16ms, max=38ms}
}
2025-07-21 10:57:43.031 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:43.031 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:43.044 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 10:57:43.098 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 10:57:43.571 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-21 10:57:43.571 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:43.571 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:43.571 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 10:57:43.571 - INFO - [VoltageQualityReportPerformanceTest.java:115] : === 大文件性能测试 ===
2025-07-21 10:57:43.830 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit8675374744079411522\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit8675374744079411522\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 259ms
2025-07-21 10:57:43.830 - INFO - [VoltageQualityReportPerformanceTest.java:126] : 大文件处理时间: 259ms
2025-07-21 10:57:43.830 - INFO - [VoltageQualityReportPerformanceTest.java:136] : 处理速度: 3861.00 记录/秒
2025-07-21 10:57:43.830 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 259ms
  平均处理时间: 259ms
  处理速度: 3861.00 记录/秒
  文件处理速度: 3.86 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=6ms, avg=6ms, min=6ms, max=6ms}
    generateReport: OperationStats{count=1, total=36ms, avg=36ms, min=36ms, max=36ms}
    generateVoltageReport: OperationStats{count=1, total=259ms, avg=259ms, min=259ms, max=259ms}
    readVoltageData: OperationStats{count=1, total=204ms, avg=204ms, min=204ms, max=204ms}
    calculateUserStats: OperationStats{count=1, total=11ms, avg=11ms, min=11ms, max=11ms}
    groupByUser: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
}
2025-07-21 10:57:43.831 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:43.831 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:43.842 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 10:57:43.891 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 10:57:44.358 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1248KB
2025-07-21 10:57:44.358 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:44.358 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:44.358 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 10:57:44.358 - INFO - [VoltageQualityReportPerformanceTest.java:175] : === 内存效率测试 ===
2025-07-21 10:57:44.750 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit9023967714455911508\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit9023967714455911508\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 200ms
2025-07-21 10:57:44.829 - INFO - [VoltageQualityReportPerformanceTest.java:190] : 内存使用: 初始=55MB, 最终=58MB, 增加=2MB
2025-07-21 10:57:44.830 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 200ms
  平均处理时间: 200ms
  处理速度: 5000.00 记录/秒
  文件处理速度: 5.00 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
    generateReport: OperationStats{count=1, total=36ms, avg=36ms, min=36ms, max=36ms}
    generateVoltageReport: OperationStats{count=1, total=200ms, avg=200ms, min=200ms, max=200ms}
    readVoltageData: OperationStats{count=1, total=160ms, avg=160ms, min=160ms, max=160ms}
    calculateUserStats: OperationStats{count=1, total=2ms, avg=2ms, min=2ms, max=2ms}
    groupByUser: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
}
2025-07-21 10:57:44.830 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:44.830 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 10:57:45.666 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.673 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.680 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.681 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.695 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.712 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit5797367740334124738\test_voltage_data.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit5797367740334124738\电压合格率报表.xlsx, 处理记录数: 3, 耗时: 17ms
2025-07-21 10:57:45.712 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.720 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.730 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.742 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:45.743 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 10:57:47.157 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 10:57:47.157 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-21 10:57:47.157 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-07-21 10:57:47.157 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-21 10:57:47.159 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-21 10:57:47.159 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-21 10:57:47.162 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 10:57:47.162 -ERROR - [DatabaseInitializer.java:54] : 数据库初始化失败
java.lang.ClassNotFoundException: invalid.driver.Class
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:335)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.cet.electric.ngapserver.config.DatabaseInitializer.run(DatabaseInitializer.java:40)
	at com.cet.electric.ngapserver.config.DatabaseInitializerTest.testRun_WhenDriverLoadFails_ShouldThrowException(DatabaseInitializerTest.java:137)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.ExpectException.evaluate(ExpectException.java:19)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:78)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:84)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:39)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:161)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at org.junit.vintage.engine.execution.RunnerExecutor.execute(RunnerExecutor.java:43)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.junit.vintage.engine.VintageTestEngine.executeAllChildren(VintageTestEngine.java:82)
	at org.junit.vintage.engine.VintageTestEngine.execute(VintageTestEngine.java:73)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
2025-07-21 10:57:47.163 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 10:57:47.163 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-21 10:57:47.163 - INFO - [DatabaseInitializer.java:45] : 数据库文件不存在，开始创建数据库: *******************************
2025-07-21 10:57:47.163 - INFO - [DatabaseInitializer.java:66] : 开始初始化数据库...
2025-07-21 10:57:47.164 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-07-21 10:57:47.164 - INFO - [DatabaseInitializer.java:75] : 数据库连接创建成功，开始执行建表脚本...
2025-07-21 10:57:47.164 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-21 10:57:48.431 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-21 10:57:48.431 - INFO - [DatabaseInitializer.java:80] : 数据库初始化完成
2025-07-21 10:57:48.432 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-21 10:57:48.594 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 10:57:48.594 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-21 10:57:48.595 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-07-21 10:57:48.595 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-21 10:57:48.598 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: simulation
2025-07-21 10:57:48.598 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: device
2025-07-21 10:57:48.598 - WARN - [DatabaseInitializer.java:109] : 数据库表结构不完整，重新执行初始化脚本...
2025-07-21 10:57:48.598 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-21 10:57:50.011 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-21 10:57:50.012 - INFO - [DatabaseInitializer.java:111] : 数据库表结构修复完成
2025-07-21 10:57:50.012 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-21 10:57:50.026 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.026 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.026 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.027 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=
2025-07-21 10:57:50.027 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-21 10:57:50.032 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.032 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.033 - INFO - [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-07-21 10:57:50.033 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-21 10:57:50.033 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-21 10:57:50.037 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.037 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.038 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.039 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectType=null, keyword=null, startTime=null, endTime=null)
2025-07-21 10:57:50.039 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 0
2025-07-21 10:57:50.043 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.043 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.043 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.044 - INFO - [ProjectController.java:134] : 接收到导出项目配置文件请求: projectId=1
2025-07-21 10:57:50.048 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.048 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.048 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.050 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=  测试项目  , projectType=  General_scenario  
2025-07-21 10:57:50.050 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-21 10:57:50.054 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.055 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.055 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.056 - INFO - [ProjectController.java:142] : 接收到导入项目配置文件请求，文件名：project.json, 文件大小：66
2025-07-21 10:57:50.056 - INFO - [ProjectController.java:146] : 项目配置文件导入成功，项目ID：1
2025-07-21 10:57:50.061 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.061 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.061 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.062 - INFO - [ProjectController.java:119] : 接收到删除项目请求: projectId=1
2025-07-21 10:57:50.062 - INFO - [ProjectController.java:124] : 项目删除成功，项目ID: 1
2025-07-21 10:57:50.066 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.066 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.066 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.067 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=General_scenario
2025-07-21 10:57:50.067 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-21 10:57:50.071 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.072 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.072 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.072 - INFO - [ProjectController.java:106] : 接收到重命名项目请求: projectId=1, newName=新项目名称
2025-07-21 10:57:50.073 - INFO - [ProjectController.java:111] : 项目重命名成功，项目ID: 1, 新名称: 新项目名称
2025-07-21 10:57:50.077 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 10:57:50.077 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 10:57:50.077 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 10:57:50.077 - INFO - [ProjectController.java:91] : 接收到复制项目请求: projectId=1
2025-07-21 10:57:50.078 - INFO - [ProjectController.java:96] : 项目复制成功，原项目ID: 1, 新项目ID: 2
2025-07-21 10:57:50.487 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 10:57:50.487 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 10:57:50.488 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-21 10:57:50.488 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 200 OK
2025-07-21 10:57:50.488 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 10:57:50.488 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-21 10:57:50.489 - WARN - [DssUtils.java:74] : DSS服务返回空的错误响应，已创建默认错误信息
2025-07-21 10:57:50.489 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-07-21 10:57:50.489 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 10:57:50.489 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-21 10:57:50.489 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-07-21 10:57:50.489 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-07-21 10:57:50.489 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 10:57:50.490 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-21 10:57:50.490 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-07-21 10:57:50.490 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 400 BAD_REQUEST
2025-07-21 10:57:52.212 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-21 10:57:52.213 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-21 10:57:52.216 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-21 10:57:52.218 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-21 11:22:57.013 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplicationTests on DESKTOP-9LFKQ75 with PID 31588 (started by dell in D:\CETWorkSpace\ngap-server)
2025-07-21 11:22:57.015 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-21 11:22:59.340 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 11:23:00.108 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-21 11:23:00.220 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-21 11:23:00.462 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-21 11:23:00.992 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-21 11:23:01.028 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-21 11:23:01.056 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-21 11:23:01.099 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-21 11:23:01.304 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-21 11:23:01.333 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-21 11:23:01.334 - INFO - [TaskSchedule.java:45] : 开始删除项目
2025-07-21 11:23:01.342 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplicationTests in 5.057 seconds (JVM running for 10.74)
2025-07-21 11:23:01.362 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-21 11:23:01.364 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 11:23:01.364 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-21 11:23:01.364 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-21 11:23:01.365 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-21 11:23:01.566 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-21 11:23:01.574 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-21 11:23:01.588 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-21 11:23:01.611 - INFO - [TaskSchedule.java:51] : 没有需要清理的已删除项目记录
2025-07-21 11:23:01.611 - INFO - [TaskSchedule.java:97] : 开始删除仿真任务
2025-07-21 11:23:01.682 - INFO - [TaskSchedule.java:103] : 没有需要清理的已删除仿真任务
2025-07-21 11:23:01.682 - INFO - [TaskSchedule.java:159] : 开始清理null文件夹
2025-07-21 11:23:01.682 - INFO - [TaskSchedule.java:185] : 开始在data目录中清理null文件夹: D:\CETWorkSpace\ngap-server\data
2025-07-21 11:23:01.700 - INFO - [TaskSchedule.java:187] : 成功清理0个null文件夹
2025-07-21 11:23:01.700 - INFO - [TaskSchedule.java:193] : null文件夹清理完毕
2025-07-21 11:23:01.700 - INFO - [TaskSchedule.java:42] : 删除完毕
2025-07-21 11:23:02.710 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.710 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.712 - INFO - [FrameworkServlet.java:547] : Completed initialization in 2 ms
2025-07-21 11:23:02.747 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=1
2025-07-21 11:23:02.748 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 1, 指标数量: 2
2025-07-21 11:23:02.799 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.799 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.799 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.820 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=1, queryDTO=UserVoltageReportQueryDTO(keyword=用户, pageNum=1, pageSize=10)
2025-07-21 11:23:02.820 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 1, 用户数量: 2
2025-07-21 11:23:02.829 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.830 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.830 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.831 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=测试仿真
2025-07-21 11:23:02.832 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-07-21 11:23:02.841 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.841 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.842 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.842 - INFO - [SimulationController.java:183] : 接收到获取仿真策略列表请求
2025-07-21 11:23:02.842 - INFO - [SimulationController.java:186] : 仿真策略列表获取成功，总数量: 2
2025-07-21 11:23:02.849 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.849 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.849 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.851 - INFO - [SimulationController.java:118] : 接收到删除仿真任务请求: simulationId=1
2025-07-21 11:23:02.851 - INFO - [SimulationController.java:122] : 仿真任务删除成功，ID: 1
2025-07-21 11:23:02.858 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.858 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.858 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.859 - INFO - [SimulationController.java:244] : 接收到获取仿真任务指标数据请求: simulationId=1, metricName=voltage
2025-07-21 11:23:02.859 - INFO - [SimulationController.java:249] : 获取仿真任务指标数据成功，任务ID: 1, 指标名称: voltage, 数据点数量: 3
2025-07-21 11:23:02.868 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.868 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.868 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.872 - INFO - [SimulationController.java:156] : 接收到上传文件请求，文件名：test.dss, 文件类型：dss, 文件大小：17
2025-07-21 11:23:02.873 - INFO - [SimulationController.java:162] : 文件上传成功，保存路径：/path/to/uploaded/file
2025-07-21 11:23:02.879 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.879 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.879 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.883 - INFO - [SimulationController.java:276] : 接收到导出仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricExportDTO(pic=chart, metricIds=[voltage, current], startTimestamp=1000)
2025-07-21 11:23:02.889 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.889 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.889 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.890 - INFO - [SimulationController.java:206] : 接收到导入仿真任务请求，文件名：simulation.json, 文件大小：36
2025-07-21 11:23:02.890 - INFO - [SimulationController.java:210] : 仿真任务导入成功，仿真任务ID：1
2025-07-21 11:23:02.900 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.900 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.900 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.902 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricQueryDTO(metricIds=[voltage, current], startTimestamp=1000)
2025-07-21 11:23:02.903 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 1, 指标数量: 2
2025-07-21 11:23:02.911 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.911 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.912 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.912 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=4
2025-07-21 11:23:02.913 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 4, 总数据点: 1000, 合格数据点: 985, 合格率: 98.50%
2025-07-21 11:23:02.923 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.923 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.923 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.924 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=1
2025-07-21 11:23:02.924 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 1, 总数据点: 10000, 合格数据点: 8550, 合格率: 85.50%
2025-07-21 11:23:02.932 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.933 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.933 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.934 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=3
2025-07-21 11:23:02.934 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 3, 总数据点: 100, 合格数据点: 75, 合格率: 75.00%
2025-07-21 11:23:02.940 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.940 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.940 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.942 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=1
2025-07-21 11:23:02.942 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 1
2025-07-21 11:23:02.949 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.949 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.949 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.950 - INFO - [SimulationController.java:82] : 接收到重命名仿真任务请求: simulationId=1, newName=新仿真名称
2025-07-21 11:23:02.950 - INFO - [SimulationController.java:86] : 仿真任务重命名成功，ID: 1, 新名称: 新仿真名称
2025-07-21 11:23:02.957 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.957 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.958 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.958 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=  测试仿真  
2025-07-21 11:23:02.959 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-07-21 11:23:02.966 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.966 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.966 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.967 - INFO - [SimulationController.java:195] : 接收到导出仿真任务请求: simulationId=1
2025-07-21 11:23:02.971 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.972 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.972 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.974 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectId=1, keyword=null, startTime=null, endTime=null)
2025-07-21 11:23:02.974 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 0
2025-07-21 11:23:02.980 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.980 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.980 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.980 - INFO - [SimulationController.java:170] : 接收到运行仿真任务请求: simulationId=1
2025-07-21 11:23:02.982 - INFO - [SimulationController.java:176] : 仿真任务运行成功，任务ID: 1
2025-07-21 11:23:02.986 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.987 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.987 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.987 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=1
2025-07-21 11:23:02.988 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 1, 用户总数: 100, 合格率: 95.5
2025-07-21 11:23:02.994 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:02.994 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:02.994 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:02.995 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=0
2025-07-21 11:23:02.995 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真ID不能为空或无效
2025-07-21 11:23:03.000 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.000 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.000 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.004 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=1, simulationModel=新模型, simulationDraft=新草稿, simulationScript=新脚本, nodes=新节点, controlStrategy=新控制策略
2025-07-21 11:23:03.004 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 1, 更新后模型: 新模型, 更新后脚本: 新脚本
2025-07-21 11:23:03.010 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.011 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.011 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.012 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-07-21 11:23:03.012 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-21 11:23:03.017 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.017 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.017 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.018 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=999
2025-07-21 11:23:03.018 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真任务不存在
2025-07-21 11:23:03.024 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.024 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.024 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.025 - INFO - [SimulationController.java:142] : 接收到复制仿真任务请求: simulationId=1
2025-07-21 11:23:03.025 - INFO - [SimulationController.java:146] : 仿真任务复制成功，新ID: 2
2025-07-21 11:23:03.030 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.030 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.030 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.030 - INFO - [SimulationController.java:218] : 接收到获取仿真任务输出路径请求: simulationId=1
2025-07-21 11:23:03.032 - INFO - [SimulationController.java:223] : 获取仿真任务输出路径成功，任务ID: 1, 输出路径: /output/path/simulation_1
2025-07-21 11:23:03.049 -ERROR - [DeviceServiceImpl.java:141] : 设备更新失败：参数代码已被其他设备使用: CONFLICT_PARAM
2025-07-21 11:23:03.050 -ERROR - [DeviceServiceImpl.java:168] : 设备删除失败：指定ID的设备不存在: 999
2025-07-21 11:23:03.050 - INFO - [DeviceServiceImpl.java:152] : 设备更新成功: Device(parameterId=1, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM, version=2.0)
2025-07-21 11:23:03.052 -ERROR - [DeviceServiceImpl.java:105] : 查询设备失败：ID不能为空
2025-07-21 11:23:03.052 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: invalidField, sortOrder: desc, keyword: null
2025-07-21 11:23:03.052 - WARN - [DeviceServiceImpl.java:74] : 无效的排序字段: invalidField，使用默认排序字段 parameterId
2025-07-21 11:23:03.052 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-07-21 11:23:03.053 - INFO - [DeviceServiceImpl.java:53] : 设备创建成功: Device(parameterId=null, deviceType=LINE, parameterCode=NEW_PARAM_001, version=1.0)
2025-07-21 11:23:03.054 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: parameterId, sortOrder: desc, keyword: null
2025-07-21 11:23:03.054 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-07-21 11:23:03.054 - INFO - [DeviceServiceImpl.java:178] : 设备删除成功: 1
2025-07-21 11:23:03.055 -ERROR - [DeviceServiceImpl.java:133] : 设备更新失败：指定ID的设备不存在: 999
2025-07-21 11:23:03.056 -ERROR - [DeviceServiceImpl.java:161] : 设备删除失败：ID不能为空
2025-07-21 11:23:03.056 -ERROR - [DeviceServiceImpl.java:42] : 设备创建失败：参数代码已存在: EXISTING_PARAM
2025-07-21 11:23:03.057 -ERROR - [DeviceServiceImpl.java:111] : 查询设备失败：指定ID的设备不存在: 999
2025-07-21 11:23:03.057 - INFO - [DeviceServiceImpl.java:96] : 开始查询设备总数, keyword: null
2025-07-21 11:23:03.057 - INFO - [DeviceServiceImpl.java:98] : 查询设备总数完成，共 5 条记录
2025-07-21 11:23:03.060 - INFO - [DeviceServiceImpl.java:115] : 查询设备成功: Device(parameterId=1, deviceType=LINE, parameterCode=TEST_PARAM_001, version=1.0)
2025-07-21 11:23:03.073 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.073 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.073 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.075 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-21 11:23:03.075 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=1, deviceType=LINE, parameterCode=UPDATED_PARAM001, version=1.1)
2025-07-21 11:23:03.075 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=2, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM002, version=2.1)
2025-07-21 11:23:03.080 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.080 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.080 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.082 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-21 11:23:03.082 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-21 11:23:03.082 -ERROR - [DeviceController.java:69] : 设备操作失败: 设备类型不能为空.
2025-07-21 11:23:03.087 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.088 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.088 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.089 - INFO - [DeviceController.java:81] : 接收到分页查询设备请求: DeviceQueryDTO [page=1, size=10, sortBy=parameterId, sortOrder=desc, keyword=test]
2025-07-21 11:23:03.090 - INFO - [DeviceController.java:93] : 设备查询成功，总数量: 2
2025-07-21 11:23:03.096 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.096 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.096 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.097 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-07-21 11:23:03.097 - INFO - [DeviceController.java:115] : 设备删除结果: true
2025-07-21 11:23:03.102 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.102 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.102 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.102 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-07-21 11:23:03.102 - INFO - [DeviceController.java:115] : 设备删除结果: false
2025-07-21 11:23:03.107 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.107 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.107 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.108 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-07-21 11:23:03.108 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-21 11:23:03.114 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.114 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.115 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.116 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-07-21 11:23:03.117 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-21 11:23:03.122 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.122 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.122 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.124 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-21 11:23:03.124 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-21 11:23:03.124 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 2
2025-07-21 11:23:03.130 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:03.130 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:03.130 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:03.130 - INFO - [DeviceController.java:100] : 接收到查询设备请求: parameterId=1
2025-07-21 11:23:03.130 - INFO - [DeviceController.java:104] : 设备查询成功: Device(parameterId=1, deviceType=LINE, parameterCode=PARAM001, version=1.0)
2025-07-21 11:23:12.459 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-07-21 11:23:12.818 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/non_existent.sql
2025-07-21 11:23:12.823 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-21 11:23:14.167 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-21 11:23:14.170 - WARN - [DatabaseUtils.java:146] : 数据库连接测试失败: jdbc:invalid:url - No suitable driver found for jdbc:invalid:url
2025-07-21 11:23:14.265 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 11:23:14.480 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 11:23:15.632 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-21 11:23:15.632 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:15.632 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:15.632 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 11:23:15.632 - INFO - [VoltageQualityReportPerformanceTest.java:96] : === 中等文件性能测试 ===
2025-07-21 11:23:15.785 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit7735013623462802479\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit7735013623462802479\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 152ms
2025-07-21 11:23:15.785 - INFO - [VoltageQualityReportPerformanceTest.java:107] : 中等文件处理时间: 152ms
2025-07-21 11:23:15.786 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 100
  总处理时间: 152ms
  平均处理时间: 152ms
  处理速度: 657.89 记录/秒
  文件处理速度: 6.58 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=3ms, avg=3ms, min=3ms, max=3ms}
    generateReport: OperationStats{count=1, total=30ms, avg=30ms, min=30ms, max=30ms}
    generateVoltageReport: OperationStats{count=1, total=152ms, avg=152ms, min=152ms, max=152ms}
    readVoltageData: OperationStats{count=1, total=117ms, avg=117ms, min=117ms, max=117ms}
    calculateUserStats: OperationStats{count=1, total=2ms, avg=2ms, min=2ms, max=2ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-07-21 11:23:15.786 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:15.786 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:15.808 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 11:23:15.937 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 11:23:17.086 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-21 11:23:17.086 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:17.086 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:17.086 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 11:23:17.087 - INFO - [VoltageQualityReportPerformanceTest.java:66] : === 小文件性能测试 ===
2025-07-21 11:23:17.112 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit3722405962203768294\small_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit3722405962203768294\电压合格率报表.xlsx, 处理记录数: 10, 耗时: 25ms
2025-07-21 11:23:17.137 - INFO - [VoltageQualityReportPerformanceTest.java:82] : 小文件处理时间 - 第一次: 26ms, 第二次: 24ms, 提升: 7.6923076923076925%
2025-07-21 11:23:17.138 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 10
  总处理时间: 25ms
  平均处理时间: 25ms
  处理速度: 400.00 记录/秒
  文件处理速度: 40.00 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    generateReport: OperationStats{count=1, total=10ms, avg=10ms, min=10ms, max=10ms}
    generateVoltageReport: OperationStats{count=2, total=50ms, avg=25ms, min=24ms, max=26ms}
    readVoltageData: OperationStats{count=1, total=15ms, avg=15ms, min=15ms, max=15ms}
    calculateUserStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-07-21 11:23:17.138 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:17.138 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:17.158 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 11:23:17.276 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 11:23:18.419 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-21 11:23:18.419 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:18.419 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:18.419 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 11:23:18.419 - INFO - [VoltageQualityReportPerformanceTest.java:141] : === 缓存效率测试 ===
2025-07-21 11:23:18.478 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit994526023685685823\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit994526023685685823\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 58ms
2025-07-21 11:23:18.479 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:18.525 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第1次处理时间: 46ms
2025-07-21 11:23:18.552 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第2次处理时间: 27ms
2025-07-21 11:23:18.578 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第3次处理时间: 26ms
2025-07-21 11:23:18.607 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第4次处理时间: 28ms
2025-07-21 11:23:18.632 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第5次处理时间: 24ms
2025-07-21 11:23:18.633 - INFO - [VoltageQualityReportPerformanceTest.java:162] : 缓存统计: CacheStats{size=5, hits=12, misses=15, hitRate=44.44%}
2025-07-21 11:23:18.633 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 0
  总记录数: 0
  总处理时间: 0ms
  平均处理时间: 0ms
  处理速度: 0.00 记录/秒
  文件处理速度: 0.00 文件/秒
  操作统计:
    generateVoltageReport: OperationStats{count=5, total=151ms, avg=30ms, min=24ms, max=46ms}
}
2025-07-21 11:23:18.633 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:18.633 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:18.654 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 11:23:18.773 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 11:23:19.929 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-21 11:23:19.929 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:19.930 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:19.930 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 11:23:19.930 - INFO - [VoltageQualityReportPerformanceTest.java:115] : === 大文件性能测试 ===
2025-07-21 11:23:20.302 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit6122497983243611984\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit6122497983243611984\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 372ms
2025-07-21 11:23:20.303 - INFO - [VoltageQualityReportPerformanceTest.java:126] : 大文件处理时间: 373ms
2025-07-21 11:23:20.303 - INFO - [VoltageQualityReportPerformanceTest.java:136] : 处理速度: 2688.17 记录/秒
2025-07-21 11:23:20.303 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 372ms
  平均处理时间: 372ms
  处理速度: 2688.17 记录/秒
  文件处理速度: 2.69 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=4ms, avg=4ms, min=4ms, max=4ms}
    generateReport: OperationStats{count=1, total=86ms, avg=86ms, min=86ms, max=86ms}
    generateVoltageReport: OperationStats{count=1, total=373ms, avg=373ms, min=373ms, max=373ms}
    readVoltageData: OperationStats{count=1, total=266ms, avg=266ms, min=266ms, max=266ms}
    calculateUserStats: OperationStats{count=1, total=14ms, avg=14ms, min=14ms, max=14ms}
    groupByUser: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
}
2025-07-21 11:23:20.303 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:20.303 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:20.322 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-21 11:23:20.441 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-21 11:23:21.576 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-21 11:23:21.577 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:21.577 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:21.577 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-21 11:23:21.577 - INFO - [VoltageQualityReportPerformanceTest.java:175] : === 内存效率测试 ===
2025-07-21 11:23:22.259 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit8575357111803873700\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit8575357111803873700\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 358ms
2025-07-21 11:23:22.347 - INFO - [VoltageQualityReportPerformanceTest.java:190] : 内存使用: 初始=111MB, 最终=114MB, 增加=2MB
2025-07-21 11:23:22.347 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 358ms
  平均处理时间: 358ms
  处理速度: 2793.30 记录/秒
  文件处理速度: 2.79 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=3ms, avg=3ms, min=3ms, max=3ms}
    generateReport: OperationStats{count=1, total=89ms, avg=89ms, min=89ms, max=89ms}
    generateVoltageReport: OperationStats{count=1, total=359ms, avg=359ms, min=359ms, max=359ms}
    readVoltageData: OperationStats{count=1, total=261ms, avg=261ms, min=261ms, max=261ms}
    calculateUserStats: OperationStats{count=1, total=5ms, avg=5ms, min=5ms, max=5ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-07-21 11:23:22.348 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:22.348 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-21 11:23:23.460 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.467 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.476 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.477 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.487 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.499 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit4275667052070491667\test_voltage_data.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit4275667052070491667\电压合格率报表.xlsx, 处理记录数: 3, 耗时: 12ms
2025-07-21 11:23:23.499 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.510 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.516 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.527 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:23.527 - INFO - [ExcelCacheManager.java:144] : 缓存已清空
2025-07-21 11:23:25.481 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 11:23:25.482 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-21 11:23:25.482 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-07-21 11:23:25.482 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-21 11:23:25.486 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-21 11:23:25.486 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-21 11:23:25.488 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 11:23:25.489 -ERROR - [DatabaseInitializer.java:54] : 数据库初始化失败
java.lang.ClassNotFoundException: invalid.driver.Class
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:335)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.cet.electric.ngapserver.config.DatabaseInitializer.run(DatabaseInitializer.java:40)
	at com.cet.electric.ngapserver.config.DatabaseInitializerTest.testRun_WhenDriverLoadFails_ShouldThrowException(DatabaseInitializerTest.java:137)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.ExpectException.evaluate(ExpectException.java:19)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:78)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:84)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:39)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:161)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at org.junit.vintage.engine.execution.RunnerExecutor.execute(RunnerExecutor.java:43)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.junit.vintage.engine.VintageTestEngine.executeAllChildren(VintageTestEngine.java:82)
	at org.junit.vintage.engine.VintageTestEngine.execute(VintageTestEngine.java:73)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
2025-07-21 11:23:25.490 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 11:23:25.490 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-21 11:23:25.490 - INFO - [DatabaseInitializer.java:45] : 数据库文件不存在，开始创建数据库: *******************************
2025-07-21 11:23:25.490 - INFO - [DatabaseInitializer.java:66] : 开始初始化数据库...
2025-07-21 11:23:25.491 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-07-21 11:23:25.492 - INFO - [DatabaseInitializer.java:75] : 数据库连接创建成功，开始执行建表脚本...
2025-07-21 11:23:25.492 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-21 11:23:26.625 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-21 11:23:26.625 - INFO - [DatabaseInitializer.java:80] : 数据库初始化完成
2025-07-21 11:23:26.625 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-21 11:23:26.753 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-21 11:23:26.754 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-21 11:23:26.754 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-07-21 11:23:26.754 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-21 11:23:26.755 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: simulation
2025-07-21 11:23:26.755 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: device
2025-07-21 11:23:26.755 - WARN - [DatabaseInitializer.java:109] : 数据库表结构不完整，重新执行初始化脚本...
2025-07-21 11:23:26.755 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-21 11:23:27.985 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-21 11:23:27.985 - INFO - [DatabaseInitializer.java:111] : 数据库表结构修复完成
2025-07-21 11:23:27.986 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-21 11:23:28.001 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.002 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.002 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.002 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=
2025-07-21 11:23:28.003 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-21 11:23:28.008 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.009 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.009 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.009 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-21 11:23:28.010 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-21 11:23:28.014 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.014 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.015 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.016 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectType=null, keyword=null, startTime=null, endTime=null)
2025-07-21 11:23:28.017 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 0
2025-07-21 11:23:28.021 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.021 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.022 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.022 - INFO - [ProjectController.java:134] : 接收到导出项目配置文件请求: projectId=1
2025-07-21 11:23:28.027 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.027 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.027 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.028 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=  测试项目  , projectType=  General_scenario  
2025-07-21 11:23:28.028 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-21 11:23:28.033 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.033 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.033 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.034 - INFO - [ProjectController.java:142] : 接收到导入项目配置文件请求，文件名：project.json, 文件大小：66
2025-07-21 11:23:28.034 - INFO - [ProjectController.java:146] : 项目配置文件导入成功，项目ID：1
2025-07-21 11:23:28.038 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.039 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.039 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.039 - INFO - [ProjectController.java:119] : 接收到删除项目请求: projectId=1
2025-07-21 11:23:28.040 - INFO - [ProjectController.java:124] : 项目删除成功，项目ID: 1
2025-07-21 11:23:28.045 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.045 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.045 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.046 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=General_scenario
2025-07-21 11:23:28.047 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-21 11:23:28.051 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.052 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.052 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.052 - INFO - [ProjectController.java:106] : 接收到重命名项目请求: projectId=1, newName=新项目名称
2025-07-21 11:23:28.052 - INFO - [ProjectController.java:111] : 项目重命名成功，项目ID: 1, 新名称: 新项目名称
2025-07-21 11:23:28.057 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-21 11:23:28.057 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-21 11:23:28.057 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-21 11:23:28.058 - INFO - [ProjectController.java:91] : 接收到复制项目请求: projectId=1
2025-07-21 11:23:28.058 - INFO - [ProjectController.java:96] : 项目复制成功，原项目ID: 1, 新项目ID: 2
2025-07-21 11:23:28.683 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 11:23:28.684 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 11:23:28.684 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-21 11:23:28.684 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 200 OK
2025-07-21 11:23:28.685 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 11:23:28.685 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-21 11:23:28.685 - WARN - [DssUtils.java:74] : DSS服务返回空的错误响应，已创建默认错误信息
2025-07-21 11:23:28.685 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-07-21 11:23:28.685 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 11:23:28.685 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-21 11:23:28.685 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-07-21 11:23:28.685 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-07-21 11:23:28.686 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-21 11:23:28.686 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-21 11:23:28.686 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-07-21 11:23:28.686 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 400 BAD_REQUEST
2025-07-21 11:23:31.051 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-21 11:23:31.053 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-21 11:23:31.056 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-21 11:23:31.058 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
