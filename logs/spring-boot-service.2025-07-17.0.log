2025-07-17 08:35:24.306 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-17 08:35:24.318 - INFO - [TaskSchedule.java:45] : 开始删除项目
2025-07-17 08:35:24.321 - INFO - [TaskSchedule.java:51] : 没有需要清理的已删除项目记录
2025-07-17 08:35:24.322 - INFO - [TaskSchedule.java:97] : 开始删除仿真任务
2025-07-17 08:35:24.404 - INFO - [TaskSchedule.java:103] : 没有需要清理的已删除仿真任务
2025-07-17 08:35:24.404 - INFO - [TaskSchedule.java:158] : 开始清理null文件夹
2025-07-17 08:35:27.573 - INFO - [TaskSchedule.java:165] : 成功清理0个null文件夹
2025-07-17 08:35:27.574 - INFO - [TaskSchedule.java:171] : null文件夹清理完毕
2025-07-17 08:35:27.574 - INFO - [TaskSchedule.java:42] : 删除完毕
2025-07-17 08:40:00.710 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 22512 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-07-17 08:40:00.721 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-17 08:40:03.289 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-17 08:40:03.297 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-17 08:40:03.298 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-17 08:40:03.298 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-17 08:40:03.379 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-17 08:40:03.379 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 2624 ms
2025-07-17 08:40:04.081 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-17 08:40:04.556 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-17 08:40:04.630 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-17 08:40:04.708 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-17 08:40:05.353 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-17 08:40:05.370 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-17 08:40:05.437 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-17 08:40:05.438 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-17 08:40:05.469 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-17 08:40:05.518 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-17 08:40:05.801 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-17 08:40:05.827 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-17 08:40:05.828 - INFO - [TaskSchedule.java:45] : 开始删除项目
2025-07-17 08:40:05.829 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 5.912 seconds (JVM running for 6.927)
2025-07-17 08:40:05.848 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-17 08:40:05.908 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-17 08:40:05.909 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-17 08:40:05.931 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-17 08:40:05.932 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-17 08:40:06.507 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-17 08:40:06.507 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-17 08:40:06.510 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-17 08:40:06.524 - INFO - [TaskSchedule.java:51] : 没有需要清理的已删除项目记录
2025-07-17 08:40:06.525 - INFO - [TaskSchedule.java:97] : 开始删除仿真任务
2025-07-17 08:40:06.833 - INFO - [TaskSchedule.java:103] : 没有需要清理的已删除仿真任务
2025-07-17 08:40:06.833 - INFO - [TaskSchedule.java:158] : 开始清理null文件夹
2025-07-17 08:40:23.520 - INFO - [TaskSchedule.java:165] : 成功清理0个null文件夹
2025-07-17 08:40:23.521 - INFO - [TaskSchedule.java:171] : null文件夹清理完毕
2025-07-17 08:40:23.521 - INFO - [TaskSchedule.java:42] : 删除完毕
2025-07-17 08:43:31.085 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 08:43:31.086 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-17 08:43:31.096 - INFO - [FrameworkServlet.java:547] : Completed initialization in 10 ms
2025-07-17 08:43:31.141 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-17 08:43:31.141 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-17 08:43:31.141 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-17 08:43:31.141 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-17 08:43:31.225 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-17 08:43:31.226 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-17 08:43:31.297 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-07-17 08:43:31.297 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-17 08:43:31.299 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-07-17 08:43:31.299 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-07-17 09:00:17.989 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-17 09:00:17.991 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-17 09:00:17.994 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-17 09:00:17.996 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-17 09:00:23.549 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 20632 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-07-17 09:00:23.551 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-17 09:00:25.333 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-17 09:00:25.339 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-17 09:00:25.340 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-17 09:00:25.340 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-17 09:00:25.429 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-17 09:00:25.429 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1842 ms
2025-07-17 09:00:26.049 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-17 09:00:26.459 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-17 09:00:26.556 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-17 09:00:26.650 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-17 09:00:26.822 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-17 09:00:26.844 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-17 09:00:26.909 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-17 09:00:26.910 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-17 09:00:26.937 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-17 09:00:26.973 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-17 09:00:27.184 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-17 09:00:27.211 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-17 09:00:27.212 - INFO - [TaskSchedule.java:45] : 开始删除项目
2025-07-17 09:00:27.215 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 4.844 seconds (JVM running for 5.685)
2025-07-17 09:00:27.226 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-17 09:00:27.292 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-17 09:00:27.293 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-17 09:00:27.293 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-17 09:00:27.294 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-17 09:00:27.431 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-17 09:00:27.432 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-17 09:00:27.434 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-17 09:00:27.473 - INFO - [TaskSchedule.java:51] : 没有需要清理的已删除项目记录
2025-07-17 09:00:27.474 - INFO - [TaskSchedule.java:97] : 开始删除仿真任务
2025-07-17 09:00:27.510 - INFO - [TaskSchedule.java:103] : 没有需要清理的已删除仿真任务
2025-07-17 09:00:27.511 - INFO - [TaskSchedule.java:158] : 开始清理null文件夹
2025-07-17 09:00:34.009 - INFO - [TaskSchedule.java:165] : 成功清理0个null文件夹
2025-07-17 09:00:34.010 - INFO - [TaskSchedule.java:171] : null文件夹清理完毕
2025-07-17 09:00:34.010 - INFO - [TaskSchedule.java:42] : 删除完毕
2025-07-17 10:45:07.896 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 22256 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-07-17 10:45:07.906 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-17 10:45:09.969 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-17 10:45:09.979 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-17 10:45:09.979 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-17 10:45:09.981 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-17 10:45:10.063 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-17 10:45:10.064 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 2086 ms
2025-07-17 10:45:11.158 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-17 10:45:12.077 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-17 10:45:12.151 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-17 10:45:12.250 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-17 10:45:12.437 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-17 10:45:12.451 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-17 10:45:12.494 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-17 10:45:12.496 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-17 10:45:12.522 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-17 10:45:12.556 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-17 10:45:12.741 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-17 10:45:12.767 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-17 10:45:12.768 - INFO - [TaskSchedule.java:45] : 开始删除项目
2025-07-17 10:45:12.771 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 5.581 seconds (JVM running for 6.478)
2025-07-17 10:45:12.783 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-17 10:45:12.851 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-17 10:45:12.852 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-17 10:45:12.860 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-17 10:45:12.861 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-17 10:45:13.263 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-17 10:45:13.263 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-17 10:45:13.265 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-17 10:45:13.288 - INFO - [TaskSchedule.java:51] : 没有需要清理的已删除项目记录
2025-07-17 10:45:13.288 - INFO - [TaskSchedule.java:97] : 开始删除仿真任务
2025-07-17 10:45:13.465 - INFO - [TaskSchedule.java:103] : 没有需要清理的已删除仿真任务
2025-07-17 10:45:13.465 - INFO - [TaskSchedule.java:158] : 开始清理null文件夹
2025-07-17 10:45:28.766 - INFO - [TaskSchedule.java:165] : 成功清理0个null文件夹
2025-07-17 10:45:28.767 - INFO - [TaskSchedule.java:171] : null文件夹清理完毕
2025-07-17 10:45:28.767 - INFO - [TaskSchedule.java:42] : 删除完毕
2025-07-17 16:30:04.428 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 16:30:04.434 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-17 16:30:04.457 - INFO - [FrameworkServlet.java:547] : Completed initialization in 23 ms
2025-07-17 16:30:04.630 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:30:04.631 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:30:04.682 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:30:04.682 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:30:04.685 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:30:04.685 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:30:08.459 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:30:08.460 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:30:08.463 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:30:08.464 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:30:08.465 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:30:08.465 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:30:15.534 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:30:15.535 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:30:15.537 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:30:15.538 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:30:15.539 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:30:15.539 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:30:34.759 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:30:34.759 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:30:34.760 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:30:34.760 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:30:34.761 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:30:34.761 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:39:22.854 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:39:22.856 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:39:22.858 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:39:22.858 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:39:22.858 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:39:22.859 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:39:28.882 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:39:28.886 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:39:28.887 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:39:28.887 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:39:28.888 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:39:28.888 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:39:32.140 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:39:32.142 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:39:32.143 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:39:32.143 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:39:32.143 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:39:32.144 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:39:41.302 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:39:41.302 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:39:41.303 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:39:41.303 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:39:41.304 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:39:41.304 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:40:08.135 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:40:08.135 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:40:08.136 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:40:08.136 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:40:08.137 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:40:08.137 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:40:17.636 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:40:17.637 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:40:17.639 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:40:17.639 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:40:17.640 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:40:17.640 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:40:26.881 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:40:26.882 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:40:26.885 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:40:26.885 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:40:26.886 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:40:26.887 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:40:29.221 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:40:29.222 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:40:29.223 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:40:29.223 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:40:29.225 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:40:29.225 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:40:34.890 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:40:34.891 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:40:34.892 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:40:34.892 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:40:34.893 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:40:34.893 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:51:17.354 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:51:17.355 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:51:17.357 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:51:17.357 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:51:17.357 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:51:17.357 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:51:18.329 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:51:18.330 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:51:18.332 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:51:18.332 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:51:18.332 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:51:18.333 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:51:23.569 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:51:23.569 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:51:23.569 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:51:23.571 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:51:23.571 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:51:23.571 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:51:27.103 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:51:27.104 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:51:27.106 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:51:27.106 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:51:27.107 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:51:27.107 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:51:50.811 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:51:50.811 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:51:50.812 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:51:50.812 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:51:50.813 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:51:50.813 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:51:58.667 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:51:58.667 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:51:58.668 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:51:58.668 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:51:58.669 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:51:58.669 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 16:52:05.027 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 16:52:05.028 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 16:52:05.029 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 16:52:05.029 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 16:52:05.030 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 16:52:05.030 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 17:02:01.543 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 17:02:01.544 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 17:02:01.547 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 17:02:01.548 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 17:02:01.549 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 17:02:01.549 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 17:02:30.394 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 17:02:30.394 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 17:02:30.395 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 17:02:30.396 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 17:02:30.396 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 17:02:30.397 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 17:02:40.820 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 17:02:40.822 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 17:02:40.825 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 17:02:40.826 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 17:02:40.828 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 17:02:40.828 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 17:05:53.665 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 17:05:53.666 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 17:05:53.667 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 17:05:53.668 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 17:05:53.669 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 17:05:53.669 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-17 17:08:28.945 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-17 17:08:28.945 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-17 17:08:28.946 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-17 17:08:28.946 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-17 17:08:28.947 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-17 17:08:28.947 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
