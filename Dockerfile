# Java Scaffold Dockerfile
ARG base_image=10.12.135.233/base/openjdk-with-fontconfig:8-jdk-dbg-alpine
FROM ${base_image}

VOLUME /tmp

ARG artifactId
ARG VERSION

# 添加应用 JAR 文件
ADD ./target/${artifactId}.jar /${artifactId}.jar

# 添加启动脚本
COPY startup.sh /startup.sh

# 转换脚本格式并设置执行权限
RUN sed -i 's/\r$//' /startup.sh && chmod +x /startup.sh

# 创建应用所需目录
RUN mkdir -p /database /logs /data

# 环境变量配置
ENV JAVA_OPTS=""
ENV JAR_FILE_NAME ${artifactId}
ENV SKY_COLLECTOR="127.0.0.1:11800"
ENV SKY_NAME="java-scaffold"
ENV SKY_AGENT=""
ENV SKY_IGNORE_PATH="/eureka/**,/swagger-resources/**,/webjars/springfox-swagger-ui/**"

# 直接使用启动脚本
ENTRYPOINT ["sh", "/startup.sh"]
