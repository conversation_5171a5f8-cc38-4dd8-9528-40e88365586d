package cet.flinkjobservice.dao;

import cet.flinkjobservice.def.RuleInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RuleInfoDao {



    /**
     * 根据 ruleName 查询对应的 RuleInfo
     * @param ruleName 子任务名
    Steven Ni<PERSON>

     * @return RuleInfo
     */
    RuleInfo loadByRuleName(String ruleName);

    RuleInfo loadById(long id);

    /**
     * 保存任务参数
     * @param info
     * @return
     */
    int addRuleInfo(RuleInfo info);

    /**
     * 更新参数信息
     * @param info
     * @return
     */
    int updateRuleInfo(RuleInfo info);

    /**
     * 分页查询所有任务参数
     * @param index 查询的起始索引
     * @param limit 查询的记录条数
     * @return 任务列表
     */
    List<RuleInfo> selectAllWithPagination(int index, int limit);

    /**
     * 分页模糊查询任务参数
     * @param index 查询的起始索引
     * @param limit 查询的记录条数
     * @param jobAlias 查询的记录名
     * @return 任务列表
     */
    List<RuleInfo> selectAllWithPaginationAndAlias(int index, int limit, String jobAlias);

    /**
     * 查询所有任务参数
     * @return 任务列表
     */
    List<RuleInfo> selectAll();

    /**
     * 检查 job_name 或 alias 是否唯一
     * @return 不唯一的记录数量，如果返回值大于 0，表示存在相同的 job_name 或 alias
     */
    int checkUniqueJobNameOrAlias(RuleInfo info);

    /**
     * 检查 job_name 是否唯一
     * @return 不唯一的记录数量，如果返回值大于 0，表示存在相同的 job_name
     */
    int checkUniqueJobName(RuleInfo info);

    /**
     * 检查 alias 是否唯一
     * @return 不唯一的记录数量，如果返回值大于 0，表示存在相同的 alias
     */
    int checkUniqueAlias(RuleInfo info);

    /**
     * 获取所有未删除的记录数量
     * @param jobAlias 记录名
     * @return 未删除的记录数量
     */
    int countAllRules(String jobAlias);

    /**
     * 获取所有未删除的正在提交的记录数量
     * @return 未删除的正在提交的记录数量
     */
    int countAllSubmittedRules();

    /**
     * 删除所有 is_delete = true 的记录
     * @return 删除的记录数量
     */
    int deleteAllMarkedAsDeleted();

}
