package cet.flinkjobservice.controller;


import cet.flinkjobservice.def.RuleInfo;
import cet.flinkjobservice.dto.*;
import cet.flinkjobservice.model.common.Result;
import cet.flinkjobservice.model.energy.EnergyReCalcRequestVO;
import cet.flinkjobservice.model.energy.EnergyReCalcStateResultVO;
import cet.flinkjobservice.model.flink.JobInfo;
import cet.flinkjobservice.model.recalc.ObjectNode;
import cet.flinkjobservice.model.recalc.RecalculateCommand;
import cet.flinkjobservice.model.recalc.RecalculateRequest;
import cet.flinkjobservice.service.FlinkJobService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.modelservice.common.entity.model.Model;
import com.cet.engine.common.model.FieldDefinition;
import com.cet.engine.common.model.QueryConfigSqlEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Api
@RestController
@RequestMapping("/flink-job")
public class FlinkJobController {

    public FlinkJobController(FlinkJobService flinkJobService) {
        this.flinkJobService = flinkJobService;
    }

    private final FlinkJobService flinkJobService;

    @GetMapping("/allJobInfo")
    @ApiOperation(value = "获取所有作业")
    @Deprecated
    public Result<List<JobInfo>> getAllJobInfo() {
        return flinkJobService.getAllJobInfo();
    }

    /**
     * 执行实时计算作业
     *
     * @param jobName 执行作业
     * @return 返回jobID
     */
    @ApiOperation(value = "执行实时计算作业")
    @PostMapping(value = {"/{jobName}"})
    @Deprecated
    public Result<String> runRealtimeJob(@ApiParam(value = "jobName", example = "BatchEnergy") @PathVariable(value = "jobName") String jobName) {
        return flinkJobService.runRealtimeJob(jobName);
    }

    /**
     * 执行重算作业
     *
     * @param jobName   作业名称
     * @param beginTime 重算开始时间
     * @return 返回JobID
     */
    @ApiOperation(value = "执行重算作业")
    @PostMapping(value = {"/{jobName}/{beginTime}"})
    @Deprecated
    public Result<String> runRecalculateJob(@ApiParam(value = "jobName", example = "BatchEnergy") @PathVariable(value = "jobName") String jobName, @ApiParam(value = "beginTime", example = "1640966400000") @PathVariable(value = "beginTime") Long beginTime) {
        return flinkJobService.runRecalculateJob(jobName, beginTime);
    }

    /**
     * 执行重算作业
     *
     * @param energyReCalcRequestVO 查询参数
     * @return 返回JobID
     */
    @ApiOperation(value = "执行重算作业")
    @PostMapping(value = {"/energy/re-calc"})
    @Deprecated
    public Result<String> runRecalculateJob(@RequestBody EnergyReCalcRequestVO energyReCalcRequestVO) {
        return flinkJobService.runRecalculateJob(energyReCalcRequestVO);
    }

    /**
     * 执行重算作业
     *
     * @param nodes 查询参数
     * @return 返回JobID
     */
    @ApiOperation(value = "获取重算状态")
    @PostMapping(value = {"/energy/re-calc/state"})
    @Deprecated
    public Result<List<EnergyReCalcStateResultVO>> readEnergyReCalcState(@RequestBody List<ObjectNode> nodes) {
        return Result.success(flinkJobService.readEnergyReCalcState(nodes));
    }

    @GetMapping("/cancelAllJob")
    @ApiOperation(value = "取消所有作业运行")
    @Deprecated
    public Result<List<String>> cancelAllJobs() {
        return flinkJobService.cancelAllJobs();
    }


    /**
     * 重算
     *
     * @param command
     */
    @ApiOperation(value = "指定对象和时段重算作业")
    @PostMapping(value = {"/recalculate"})
    @Deprecated
    public Result<RecalculateCommand> RecalculateEnergy(@RequestBody RecalculateRequest command) {
        RecalculateCommand recalculateCommand = flinkJobService.RecalculateEnergy(command);
        return new Result<RecalculateCommand>(0, "成功", recalculateCommand);
    }

    @ApiOperation(value = "查询所有信息")
    @GetMapping(value = {"/getAll"})
    @Deprecated
    public Result<List<RuleInfo>> getAll() {
        List<RuleInfo> ruleInfos = flinkJobService.selectAll();
        return new Result<List<RuleInfo>>(0, "成功", ruleInfos);
    }

    /**
     * 新增参数
     *
     * @param addRuleDTO 包含作业名称和别名的DTO对象
     * @return 返回保存结果，包含已保存的作业信息
     */
    @ApiOperation(value = "新增参数")
    @PostMapping("/add")
    public Result<List<Map<String, String>>> addRule(@RequestBody @ApiParam(value = "作业信息") AddRuleDTO addRuleDTO) {
        Result<List<Map<String, String>>> savedRule = flinkJobService.addRule(addRuleDTO.getJobName(), addRuleDTO.getAlias());
        return savedRule;
    }

    /**
     * 查询参数
     *
     * @param lang         查询的语言参数
     * @param queryRuleDTO 包含查询的起始索引和限制条数的DTO对象
     * @return 返回查询结果，包含作业列表及总记录数
     */
    @ApiOperation(value = "查询参数")
    @PostMapping("/query")
    public Result<?> queryRule(@RequestParam(defaultValue = "zh") String lang, @RequestBody @ApiParam(value = "查询信息") QueryRuleDTO queryRuleDTO) {
        Result<?> queryRule = flinkJobService.queryRule(queryRuleDTO.getIndex(), queryRuleDTO.getLimit(), lang, queryRuleDTO.getJobAlias());
        return queryRule;
    }

    /**
     * 更新参数
     *
     * @param updateRuleDTO 包含作业ID、作业名称和别名的DTO对象
     * @return 返回更新结果，包含更新后的作业信息
     */
    @ApiOperation(value = "更新参数")
    @PutMapping("/update")
    public Result<List<Map<String, String>>> updateRule(@RequestBody @ApiParam(value = "更新信息") UpdateRuleDTO updateRuleDTO) {
        Result<List<Map<String, String>>> updatedRule = flinkJobService.updateRule(updateRuleDTO.getJobName(), updateRuleDTO.getAlias(), updateRuleDTO.getId());
        return updatedRule;
    }

    /**
     * 删除参数
     *
     * @param id 要删除的作业ID（数据库主键）
     * @return 返回删除结果，包含已删除的作业信息
     */
    @ApiOperation(value = "删除参数")
    @DeleteMapping("/delete")
    public Result<List<Map<String, String>>> deleteRule(@RequestParam("id") @ApiParam(value = "id", example = "1234") long id) {
        Result<List<Map<String, String>>> deletedRule = flinkJobService.deleteRule(id);
        return deletedRule;
    }

    /**
     * 保存参数
     *
     * @param saveArgsDTO 保存的信息
     * @return 返回保存结果，包含已保存的作业信息
     */
    @ApiOperation(value = "保存参数")
    @PutMapping("/save")
    public Result<List<Map<String, String>>> saveArgs(@RequestBody @ApiParam(value = "保存信息") SaveArgsDTO saveArgsDTO) {
        Result<List<Map<String, String>>> savedArgs = flinkJobService.saveArgs(saveArgsDTO.getId(), saveArgsDTO.getArgues());
        return savedArgs;
    }

    /**
     * 查询作业逻辑参数
     *
     * @param id 作业的ID（数据库主键）
     * @return 返回查询结果，包含作业ID、作业名称及参数信息
     */
    @ApiOperation(value = "查询作业逻辑参数")
    @GetMapping("queryArgus")
    public Result<List<Map<String, String>>> queryArgus(@RequestParam("id") @ApiParam(value = "id", example = "1234") long id) {
        Result<List<Map<String, String>>> argusResult = flinkJobService.queryArgus(id);
        return argusResult;
    }

    /**
     * 保存作业定位坐标参数
     *
     * @param updateLayoutDTO 保存的信息
     * @return 返回保存结果，包含已保存的作业信息
     */
    @ApiOperation(value = "保存作业定位坐标参数")
    @PutMapping("updateLayout")
    public Result<List<Map<String, String>>> updateLayout(@RequestBody @ApiParam(value = "保存信息") UpdateLayoutDTO updateLayoutDTO) {
        Result<List<Map<String, String>>> updatedLayout = flinkJobService.updateLayout(updateLayoutDTO.getId(), updateLayoutDTO.getJobName(), updateLayoutDTO.getLayout());
        return updatedLayout;
    }

    /**
     * 查询作业定位坐标参数
     *
     * @param id 作业的ID（数据库主键）
     * @return 返回查询结果，包含作业ID、作业名称及定位坐标参数
     */
    @ApiOperation(value = "查询作业定位坐标参数")
    @GetMapping("queryLayout")
    public Result<List<Map<String, String>>> queryLayout(@RequestParam("id") @ApiParam(value = "id", example = "1234") long id) {
        Result<List<Map<String, String>>> layoutResult = flinkJobService.queryLayout(id);
        return layoutResult;
    }

    /**
     * 运行作业任务
     *
     * @param runJobDTO 包含作业名称、作业ID和运行参数的DTO对象
     * @return 返回提交作业的结果，包含作业ID、作业名称及状态
     */
    @ApiOperation(value = "运行作业任务")
    @PostMapping("/run")
    public Result<List<Map<String, String>>> runJob(@RequestBody @ApiParam(value = "作业信息") RunJobDTO runJobDTO) {
        Result<List<Map<String, String>>> submitResult = flinkJobService.runJob(runJobDTO.getJobName(), runJobDTO.getId(), runJobDTO.getArgues());
        return submitResult;
    }

    /**
     * 停止作业任务
     *
     * @param cancelJobDTO 包含作业ID、作业名称和数据库主键ID的DTO对象
     * @return 返回停止作业的结果，包含作业ID、作业名称及状态
     */
    @ApiOperation(value = "停止作业任务")
    @PostMapping("/cancel")
    public Result<List<Map<String, String>>> cancelJob(@RequestBody @ApiParam(value = "作业取消信息") CancelJobDTO cancelJobDTO) {
        Result<List<Map<String, String>>> cancelResult = flinkJobService.cancelJob(cancelJobDTO.getJobId(), cancelJobDTO.getJobName(), cancelJobDTO.getId());
        return cancelResult;
    }

    /**
     * 获取记录的字段和类型
     *
     * @param functionType 作业的功能类型
     * @return 返回字段和类型的查询结果，包含字段名称、字段类型及描述
     */
    @ApiOperation(value = "获取记录字段和类型")
    @GetMapping("queryFields")
    public Result<List<Map<String, String>>> getFields(@RequestParam("functionType") @ApiParam(value = "functionType", example = "DatalogDataStreamSource") String functionType) {
        Result<List<Map<String, String>>> result = flinkJobService.getFields(functionType);
        return result;
    }

    /**
     * 获取模型属性定义
     *
     * @param modelLabel 模型标识
     * @return 返回字段和类型的查询结果，包含字段名称、字段类型及描述
     */
    @ApiOperation(value = "获取模型属性定义")
    @GetMapping("/source/queryModelFields")
    public ApiResult<List<FieldDefinition>> getModelPropertyList(@RequestParam("modelLabel") @ApiParam(value = "modelLabel", example = "quantityaggregationdata") String modelLabel) throws IOException {
        return flinkJobService.getModelPropertyList(modelLabel);
    }

    /**
     * 查询所有的模型标签
     *
     * @return 返回模型标签列表的查询结果，包含模型列表及状态
     */
    @ApiOperation(value = "查询所有业务模型配置")
    @GetMapping("queryModels")
    public Result<List<QueryConfigSqlEntity>> getModels() {
        return flinkJobService.getModels();
    }

    /**
     * 根据枚举类的名称查询所有的枚举值
     *
     * @param enumClassName 枚举类的名称（字符串）
     * @return 返回指定枚举类型的查询结果，包含枚举类型及唯一标识
     */
    @ApiOperation(value = "查询枚举类型")
    @GetMapping("queryEnumerations")
    public Result<?> getEnumerations(@RequestParam("enumClassName") @ApiParam(value = "enumClassName", example = "RuleStatus") String enumClassName) {
        Result<?> result = flinkJobService.getEnumerations(enumClassName);
        return result;
    }

    /**
     * 获取两个表连接后的字段列表
     *
     * @param joinedFieldsDTO 包含连接信息的DTO对象
     * @return 返回字段合并结果，包含合并后的字段列表及状态
     */
    @ApiOperation(value = "查询join合并后字段数据")
    @PostMapping("queryJoinedFields")
    public Result<List<Map<String, String>>> getJoinedFields(@RequestBody @ApiParam(value = "合并后的字段数据") JoinedFieldsDTO joinedFieldsDTO) {
        Result<List<Map<String, String>>> result = flinkJobService.getJoinedFields(joinedFieldsDTO.getTableA(), joinedFieldsDTO.getTableB(), joinedFieldsDTO.getAssociationCondition(), joinedFieldsDTO.getId());
        return result;
    }

    /**
     * 获取分组汇总的字段列表
     *
     * @param groupedFieldsDTO 包含连接信息的DTO对象
     * @return 返回字段分组汇总后结果，包含合并后的字段列表及状态
     */
    @ApiOperation(value = "查询分组汇总后字段数据")
    @PostMapping("queryGroupedFields")
    public Result<List<Map<String, String>>> getGroupedFields(@RequestBody @ApiParam(value = "分组汇总后的字段数据") GroupedFieldsDTO groupedFieldsDTO) {
        Result<List<Map<String, String>>> result = flinkJobService.getGroupedFields(groupedFieldsDTO.getJoinTable(), groupedFieldsDTO.getFunc(), groupedFieldsDTO.getGroupBy(), groupedFieldsDTO.getPeriodField(), groupedFieldsDTO.getId());
        return result;
    }

    /**
     * 导出流程文件
     *
     * @param Ids      包含导出流程id的列表
     * @param response HTTP响应对象
     * @return 返回导出结果，包含文件下载链接及状态
     */
    @ApiOperation(value = "导出流程文件")
    @PostMapping("exportRulesFile")
    public void exportRulesFile(@RequestBody @ApiParam(value = "导出流程id") List<Long> Ids, HttpServletResponse response) {
        flinkJobService.exportRulesFile(Ids, response);
    }

    /**
     * 导入流程文件
     * 并处理每条规则，执行插入操作
     *
     * @param rulesFile 包含规则信息的MultipartFile文件对象
     * @return Result对象，包含处理结果
     */
    @ApiOperation(value = "导入流程文件")
    @PostMapping("importRulesFile")
    public Result<Void> importRulesFile(@RequestParam("rulesFile") MultipartFile rulesFile) {
        Result<Void> result = flinkJobService.importRulesFile(rulesFile);
        return result;
    }

    /**
     * 获取所有物理模型
     *
     * @return Result对象，包含所有物理模型数据的列表
     */
    @ApiOperation(value = "获取所有物理模型")
    @GetMapping("getAllPhysicalModels")
    public ApiResult<List<Model>> getAllPhysicalModels() {
        return flinkJobService.getAllPhysicalModels();
    }

    /**
     * 获取特定物理模型的详细数据
     *
     * @param modelLabel 模型标签
     * @return Result对象，包含特定物理模型的详细数据
     */
    @ApiOperation(value = "获取特定物理模型的详细数据")
    @GetMapping("getPhysicalModelDetails")
    public Result<Object> getPhysicalModelDetails(@RequestParam("modelLabel") String modelLabel) {
        return flinkJobService.getPhysicalModelDetails(modelLabel);
    }

    /**
     * 获取指定目录下所有文件的内容
     *
     * @param getFileDTO 包含作业名称、文件ID、分页信息等
     * @return Result对象，包含文件内容列表
     */
    @ApiOperation(value = "获取指定目录下所有文件的内容")
    @PostMapping("getFile")
    public Result<List<Map<String, String>>> getFile(@RequestBody getFileDTO getFileDTO) {
        Result<List<Map<String, String>>> result = flinkJobService.getFile(getFileDTO.getJobName(), getFileDTO.getId(), getFileDTO.getIndex(), getFileDTO.getLimit());
        return result;
    }

    /**
     * 检查模型映射关系
     *
     * @param checkModelAssociationDTO 包含模型映射关系的DTO
     * @return Result对象，包含检查结果的布尔值
     */
    @ApiOperation(value = "检查模型映射关系")
    @PostMapping("checkModelAssociation")
    public Result<Boolean> checkModelAssociation(@RequestBody CheckModelAssociationDTO checkModelAssociationDTO) {
        Result<Boolean> result = flinkJobService.checkModelAssociation(checkModelAssociationDTO.getModelAssociation());
        return result;
    }

    /**
     * 校验字段公式正确性
     *
     * @param validateFieldFormulaDTO 包含字段公式的DTO
     * @return Result对象，包含校验结果的布尔值
     */
    @ApiOperation(value = "校验字段公式正确性")
    @PostMapping("validateFieldFormula")
    public Result<Boolean> validateFieldFormula(@RequestBody ValidateFieldFormulaDTO validateFieldFormulaDTO) {
        // 调用service层方法校验字段公式
        Result<Boolean> result = flinkJobService.validateFieldFormula(validateFieldFormulaDTO.getTableArray());
        return result;
    }

    /**
     * 获取Redis元数据信息
     *
     * @param modelLabel 模型标签
     * @return Result对象，包含Redis元数据信息
     */
    @ApiOperation(value = "获取Redis元数据信息")
    @GetMapping("getRedisMeta")
    public Result<List<Map<String, String>>> getRedisMeta(@RequestParam("modelLabel") String modelLabel) {
        // 调用service层方法获取Redis元数据
        Result<List<Map<String, String>>> result = flinkJobService.getRedisMeta(modelLabel);
        return result;
    }

    /**
     * 获取业务场景数据
     *
     * @return Result对象，包含业务场景数据列表
     */
    @ApiOperation(value = "获取业务场景数据")
    @GetMapping("getBusinessScenarios")
    public Result<List<Map<String, Object>>> getBusinessScenarios() {
        // 调用service层方法获取业务场景数据
        Result<List<Map<String, Object>>> result = flinkJobService.getBusinessScenarios();
        return result;
    }

    /**
     * 获取业务场景所需的必填参数
     *
     * @param id 业务场景ID
     * @return Result对象，包含业务场景的必填参数
     */
    @ApiOperation(value = "获取业务场景必填参数")
    @GetMapping("getRequiredParameters")
    public Result<List<Map<String, Object>>> getRequiredParameters(@RequestParam("id") int id) {
        // 获取指定业务场景的必填参数
        Result<List<Map<String, Object>>> result = flinkJobService.getRequiredParameters(id);
        return result;
    }

    /**
     * 获取业务场景所需的选填参数
     *
     * @param id 业务场景ID
     * @return Result对象，包含业务场景的选填参数
     */
    @ApiOperation(value = "获取业务场景选填参数")
    @GetMapping("getOptionalParameters")
    public Result<List<Map<String, Object>>> getOptionalParameters(@RequestParam("id") int id) {
        // 获取指定业务场景的选填参数
        Result<List<Map<String, Object>>> result = flinkJobService.getOptionalParameters(id);
        return result;
    }

    /**
     * 获取异常处理后的字段列表
     *
     * @param processedFieldsDTO 包含异常处理信息的DTO对象
     * @return 返回字段处理后结果，包含处理后后的字段列表及状态
     */
    @ApiOperation(value = "取异常处理后的字段列表")
    @PostMapping("queryProcessedFields")
    public Result<List<Map<String, String>>> getProcessedFields(@RequestBody @ApiParam(value = "处理后的字段数据") ProcessedFieldsDTO processedFieldsDTO) {
        Result<List<Map<String, String>>> result = flinkJobService.getProcessedFields(processedFieldsDTO);
        return result;
    }

}