package cet.flinkjobservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "DTO for updating a job rule")
public class UpdateRuleDTO {

    @ApiModelProperty(value = "作业名称", example = "MyFlinkJob", required = true)
    private String jobName;

    @ApiModelProperty(value = "作业别名", example = "flink1", required = true)
    private String alias;

    @ApiModelProperty(value = "数据库主键ID", example = "1234", required = true)
    private long id;
}
