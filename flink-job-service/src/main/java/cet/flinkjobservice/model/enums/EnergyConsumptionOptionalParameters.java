package cet.flinkjobservice.model.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EnergyConsumptionOptionalParameters {
    private double turnOverEng;
    private Double ratedPower;
    private boolean bFixedEmpirical;
    private double maxDifEng;
    private double maxMultiEng;
    private double ignoreDifEng;
    private double limitDifEng;
    private int cacheDtlogNum;
    private int dtlogNum4CheckEngDown;
    private boolean inRange;
    private boolean fallback;
    private boolean difValLimit;
    private boolean euclideanDistance;

    // 构造函数
    public EnergyConsumptionOptionalParameters() {
        // 设置默认值
        this.turnOverEng = 999999999.0;
        this.ratedPower = null;
        this.bFixedEmpirical = false;
        this.maxDifEng = 500.0;
        this.maxMultiEng = 10.0;
        this.ignoreDifEng = 1.0;
        this.limitDifEng = 100000.0;
        this.cacheDtlogNum = 288;
        this.dtlogNum4CheckEngDown = 12;
        this.inRange = true;
        this.fallback = true;
        this.difValLimit = true;
        this.euclideanDistance = true;
    }

    /**
     * 将参数转换为List<Map<String, Object>>
     * @return 包含所有参数的List<Map<String, Object>>
     */
    public List<Map<String, Object>> getOptionalParameters() {
        List<Map<String, Object>> paramsList = new ArrayList<>();
        paramsList.add(createParamMap("turnOverEng", "翻转值", turnOverEng, "double", "number"));
        paramsList.add(createParamMap("ratedPower", "额定功率", ratedPower, "double", "number"));
        paramsList.add(createParamMap("bFixedEmpirical", "是否固定经验值", bFixedEmpirical, "boolean", "boolean"));
        paramsList.add(createParamMap("maxDifEng", "经验值", maxDifEng, "double", "number"));
        paramsList.add(createParamMap("maxMultiEng", "欧式距离倍数", maxMultiEng, "double", "number"));
        paramsList.add(createParamMap("ignoreDifEng", "忽略经验值", ignoreDifEng, "double", "number"));
        paramsList.add(createParamMap("limitDifEng", "两点间最大差值", limitDifEng, "double", "number"));
        paramsList.add(createParamMap("cacheDtlogNum", "异常判断缓存个数", cacheDtlogNum, "int", "number"));
        paramsList.add(createParamMap("dtlogNum4CheckEngDown", "重算时后续下降点判断个数", dtlogNum4CheckEngDown, "int", "number"));
        paramsList.add(createParamMap("inRange", "范围检查", inRange, "boolean", "boolean"));
        paramsList.add(createParamMap("fallback", "回退处理", fallback, "boolean", "boolean"));
        paramsList.add(createParamMap("difValLimit", "差值限制检查", difValLimit, "boolean", "boolean"));
        paramsList.add(createParamMap("euclideanDistance", "欧几里得距离检查", euclideanDistance, "boolean", "boolean"));
        return paramsList;
    }

    /**
     * 创建参数Map
     * @param field 字段名
     * @param description 字段描述
     * @param defaultValue 默认值
     * @param fieldType 字段类型
     * @param frontedType 前端类型
     * @return 包含字段信息的Map
     */
    private Map<String, Object> createParamMap(String field, String description, Object defaultValue, String fieldType, String frontedType) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("field", field);
        paramMap.put("description", description);
        paramMap.put("defaultValue", defaultValue);
        paramMap.put("fieldType", fieldType);
        paramMap.put("frontedType", frontedType);
        return paramMap;
    }
}
